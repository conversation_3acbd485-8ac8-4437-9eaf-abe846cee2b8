"""
性能监控和状态管理相关的数据模型
"""

import time
from typing import Dict, Any, Tuple, List, Optional
from dataclasses import dataclass, field
from utils.logger import logger


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    total_time: float = 0.0
    reasoning_time: float = 0.0
    action_time: float = 0.0
    tool_execution_time: float = 0.0
    observation_time: float = 0.0
    llm_calls: int = 0
    tool_calls: int = 0
    successful_tools: int = 0
    failed_tools: int = 0
    
    def calculate_efficiency_score(self) -> float:
        """计算效率分数"""
        if self.tool_calls == 0:
            return 0.0
        
        success_rate = self.successful_tools / self.tool_calls
        time_efficiency = min(1.0, 30.0 / max(self.total_time, 0.1))  # 30秒内完成得高分
        
        return (success_rate * 0.7 + time_efficiency * 0.3) * 100
    
    def generate_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        if self.total_time > 60:
            recommendations.append("总执行时间过长，建议减少工具调用或优化工具性能")
        
        if self.tool_calls > 0:
            success_rate = self.successful_tools / self.tool_calls
            if success_rate < 0.8:
                recommendations.append("工具成功率较低，建议检查工具参数或添加错误恢复机制")
        
        if self.reasoning_time > self.tool_execution_time * 2:
            recommendations.append("推理时间过长，建议优化提示词或减少推理复杂度")
        
        if len(recommendations) == 0:
            recommendations.append("性能表现良好，无需特别优化")
        
        return recommendations


@dataclass
class ReActState:
    """ReAct状态管理数据类"""
    query: str = ""
    available_tools: List = field(default_factory=list)
    tools_description: str = ""
    conversation_history: List[Tuple[str, str]] = field(default_factory=list)
    tool_results: List[Dict[str, Any]] = field(default_factory=list)
    current_iteration: int = 0
    max_iterations: int = 5
    exit_reason: Optional[str] = None
    performance_metrics: PerformanceMetrics = field(default_factory=PerformanceMetrics)
    
    def add_reasoning(self, reasoning: str):
        """添加推理步骤"""
        self.conversation_history.append(("reasoning", reasoning))
        self.performance_metrics.llm_calls += 1
    
    def add_action(self, action: str):
        """添加行动决策"""
        self.conversation_history.append(("action", action))
        self.performance_metrics.llm_calls += 1
    
    def add_observation(self, observation: str, tool_result: Dict[str, Any]):
        """添加观察结果"""
        self.conversation_history.append(("observation", observation))
        self.tool_results.append(tool_result)
        self.performance_metrics.tool_calls += 1
        
        if tool_result.get("status") == "success":
            self.performance_metrics.successful_tools += 1
        else:
            self.performance_metrics.failed_tools += 1
    
    def get_recent_history(self, max_steps: int = 6) -> str:
        """获取最近的对话历史"""
        recent_history = ""
        for step_type, content in self.conversation_history[-max_steps:]:
            truncated_content = content[:150] + "..." if len(content) > 150 else content
            recent_history += f"\n{step_type.upper()}: {truncated_content}"
        return recent_history
    
    def is_complete(self) -> bool:
        """判断任务是否完成"""
        return (self.exit_reason is not None or 
                self.current_iteration >= self.max_iterations)
