"""
工具调用器模块

提供ReAct模式和标准模式的工具调用功能
"""

import re
import json
import time
from typing import Dict, Any, Tu<PERSON>, List

from apis.core.llm_invoker import LLMInvoker
from apis.core.prompt_manager import PromptTemplateManager
from apis.core.error_recovery import ErrorRecoveryManager
from apis.models.performance_models import ReActState
from utils.logger import logger

from langchain_core.messages import ToolMessage


class ReActToolCaller:
    """ReAct模式工具调用器：实现Reasoning-Acting-Observation循环（优化版）"""
    
    def __init__(self, llm_invoker: LLMInvoker, max_iterations: int = 5, 
                 enable_error_recovery: bool = True, enable_performance_monitoring: bool = True):
        self.llm_invoker = llm_invoker
        self.max_iterations = max_iterations
        self.enable_error_recovery = enable_error_recovery
        self.enable_performance_monitoring = enable_performance_monitoring
        
        # 初始化优化组件
        self.prompt_manager = PromptTemplateManager()
        self.error_recovery = ErrorRecoveryManager() if enable_error_recovery else None
        
        logger.info(f"🧠 ReActToolCaller初始化完成")
        logger.info(f"   - 最大迭代次数: {max_iterations}")
        logger.info(f"   - 错误恢复: {'启用' if enable_error_recovery else '禁用'}")
        logger.info(f"   - 性能监控: {'启用' if enable_performance_monitoring else '禁用'}")
    
    def call_tools(self, tools: List, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        ReAct模式工具调用实现（优化版）
        
        Args:
            tools: 工具列表
            query: 用户查询
            context: 额外的上下文信息，会传递给工具执行
            
        Returns:
            包含执行结果的字典
        """
        logger.info("🧠 开始ReAct模式工具调用（优化版）...")
        
        # 记录上下文信息
        if context:
            logger.info(f"📋 接收到上下文信息: {list(context.keys())}")
        
        # 初始化状态和性能监控
        start_time = time.time()
        tool_map, tools_desc = self._build_tool_descriptions(tools)
        
        # 使用新的状态管理
        state = ReActState(
            query=query,
            available_tools=tools,
            tools_description=tools_desc,
            max_iterations=self.max_iterations
        )
        
        # 将 context 保存到 state 中，以便后续使用
        state.context = context or {}
        
        # 重置错误恢复计数
        if self.error_recovery:
            self.error_recovery.reset_retries()
        
        # 初始推理
        logger.info(f"\n{'='*50}")
        logger.info(f"🤔 迭代 {state.current_iteration + 1}: 初始推理")
        logger.info(f"{'='*50}")
        
        reasoning_start = time.time()
        try:
            reasoning_prompt = self.prompt_manager.build_prompt(
                "initial_reasoning",
                tools_description=tools_desc,
                query=query
            )
            
            reasoning_response = self.llm_invoker.invoke_with_prompt_info(
                reasoning_prompt, f"ReAct推理-迭代{state.current_iteration + 1}"
            )
            
            if self.enable_performance_monitoring:
                state.performance_metrics.reasoning_time += time.time() - reasoning_start
            
            state.add_reasoning(reasoning_response)
            logger.info(f"💭 推理结果: {reasoning_response[:200]}..." if len(reasoning_response) > 200 else f"💭 推理结果: {reasoning_response}")
            
        except Exception as e:
            return self._build_error_result(query, f"初始推理失败: {str(e)}", state)
        
        # ReAct主循环
        while not state.is_complete():
            state.current_iteration += 1
            logger.info(f"\n{'='*60}")
            logger.info(f"🔄 ReAct迭代 {state.current_iteration}/{state.max_iterations}")
            logger.info(f"📊 当前状态: {len(state.tool_results)} 个工具已执行, {state.performance_metrics.successful_tools} 个成功")
            logger.info(f"{'='*60}")
            
            # Acting: 确定下一步行动
            logger.info(f"🎯 步骤1: 分析并决定下一步行动...")
            action_start = time.time()
            
            try:
                action_prompt = self.prompt_manager.build_prompt(
                    "action_decision",
                    query=query,
                    tools_description=tools_desc,
                    recent_history=state.get_recent_history(),
                    current_iteration=state.current_iteration,
                    max_iterations=state.max_iterations
                )
                
                action_response = self.llm_invoker.invoke_with_prompt_info(
                    action_prompt, f"ReAct行动决策-迭代{state.current_iteration}"
                )
                
                if self.enable_performance_monitoring:
                    state.performance_metrics.action_time += time.time() - action_start
                
                state.add_action(action_response)
                logger.info(f"📝 行动决策: {action_response[:200]}..." if len(action_response) > 200 else f"📝 行动决策: {action_response}")
                
            except Exception as e:
                logger.info(f"❌ 迭代 {state.current_iteration} 行动决策失败: {str(e)}")
                state.exit_reason = f"action_failed_iteration_{state.current_iteration}"
                break
            
            # 解析行动决策
            action_decision = self._parse_action_decision(action_response)
            
            if action_decision.get("action_type") == "FINISH":
                logger.info(f"✅ Agent在第 {state.current_iteration} 次迭代中决定完成任务")
                logger.info(f"🎯 完成原因: {action_decision.get('reason', '未提供原因')}")
                state.exit_reason = f"agent_finished_iteration_{state.current_iteration}"
                break
                
            elif action_decision.get("action_type") == "USE_TOOL":
                tool_name = action_decision.get("tool_name", "unknown")
                logger.info(f"🔧 步骤2: 执行工具 '{tool_name}'...")
                logger.info(f"💡 执行原因: {action_decision.get('reason', '未提供原因')}")
                
                # 执行工具调用（带错误恢复和上下文）
                tool_start = time.time()
                tool_result = self._execute_tool_with_recovery(action_decision, tool_map, state.current_iteration, state.context)
                
                if self.enable_performance_monitoring:
                    state.performance_metrics.tool_execution_time += time.time() - tool_start
                
                # 显示工具执行结果
                if tool_result.get("status") == "success":
                    logger.info(f"✅ 工具 '{tool_name}' 执行成功")
                else:
                    logger.info(f"❌ 工具 '{tool_name}' 执行失败: {tool_result.get('error', '未知错误')}")
                
                # Observation: 观察工具结果
                logger.info(f"👁️ 步骤3: 观察工具执行结果...")
                observation_start = time.time()
                observation = self._build_observation(tool_result)
                
                if self.enable_performance_monitoring:
                    state.performance_metrics.observation_time += time.time() - observation_start
                
                state.add_observation(observation, tool_result)
                logger.info(f"📋 观察结果: {observation[:300]}..." if len(observation) > 300 else f"📋 观察结果: {observation}")
                
                # 继续推理
                if state.current_iteration < state.max_iterations:
                    logger.info(f"🤔 步骤4: 基于观察结果继续推理...")
                    reasoning_start = time.time()
                    
                    try:
                        next_reasoning_prompt = self.prompt_manager.build_prompt(
                            "continue_reasoning",
                            query=query,
                            recent_history=state.get_recent_history(),
                            successful_tools=state.performance_metrics.successful_tools,
                            total_tools=state.performance_metrics.tool_calls
                        )
                        
                        next_reasoning = self.llm_invoker.invoke_with_prompt_info(
                            next_reasoning_prompt, f"ReAct持续推理-迭代{state.current_iteration}"
                        )
                        
                        if self.enable_performance_monitoring:
                            state.performance_metrics.reasoning_time += time.time() - reasoning_start
                        
                        state.add_reasoning(next_reasoning)
                        logger.info(f"💭 推理结果: {next_reasoning[:300]}..." if len(next_reasoning) > 300 else f"💭 推理结果: {next_reasoning}")
                        
                    except Exception as e:
                        logger.info(f"⚠️ 迭代 {state.current_iteration} 持续推理失败: {str(e)}")
                        state.exit_reason = f"reasoning_failed_iteration_{state.current_iteration}"
                        break
                else:
                    logger.info(f"⚠️ 已达到最大迭代次数 {state.max_iterations}，将在此次迭代后结束")
                    state.exit_reason = f"max_iterations_reached"
                
                logger.info(f"✅ 第 {state.current_iteration} 次迭代完成")
                
            else:
                logger.info(f"⚠️ 迭代 {state.current_iteration}: 无法理解的行动决策类型 '{action_decision.get('action_type', 'unknown')}'")
                state.exit_reason = f"unknown_action_iteration_{state.current_iteration}"
                break
        
        # 检查是否因为达到最大迭代次数而退出
        if state.current_iteration >= state.max_iterations and not state.exit_reason:
            state.exit_reason = "max_iterations_reached"
        
        # 完成性能监控
        if self.enable_performance_monitoring:
            state.performance_metrics.total_time = time.time() - start_time
        
        # 显示退出信息和性能统计
        self._display_completion_summary(state)
        
        # 生成最终总结
        final_summary = self._generate_final_summary_optimized(state)
        
        # 构建返回结果
        result = {
            "messages": [query] + [msg for _, msg in state.conversation_history],
            "tool_results": state.tool_results,
            "conversation_history": state.conversation_history,
            "iterations": state.current_iteration,
            "max_iterations": state.max_iterations,
            "exit_reason": state.exit_reason,
            "exit_reason_formatted": self._format_exit_reason(state.exit_reason),
            "summary": {
                "total_iterations": state.current_iteration,
                "total_tools_used": state.performance_metrics.tool_calls,
                "successful_tools": state.performance_metrics.successful_tools,
                "failed_tools": state.performance_metrics.failed_tools,
                "completion_reason": "max_iterations" if "max_iterations" in (state.exit_reason or "") else "agent_finished" if "agent_finished" in (state.exit_reason or "") else "error",
                "exit_reason": state.exit_reason
            },
            "final_summary": final_summary
        }
        
        # 添加性能指标
        if self.enable_performance_monitoring:
            result["performance_metrics"] = {
                "total_time": state.performance_metrics.total_time,
                "reasoning_time": state.performance_metrics.reasoning_time,
                "action_time": state.performance_metrics.action_time,
                "tool_execution_time": state.performance_metrics.tool_execution_time,
                "observation_time": state.performance_metrics.observation_time,
                "llm_calls": state.performance_metrics.llm_calls,
                "efficiency_score": state.performance_metrics.calculate_efficiency_score(),
                "recommendations": state.performance_metrics.generate_recommendations()
            }
        
        return result

    def _build_tool_descriptions(self, tools: List) -> Tuple[Dict[str, Any], str]:
        """构建工具描述和映射"""
        tool_descriptions = []
        tool_map = {}

        for tool in tools:
            tool_name = tool.name
            tool_map[tool_name] = tool

            # 获取工具描述
            description = getattr(tool, 'description', tool.__doc__ or "No description available")

            # 获取参数模式
            param_str = self._get_tool_parameters(tool)

            tool_descriptions.append(f"""
Tool: {tool_name}
Description: {description}
Parameters:
{param_str}
""")

        return tool_map, "\n".join(tool_descriptions)

    def _get_tool_parameters(self, tool) -> str:
        """获取工具参数描述"""
        if not hasattr(tool, 'args_schema'):
            return "No parameters"

        schema = tool.args_schema
        params = []

        # 兼容 Pydantic V2
        if hasattr(schema, 'model_fields'):
            # Pydantic V2
            for field_name, field_info in schema.model_fields.items():
                field_type = field_info.annotation if hasattr(field_info, 'annotation') else str(field_info)
                field_desc = field_info.description if hasattr(field_info, 'description') else ""
                params.append(f"- {field_name} ({field_type}): {field_desc}")
        elif hasattr(schema, '__fields__'):
            # Pydantic V1 (向后兼容)
            for field_name, field_info in schema.__fields__.items():
                field_type = field_info.type_ if hasattr(field_info, 'type_') else str(field_info)
                field_desc = field_info.field_info.description if hasattr(field_info, 'field_info') and hasattr(field_info.field_info, 'description') else ""
                params.append(f"- {field_name} ({field_type}): {field_desc}")

        return "\n".join(params) if params else "No parameters"

    def _execute_tool_with_recovery(self, action_decision: Dict[str, Any], tool_map: Dict[str, Any], iteration: int, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行工具调用（带错误恢复）"""
        tool_name = action_decision.get("tool_name", "")
        arguments = action_decision.get("arguments", {})
        reason = action_decision.get("reason", "")
        tool_call_id = f"react_call_{iteration}_{tool_name}"

        if tool_name not in tool_map:
            return {
                "tool": tool_name,
                "tool_call_id": tool_call_id,
                "arguments": arguments,
                "reason": reason,
                "error": f"未找到工具: {tool_name}",
                "status": "not_found"
            }

        # 首次尝试
        try:
            tool = tool_map[tool_name]
            
            # 如果工具支持 context 参数，则传递 context
            if context and hasattr(tool, 'args_schema'):
                # 检查工具是否接受 context 参数
                schema = tool.args_schema
                if hasattr(schema, 'model_fields') and 'context' in schema.model_fields:
                    arguments['context'] = context
                    logger.info(f"🔗 向工具 {tool_name} 传递上下文信息")
                elif hasattr(schema, '__fields__') and 'context' in schema.__fields__:
                    arguments['context'] = context
                    logger.info(f"🔗 向工具 {tool_name} 传递上下文信息")
            
            result = tool.invoke(arguments)

            return {
                "tool": tool_name,
                "tool_call_id": tool_call_id,
                "arguments": arguments,
                "reason": reason,
                "result": result,
                "status": "success"
            }

        except Exception as e:
            # 尝试错误恢复
            if self.error_recovery and self.error_recovery.should_retry(e, tool_name):
                recovery_result = self.error_recovery.handle_error(e, tool_name, arguments)

                if recovery_result.get("status") == "retry":
                    logger.info(f"🔄 尝试恢复工具 {tool_name} 的执行...")
                    corrected_args = recovery_result.get("corrected_arguments", arguments)

                    try:
                        result = tool.invoke(corrected_args)
                        logger.info(f"✅ 工具 {tool_name} 恢复成功")
                        return {
                            "tool": tool_name,
                            "tool_call_id": tool_call_id,
                            "arguments": corrected_args,
                            "reason": reason,
                            "result": result,
                            "status": "success",
                            "recovery_attempted": True,
                            "original_error": str(e)
                        }
                    except Exception as retry_error:
                        logger.info(f"❌ 工具 {tool_name} 恢复失败: {str(retry_error)}")
                        return {
                            "tool": tool_name,
                            "tool_call_id": tool_call_id,
                            "arguments": corrected_args,
                            "reason": reason,
                            "error": str(retry_error),
                            "status": "failed",
                            "recovery_attempted": True,
                            "original_error": str(e)
                        }

            # 无法恢复或不尝试恢复
            return {
                "tool": tool_name,
                "tool_call_id": tool_call_id,
                "arguments": arguments,
                "reason": reason,
                "error": str(e),
                "status": "failed",
                "recovery_attempted": False
            }

    def _display_completion_summary(self, state: ReActState):
        """显示完成总结"""
        logger.info(f"\n{'='*60}")
        logger.info(f"🏁 ReAct执行结束（优化版）")
        logger.info(f"{'='*60}")
        logger.info(f"📊 执行统计:")
        logger.info(f"   - 总迭代次数: {state.current_iteration}/{state.max_iterations}")
        logger.info(f"   - 工具执行总数: {state.performance_metrics.tool_calls}")
        logger.info(f"   - 成功工具数: {state.performance_metrics.successful_tools}")
        logger.info(f"   - 失败工具数: {state.performance_metrics.failed_tools}")
        logger.info(f"🚪 退出原因: {self._format_exit_reason(state.exit_reason)}")

        if self.enable_performance_monitoring:
            logger.info(f"\n⚡ 性能指标:")
            logger.info(f"   - 总执行时间: {state.performance_metrics.total_time:.2f}秒")
            logger.info(f"   - 推理时间: {state.performance_metrics.reasoning_time:.2f}秒")
            logger.info(f"   - 工具执行时间: {state.performance_metrics.tool_execution_time:.2f}秒")
            logger.info(f"   - LLM调用次数: {state.performance_metrics.llm_calls}")
            logger.info(f"   - 效率分数: {state.performance_metrics.calculate_efficiency_score():.1f}/100")

            recommendations = state.performance_metrics.generate_recommendations()
            if recommendations:
                logger.info(f"\n💡 优化建议:")
                for i, rec in enumerate(recommendations, 1):
                    logger.info(f"   {i}. {rec}")

        logger.info(f"{'='*60}")

    def _generate_final_summary_optimized(self, state: ReActState) -> str:
        """生成优化版最终总结"""
        reasoning_count = len([h for h in state.conversation_history if h[0] == 'reasoning'])

        summary = f"""ReAct执行总结（优化版）：
用户查询：{state.query.strip()}

执行统计：
- 总迭代次数：{state.current_iteration}/{state.max_iterations}
- 推理步骤数：{reasoning_count}
- 工具调用次数：{state.performance_metrics.tool_calls}
- 成功工具数：{state.performance_metrics.successful_tools}
- 失败工具数：{state.performance_metrics.failed_tools}
- 成功率：{(state.performance_metrics.successful_tools/state.performance_metrics.tool_calls*100) if state.performance_metrics.tool_calls else 0:.1f}%

退出信息：
- 退出原因：{self._format_exit_reason(state.exit_reason)}
- 是否正常完成：{'是' if 'agent_finished' in (state.exit_reason or '') else '否'}"""

        if self.enable_performance_monitoring:
            summary += f"""

性能指标：
- 总执行时间：{state.performance_metrics.total_time:.2f}秒
- 推理时间：{state.performance_metrics.reasoning_time:.2f}秒
- 工具执行时间：{state.performance_metrics.tool_execution_time:.2f}秒
- 效率分数：{state.performance_metrics.calculate_efficiency_score():.1f}/100"""

        successful_tools = [r for r in state.tool_results if r.get("status") == "success"]
        if successful_tools:
            summary += f"\n\n成功执行的工具："
            for r in successful_tools:
                recovery_info = " (已恢复)" if r.get("recovery_attempted") else ""
                summary += f"\n- {r['tool']}: {r.get('reason', '无原因说明')}{recovery_info}"
        else:
            summary += f"\n\n成功执行的工具：\n- 无"

        failed_tools = [r for r in state.tool_results if r.get("status") == "failed"]
        if failed_tools:
            summary += f"\n\n失败的工具调用："
            for r in failed_tools:
                recovery_info = " (尝试恢复)" if r.get("recovery_attempted") else ""
                summary += f"\n- {r['tool']}: {r.get('error', '无错误信息')}{recovery_info}"

        return summary

    def _build_error_result(self, query: str, error_message: str, state: ReActState = None) -> Dict[str, Any]:
        """构建错误结果（优化版）"""
        if state is None:
            from apis.models.performance_models import ReActState
            state = ReActState(query=query, max_iterations=self.max_iterations)

        return {
            "messages": [query, f"ReAct执行失败: {error_message}"],
            "tool_results": state.tool_results,
            "conversation_history": state.conversation_history,
            "iterations": state.current_iteration,
            "max_iterations": state.max_iterations,
            "exit_reason": "error",
            "exit_reason_formatted": f"执行失败: {error_message}",
            "summary": {
                "total_iterations": state.current_iteration,
                "total_tools_used": state.performance_metrics.tool_calls,
                "successful_tools": state.performance_metrics.successful_tools,
                "failed_tools": state.performance_metrics.failed_tools,
                "completion_reason": "error"
            },
            "final_summary": f"ReAct执行失败: {error_message}",
            "error": error_message
        }

    def _parse_action_decision(self, action_response: str) -> Dict[str, Any]:
        """解析行动决策"""
        action_response = action_response.strip()

        if "ACTION: FINISH" in action_response.upper():
            reason_match = re.search(r'REASON:\s*(.+)', action_response, re.IGNORECASE | re.DOTALL)
            return {
                "action_type": "FINISH",
                "reason": reason_match.group(1).strip() if reason_match else "任务完成"
            }
        elif "ACTION: USE_TOOL" in action_response.upper():
            tool_name_match = re.search(r'TOOL_NAME:\s*(.+)', action_response, re.IGNORECASE)
            arguments_match = re.search(r'ARGUMENTS:\s*(\{.+?\})', action_response, re.IGNORECASE | re.DOTALL)
            reason_match = re.search(r'REASON:\s*(.+)', action_response, re.IGNORECASE | re.DOTALL)

            tool_name = tool_name_match.group(1).strip() if tool_name_match else ""

            arguments = {}
            if arguments_match:
                try:
                    arguments = json.loads(arguments_match.group(1))
                except json.JSONDecodeError:
                    logger.info(f"⚠️ 无法解析参数: {arguments_match.group(1)}")

            return {
                "action_type": "USE_TOOL",
                "tool_name": tool_name,
                "arguments": arguments,
                "reason": reason_match.group(1).strip() if reason_match else "使用工具"
            }
        else:
            return {
                "action_type": "UNKNOWN",
                "reason": "无法解析的行动决策"
            }

    def _build_observation(self, tool_result: Dict[str, Any]) -> str:
        """构建观察结果"""
        tool_name = tool_result.get("tool", "unknown")
        status = tool_result.get("status", "unknown")

        if status == "success":
            result = tool_result.get("result", "")
            # 截断过长的结果
            if isinstance(result, str) and len(result) > 500:
                return f"工具 {tool_name} 执行成功。结果：{result[:500]}...[结果过长，已截断]"
            else:
                return f"工具 {tool_name} 执行成功。结果：{result}"
        elif status == "failed":
            error = tool_result.get("error", "未知错误")
            return f"工具 {tool_name} 执行失败。错误：{error}"
        elif status == "not_found":
            return f"未找到工具 {tool_name}"
        else:
            return f"工具 {tool_name} 执行状态未知"

    def _format_exit_reason(self, exit_reason: str) -> str:
        """格式化退出原因为用户友好的文本"""
        reason_map = {
            "max_iterations_reached": "达到最大迭代次数限制",
            "unknown": "未知原因"
        }

        # 处理包含迭代次数的退出原因
        if "agent_finished_iteration_" in exit_reason:
            iteration_num = exit_reason.split("_")[-1]
            return f"Agent在第 {iteration_num} 次迭代中主动完成任务"
        elif "action_failed_iteration_" in exit_reason:
            iteration_num = exit_reason.split("_")[-1]
            return f"第 {iteration_num} 次迭代中行动决策失败"
        elif "reasoning_failed_iteration_" in exit_reason:
            iteration_num = exit_reason.split("_")[-1]
            return f"第 {iteration_num} 次迭代中推理过程失败"
        elif "unknown_action_iteration_" in exit_reason:
            iteration_num = exit_reason.split("_")[-1]
            return f"第 {iteration_num} 次迭代中遇到无法理解的行动决策"

        return reason_map.get(exit_reason, f"未知退出原因: {exit_reason}")


class ToolCaller:
    """工具调用器"""

    def __init__(self, llm_invoker: LLMInvoker):
        self.llm_invoker = llm_invoker

    def call_tools(self, tools: List, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        自定义工具调用实现，用于替代 React Agent

        Args:
            tools: 工具列表
            query: 用户查询
            context: 额外的上下文信息，会传递给工具执行

        Returns:
            包含执行结果的字典
        """
        logger.info("🔧 开始自定义工具调用...")
        
        # 记录上下文信息
        if context:
            logger.info(f"📋 接收到上下文信息: {list(context.keys())}")

        # 构建工具描述和映射
        tool_map, tools_desc = self._build_tool_descriptions(tools)

        # 构建提示词
        prompt = self._build_tool_calling_prompt(tools_desc, query)

        # 调用 LLM 获取工具调用计划
        try:
            cleaned_response = self.llm_invoker.invoke_with_prompt_info(prompt, "工具调用计划生成")
            logger.info("✅ 工具调用计划生成成功")
        except Exception as e:
            return {
                "messages": [query, f"LLM调用失败: {str(e)}"],
                "tool_results": [],
                "plan": {},
                "error": f"LLM调用失败: {str(e)}"
            }

        # 解析响应
        try:
            plan = self._parse_tool_plan(cleaned_response)
            logger.info(f"📋 解析到 {len(plan.get('tool_calls', []))} 个工具调用")
        except Exception as e:
            logger.info(f"❌ 解析工具调用计划失败: {str(e)}")
            return {
                "messages": [query, f"解析失败: {cleaned_response}"],
                "tool_results": [],
                "plan": {},
                "error": f"解析失败: {str(e)}"
            }

        # 执行工具调用
        return self._execute_tools(plan, tool_map, query, context)

    def _build_tool_descriptions(self, tools: List) -> Tuple[Dict[str, Any], str]:
        """构建工具描述和映射"""
        tool_descriptions = []
        tool_map = {}

        for tool in tools:
            tool_name = tool.name
            tool_map[tool_name] = tool

            # 获取工具描述
            description = getattr(tool, 'description', tool.__doc__ or "No description available")

            # 获取参数模式
            param_str = self._get_tool_parameters(tool)

            tool_descriptions.append(f"""
Tool: {tool_name}
Description: {description}
Parameters:
{param_str}
""")

        return tool_map, "\n".join(tool_descriptions)

    def _get_tool_parameters(self, tool) -> str:
        """获取工具参数描述"""
        if not hasattr(tool, 'args_schema'):
            return "No parameters"

        schema = tool.args_schema
        params = []

        # 兼容 Pydantic V2
        if hasattr(schema, 'model_fields'):
            # Pydantic V2
            for field_name, field_info in schema.model_fields.items():
                field_type = field_info.annotation if hasattr(field_info, 'annotation') else str(field_info)
                field_desc = field_info.description if hasattr(field_info, 'description') else ""
                params.append(f"- {field_name} ({field_type}): {field_desc}")
        elif hasattr(schema, '__fields__'):
            # Pydantic V1 (向后兼容)
            for field_name, field_info in schema.__fields__.items():
                field_type = field_info.type_ if hasattr(field_info, 'type_') else str(field_info)
                field_desc = field_info.field_info.description if hasattr(field_info, 'field_info') and hasattr(field_info.field_info, 'description') else ""
                params.append(f"- {field_name} ({field_type}): {field_desc}")

        return "\n".join(params) if params else "No parameters"

    def _get_required_parameters(self, tool) -> List[str]:
        """获取工具的必须参数列表"""
        if not hasattr(tool, 'args_schema'):
            return []
        
        schema = tool.args_schema
        required_params = []
        
        # 兼容 Pydantic V2
        if hasattr(schema, 'model_fields'):
            # Pydantic V2
            for field_name, field_info in schema.model_fields.items():
                # 检查是否为必须参数
                if hasattr(field_info, 'is_required') and field_info.is_required():
                    required_params.append(field_name)
                elif hasattr(field_info, 'default') and field_info.default is ...:  # ... 表示无默认值
                    required_params.append(field_name)
                elif not hasattr(field_info, 'default'):
                    required_params.append(field_name)
        elif hasattr(schema, '__fields__'):
            # Pydantic V1 (向后兼容)
            for field_name, field_info in schema.__fields__.items():
                if field_info.is_required():
                    required_params.append(field_name)
        
        return required_params

    def _generate_tool_signature(self, tool_name: str, arguments: Dict[str, Any], tool_map: Dict[str, Any]) -> str:
        """生成工具调用的唯一签名（基于工具名称和必须参数）"""
        if tool_name not in tool_map:
            return f"{tool_name}:unknown"
        
        tool = tool_map[tool_name]
        required_params = self._get_required_parameters(tool)
        
        # 构建签名：工具名称 + 必须参数的键值对（排序后）
        signature_parts = [tool_name]
        
        for param in sorted(required_params):
            if param in arguments:
                # 将参数值转换为字符串并标准化
                param_value = json.dumps(arguments[param], sort_keys=True, ensure_ascii=False)
                signature_parts.append(f"{param}:{param_value}")
        
        return "|".join(signature_parts)

    def _deduplicate_tool_calls(self, tool_calls: List[Dict[str, Any]], tool_map: Dict[str, Any]) -> List[Dict[str, Any]]:
        """去重工具调用：相同工具名称和必须参数的调用保留参数最丰富的那个"""
        if not tool_calls:
            return tool_calls
        
        # 使用字典存储每个签名对应的最佳工具调用（参数最丰富的）
        signature_to_best_call = {}
        signature_to_call_info = {}
        removed_calls = []
        
        for i, tool_call in enumerate(tool_calls):
            tool_name = tool_call.get("tool_name", "")
            arguments = tool_call.get("arguments", {})
            
            # 生成工具调用签名
            signature = self._generate_tool_signature(tool_name, arguments, tool_map)
            param_count = len(arguments)
            
            if signature not in signature_to_best_call:
                # 第一次遇到这个签名，保存
                signature_to_best_call[signature] = tool_call
                signature_to_call_info[signature] = {
                    "index": i + 1,
                    "param_count": param_count,
                    "tool_name": tool_name,
                    "arguments": arguments
                }
                logger.info(f"✅ 首次遇到工具调用 #{i+1}: {tool_name} (签名: {signature}, 参数数: {param_count})")
            else:
                # 已经遇到过这个签名，比较参数数量
                existing_info = signature_to_call_info[signature]
                existing_param_count = existing_info["param_count"]
                
                if param_count > existing_param_count:
                    # 新的调用参数更丰富，替换之前的
                    old_call = signature_to_best_call[signature]
                    signature_to_best_call[signature] = tool_call
                    
                    # 记录被替换的调用
                    removed_calls.append({
                        "index": existing_info["index"],
                        "tool_name": existing_info["tool_name"],
                        "signature": signature,
                        "arguments": existing_info["arguments"],
                        "reason": f"参数较少({existing_param_count}个)，被参数更丰富的调用替换({param_count}个)"
                    })
                    
                    # 更新信息
                    signature_to_call_info[signature] = {
                        "index": i + 1,
                        "param_count": param_count,
                        "tool_name": tool_name,
                        "arguments": arguments
                    }
                    
                    logger.info(f"🔄 替换工具调用: #{existing_info['index']} (参数{existing_param_count}个) -> #{i+1} (参数{param_count}个): {tool_name}")
                else:
                    # 新的调用参数较少或相等，删除新的调用
                    removed_calls.append({
                        "index": i + 1,
                        "tool_name": tool_name,
                        "signature": signature,
                        "arguments": arguments,
                        "reason": f"参数较少或相等({param_count}个)，已有更丰富的调用({existing_param_count}个)"
                    })
                    logger.info(f"🗑️ 删除工具调用 #{i+1}: {tool_name} (参数{param_count}个，已有更丰富的调用{existing_param_count}个)")
        
        # 构建最终的去重后调用列表
        deduplicated_calls = list(signature_to_best_call.values())
        
        # 记录去重统计信息
        if removed_calls:
            logger.info(f"📊 工具调用去重统计:")
            logger.info(f"   - 原始调用数: {len(tool_calls)}")
            logger.info(f"   - 去重后调用数: {len(deduplicated_calls)}")
            logger.info(f"   - 删除重复调用数: {len(removed_calls)}")
            logger.info(f"   - 删除的调用详情:")
            for removed in removed_calls:
                logger.info(f"     * #{removed['index']}: {removed['tool_name']} - {removed['reason']}")
        
        return deduplicated_calls

    def _build_tool_calling_prompt(self, tools_desc: str, query: str) -> str:
        """构建工具调用提示词"""
        return f"""你是一个智能助手，可以使用以下工具来帮助用户：

{tools_desc}

用户查询：{query}

请分析用户查询，并按以下步骤操作：

1. 分析需要使用哪些工具
2. 以JSON格式输出工具调用计划，格式如下：
{{
    "tool_calls": [
        {{
            "tool_name": "工具名称",
            "arguments": {{参数对象}}
        }},
        ...
    ]
}}

注意：
- 每个工具调用都要包含完整的参数
- 参数要符合工具的要求
- 只输出JSON，不要有其他内容
"""

    def _parse_tool_plan(self, response: str) -> Dict[str, Any]:
        """解析工具调用计划"""
        # 提取 JSON
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json.loads(json_match.group(0))
        else:
            return json.loads(response)

    def _execute_tools(self, plan: Dict[str, Any], tool_map: Dict[str, Any], query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行工具调用"""
        results = []
        messages = [query]
        successful_tools = 0
        failed_tools = 0

        if "tool_calls" not in plan or not plan["tool_calls"]:
            logger.info("⚠️ 没有找到工具调用计划")
            return {
                "messages": [query, "没有生成工具调用计划"],
                "tool_results": [],
                "plan": plan,
                "error": "没有工具调用计划"
            }

        # 工程化检查：去重相同必须参数的工具调用
        logger.info("🔧 开始工具调用去重检查...")
        original_count = len(plan["tool_calls"])
        plan["tool_calls"] = self._deduplicate_tool_calls(plan["tool_calls"], tool_map)
        deduplicated_count = len(plan["tool_calls"])
        
        if original_count != deduplicated_count:
            logger.info(f"🔧 工具调用去重完成: 原始 {original_count} 个，去重后 {deduplicated_count} 个，删除了 {original_count - deduplicated_count} 个重复调用")
        else:
            logger.info(f"✅ 工具调用去重检查完成: 没有发现重复调用")

        for i, tool_call in enumerate(plan["tool_calls"]):
            tool_name = tool_call.get("tool_name")
            arguments = tool_call.get("arguments", {})
            tool_call_id = f"call_{i+1}_{tool_name}"

            result = self._execute_single_tool(tool_name, arguments, tool_call_id, tool_map, context)
            results.append(result)

            # 创建 ToolMessage
            if result["status"] == "success":
                message_content = json.dumps(result["result"], ensure_ascii=False)
                successful_tools += 1
                logger.info(f"✅ 工具 {tool_name} 执行成功")
            else:
                message_content = result.get("error", f"工具执行失败: {tool_name}")
                failed_tools += 1

            tool_message = ToolMessage(
                content=message_content,
                name=tool_name if result["status"] == "success" else "system",
                tool_call_id=tool_call_id
            )
            messages.append(tool_message)

        # 汇总执行结果
        logger.info(f"📊 工具执行总结: 成功 {successful_tools} 个，失败 {failed_tools} 个")

        return {
            "messages": messages,
            "tool_results": results,
            "plan": plan,
            "summary": {
                "total_tools": len(plan["tool_calls"]),
                "successful_tools": successful_tools,
                "failed_tools": failed_tools,
                "success_rate": successful_tools / len(plan["tool_calls"]) * 100 if plan["tool_calls"] else 0,
                "original_tool_count": original_count,
                "deduplicated_tool_count": deduplicated_count,
                "removed_duplicates": original_count - deduplicated_count
            }
        }

    def _execute_single_tool(self, tool_name: str, arguments: Dict[str, Any],
                           tool_call_id: str, tool_map: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行单个工具"""
        if tool_name not in tool_map:
            logger.info(f"❌ 未找到工具: {tool_name}")
            return {
                "tool": tool_name,
                "tool_call_id": tool_call_id,
                "arguments": arguments,
                "error": f"未找到工具: {tool_name}",
                "status": "not_found"
            }

        try:
            logger.info(f"🔧 执行工具: {tool_name} (ID: {tool_call_id})")
            logger.info(f"📝 参数: {arguments}")

            tool = tool_map[tool_name]
            
            # 如果工具支持 context 参数，则传递 context
            if context and hasattr(tool, 'args_schema'):
                # 检查工具是否接受 context 参数
                schema = tool.args_schema
                if hasattr(schema, 'model_fields') and 'context' in schema.model_fields:
                    arguments['context'] = context
                    logger.info(f"🔗 向工具 {tool_name} 传递上下文信息")
                elif hasattr(schema, '__fields__') and 'context' in schema.__fields__:
                    arguments['context'] = context
                    logger.info(f"🔗 向工具 {tool_name} 传递上下文信息")
                logger.info(f"🔗 传递后的参数: {arguments.keys()}")
            
            result = tool.invoke(arguments)

            return {
                "tool": tool_name,
                "tool_call_id": tool_call_id,
                "arguments": arguments,
                "result": result,
                "status": "success"
            }

        except Exception as e:
            logger.info(f"❌ 执行工具 {tool_name} 失败: {str(e)}")
            return {
                "tool": tool_name,
                "tool_call_id": tool_call_id,
                "arguments": arguments,
                "error": str(e),
                "status": "failed"
            }
