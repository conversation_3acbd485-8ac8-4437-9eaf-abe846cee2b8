"""
前端资源验证模块

提供前端资源识别、提取和验证功能
"""

import subprocess
from typing import List, Dict, Any, Optional
from utils.logger import logger


class FrontendResourceValidator:
    """前端资源验证器"""
    
    def __init__(self, frontend_extensions: List[str] = None):
        """
        初始化前端资源验证器
        
        Args:
            frontend_extensions: 前端资源文件扩展名列表
        """
        self.frontend_extensions = frontend_extensions or [
            '.js', '.css', '.less', '.scss', '.styl', '.style', '.mjs', '.ts', '.jsx',
            '.pdf', '.txt', '.json', '.xml', '.csv', '.mp3', '.mp4', '.wav',
            '.png', '.jpg', '.jpeg', '.gif', '.webp', '.avif', '.svg', '.ico'
        ]
    
    def extract_frontend_resources(self, log_data: Any) -> List[str]:
        """
        从日志数据中提取前端资源URL
        
        Args:
            log_data: 日志数据
            
        Returns:
            前端资源URL列表
        """
        resources = []
        
        def extract_from_node(node):
            if isinstance(node, dict):
                # 检查request字段
                request = node.get('request', '')
                if request and self._is_frontend_resource(request):
                    # 提取资源地址（去除查询参数）
                    clean_url = self._extract_resource_url(request)
                    if clean_url and clean_url not in resources:
                        resources.append(clean_url)
                
                # 递归处理children
                children = node.get('children')
                if children and isinstance(children, list):
                    for child in children:
                        extract_from_node(child)
            elif isinstance(node, list):
                for item in node:
                    extract_from_node(item)
        
        extract_from_node(log_data)
        return resources
    
    def validate_resources_with_size(self, resource_urls: List[str]) -> List[Dict[str, Any]]:
        """
        验证资源并获取文件大小信息
        
        Args:
            resource_urls: 资源URL列表
            
        Returns:
            包含资源信息的字典列表
        """
        resources_with_size = []
        
        for resource_url in resource_urls:
            resource_info = self._get_resource_info(resource_url)
            if resource_info:
                resources_with_size.append(resource_info)
                logger.info(f"📁 资源: {resource_info['filename']} ({resource_info['size_bytes']} bytes)")
        
        return resources_with_size
    
    def _is_frontend_resource(self, request: str) -> bool:
        """
        判断请求是否为前端资源
        
        Args:
            request: 请求字符串
            
        Returns:
            是否为前端资源
        """
        if not request:
            return False
        
        # 转换为小写进行比较
        request_lower = request.lower()
        
        # 检查是否包含前端资源扩展名
        for ext in self.frontend_extensions:
            if ext.lower() in request_lower:
                return True
        
        return False
    
    def _extract_resource_url(self, request: str) -> str:
        """
        从请求中提取资源地址，去除查询参数
        
        Args:
            request: 原始请求字符串
            
        Returns:
            清理后的资源URL
        """
        if not request:
            return ""
        
        try:
            # 如果包含查询参数，去除它们
            if '?' in request:
                base_url = request.split('?')[0]
            else:
                base_url = request
            
            return base_url.strip()
        except Exception:
            return request.strip()
    
    def _get_resource_info(self, resource_url: str) -> Optional[Dict[str, Any]]:
        """
        获取资源文件信息（文件名和大小）
        
        Args:
            resource_url: 资源URL
            
        Returns:
            资源信息字典，如果获取失败则返回None
        """
        try:
            # 提取文件名
            filename = resource_url.split('/')[-1] if '/' in resource_url else resource_url
            
            # 使用curl获取文件大小
            cmd = f"curl -sI '{resource_url}' | grep -i 'Content-Length' | awk '{{print $2}}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    size_bytes = int(result.stdout.strip().replace('\r', '').replace('\n', ''))
                    return {
                        'url': resource_url,
                        'filename': filename,
                        'size_bytes': size_bytes,
                        'size_kb': round(size_bytes / 1024, 2),
                        'size_mb': round(size_bytes / (1024 * 1024), 2)
                    }
                except ValueError:
                    logger.info(f"⚠️ 无法解析文件大小: {resource_url}")
                    return None
            else:
                logger.info(f"⚠️ 无法获取文件大小: {resource_url}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.info(f"⚠️ 获取文件大小超时: {resource_url}")
            return None
        except Exception as e:
            logger.info(f"⚠️ 获取文件信息失败: {resource_url}, 错误: {str(e)}")
            return None
