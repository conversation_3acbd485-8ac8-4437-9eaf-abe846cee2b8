"""
日志预处理API模块

提供日志数据的获取、过滤和处理功能，支持多种数据源和过滤策略
"""

import json
import requests
from typing import Dict, Any, Optional, Protocol, Union
from pathlib import Path
from abc import ABC, abstractmethod
from utils.logger import logger


class DataSource(Protocol):
    """数据源协议接口"""
    
    def fetch_data(self, traceid: str, time: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取数据"""
        ...


class LogFilter(ABC):
    """日志过滤器抽象基类"""
    
    @abstractmethod
    def filter(self, log_data: Dict[str, Any]) -> str:
        """过滤日志数据"""
        ...


class APIDataSource:
    """API数据源实现"""
    
    def __init__(self, base_url: str, headers: Dict[str, str], timeout: int = 10):
        self.base_url = base_url
        self.headers = headers
        self.timeout = timeout
    
    def fetch_data(self, traceid: str, time: str, **kwargs) -> Optional[Dict[str, Any]]:
        """从API获取日志数据"""
        url = self.base_url
        data = {
            "time": time,
            "traceid": traceid,
            "isQueryIngress": kwargs.get("is_query_ingress", False)
        }
        
        try:
            logger.info(f"🔄 正在从API获取日志数据: traceid={traceid}, time={time}")
            response = requests.post(url, headers=self.headers, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            json_data = response.json()
            return self._validate_response(json_data)
            
        except requests.exceptions.Timeout:
            logger.info("❌ API请求超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.info("❌ 无法连接到API服务器")
            return None
        except requests.exceptions.HTTPError as e:
            logger.info(f"❌ HTTP请求失败: {e.response.status_code} - {e.response.reason}")
            return None
        except json.JSONDecodeError:
            logger.info("❌ API返回的数据不是有效的JSON格式")
            return None
        except Exception as e:
            logger.info(f"❌ 从API获取日志数据失败: {str(e)}")
            return None
    
    def _validate_response(self, json_data: Any) -> Optional[Dict[str, Any]]:
        """验证API响应数据"""
        if not json_data:
            logger.info("⚠️ API返回空数据")
            return None
            
        if not isinstance(json_data, dict):
            logger.info("⚠️ API返回的数据类型不是字典")
            return None
        
        # 检查错误响应
        if self._is_error_response(json_data):
            return None
        
        # 检查是否包含有效数据
        if self._has_valid_data(json_data):
            data_size = len(str(json_data))
            logger.info(f"✅ 成功从API获取日志数据，数据大小: {data_size} 字符")
            return json_data
        
        logger.info("⚠️ API返回的数据不包含有效的日志内容")
        return None
    
    def _is_error_response(self, json_data: Dict[str, Any]) -> bool:
        """检查是否是错误响应"""
        # 检查success字段
        if 'success' in json_data and json_data['success'] is False:
            error_msg = json_data.get('message', '未知错误')
            error_code = json_data.get('code', '未知代码')
            logger.info(f"❌ API返回错误: {error_code} - {error_msg}")
            return True
        
        # 检查5xx错误码
        if 'code' in json_data and str(json_data['code']).startswith('5'):
            error_msg = json_data.get('message', '服务器错误')
            logger.info(f"❌ API服务器错误: {json_data['code']} - {error_msg}")
            return True
        
        # 检查error字段
        if 'error' in json_data:
            error_msg = json_data.get('error', '未知错误')
            logger.info(f"❌ API返回错误: {error_msg}")
            return True
        
        return False
    
    def _has_valid_data(self, json_data: Dict[str, Any]) -> bool:
        """检查是否包含有效数据"""
        # 检查data字段
        if 'data' in json_data:
            data_content = json_data['data']
            if data_content and len(data_content) > 0:
                logger.info(f"✅ 检测到data字段，包含 {len(data_content)} 条记录")
                return True
            else:
                logger.info("⚠️ API返回的data字段为空")
                return False
        
        # 检查其他日志相关字段
        log_fields = ['logs', 'traces', 'entries', 'results']
        if any(key in json_data for key in log_fields):
            return True
        
        # 检查是否有足够的非状态字段
        basic_fields = {'code', 'message', 'success', 'error', 'status'}
        if len(json_data) > 3 and not set(json_data.keys()).issubset(basic_fields):
            return True
        
        return False


class LocalFileDataSource:
    """本地文件数据源实现"""
    
    def fetch_data(self, traceid: str, time: str, file_path: str = None, **kwargs) -> Optional[Dict[str, Any]]:
        """从本地文件加载日志数据"""
        if not file_path:
            # 默认使用traceid构建文件路径
            file_path = f"log_{traceid}.json"
        
        try:
            logger.info(f"📁 正在从本地文件加载数据: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"✅ 成功从本地文件加载数据")
            return data
        except FileNotFoundError:
            logger.info(f"❌ 文件不存在: {file_path}")
            return None
        except json.JSONDecodeError:
            logger.info(f"❌ 文件不是有效的JSON格式: {file_path}")
            return None
        except Exception as e:
            logger.info(f"❌ 从本地文件加载数据失败: {str(e)}")
            return None


class ProblemNodeFilter(LogFilter):
    """问题节点过滤器：找到每个问题分支最深处的问题节点"""

    def __init__(self, problem_levels: list = None):
        self.problem_levels = problem_levels or ['缓慢', '异常']

    def filter(self, log_data: Dict[str, Any]) -> str:
        """
        根据新需求重构的过滤器：
        1. 找到每个问题分支最深处的问题节点
        2. 对缓慢分支进行子节点验证和更新
        3. 排除client或gateway类型的分支
        4. 返回一维列表形式的目标节点信息
        """

        # 提取原始数据
        if 'data' in log_data and isinstance(log_data['data'], list):
            raw_data = log_data['data']
        elif 'data' in log_data and isinstance(log_data['data'], dict):
            raw_data = log_data['data']
        else:
            raw_data = log_data

        logger.info("🔍 开始新的问题分支分析...")

        # 步骤1: 构建树形结构（如果是扁平数据）
        if isinstance(raw_data, list) and any('pSpanId' in item for item in raw_data if isinstance(item, dict)):
            logger.info("检测到扁平列表，重建树形结构...")
            tree_data = self._rebuild_tree_structure_from_flat(raw_data)
        else:
            tree_data = raw_data

        # 步骤2: 找到所有问题分支的最深处问题节点
        deepest_problem_nodes = self._find_deepest_problem_nodes_in_branches(tree_data)

        if not deepest_problem_nodes:
            logger.info("未找到任何问题分支")
            return ""

        logger.info(f"✅ 找到 {len(deepest_problem_nodes)} 个问题分支的最深处节点")

        # 步骤3: 对缓慢分支进行子节点验证和更新
        self._process_slow_branches_children(deepest_problem_nodes, tree_data)

        # 步骤4: 过滤掉client或gateway类型的分支
        valid_nodes = self._filter_valid_problem_nodes(deepest_problem_nodes)

        if not valid_nodes:
            logger.info("所有问题分支都被过滤掉，返回空结果")
            return ""

        logger.info(f"✅ 最终保留 {len(valid_nodes)} 个有效的问题节点")

        # 步骤5: 返回一维列表形式的结果
        result = json.dumps(valid_nodes, ensure_ascii=False, indent=2)
        return result
    def _rebuild_tree_structure_from_flat(self, flat_data: list) -> list:
        """将扁平的节点列表重建为树形结构"""
        # 创建节点映射，用于快速查找
        node_map = {}
        for node in flat_data:
            if isinstance(node, dict) and 'spanId' in node:
                node_map[node['spanId']] = node.copy()
                # 初始化children字段
                node_map[node['spanId']]['children'] = []

        # 构建树形结构
        root_nodes = []
        for span_id, node in node_map.items():
            parent_span_id = node.get('pSpanId')

            if parent_span_id in node_map:
                # 将当前节点添加到父节点的children中
                node_map[parent_span_id]['children'].append(node)
            else:
                # 如果找不到父节点，则视为根节点
                root_nodes.append(node)

        # 清理空的children列表
        for node in node_map.values():
            if not node['children']:
                node['children'] = None

        logger.info(f"🔄 从扁平列表重建树形结构: {len(flat_data)} 个节点 -> {len(root_nodes)} 个根节点")
        return root_nodes

    def _find_deepest_problem_nodes_in_branches(self, tree_data: Any) -> list:
        """
        找到每个问题分支最深处的问题节点
        对于同一个分支同时存在"缓慢"和"异常"两种情况时分别提取
        """
        deepest_nodes = []

        def find_deepest_in_branch(node, current_path=None, problem_nodes_in_path=None):
            """递归查找分支中最深的问题节点"""
            if current_path is None:
                current_path = []
            if problem_nodes_in_path is None:
                problem_nodes_in_path = {'缓慢': [], '异常': []}

            if not isinstance(node, dict):
                return

            # 将当前节点添加到路径中
            new_path = current_path + [node]
            new_problem_nodes = problem_nodes_in_path.copy()

            # 检查当前节点是否是问题节点
            level = str(node.get('level', '')).strip()
            if level in self.problem_levels:
                # 将问题节点添加到对应类型的列表中
                if level == '缓慢':
                    new_problem_nodes['缓慢'] = new_problem_nodes['缓慢'] + [node]
                elif level == '异常':
                    new_problem_nodes['异常'] = new_problem_nodes['异常'] + [node]

            # 检查子节点
            children = node.get('children')
            has_children = children and isinstance(children, list) and len(children) > 0

            if not has_children:
                # 这是叶子节点，检查路径中是否有问题节点
                for problem_type, nodes_list in new_problem_nodes.items():
                    if nodes_list:
                        # 取最深的问题节点（列表中最后一个）
                        deepest_node = nodes_list[-1]
                        deepest_nodes.append({
                            'node': deepest_node,
                            'type': problem_type,
                            'path': new_path,
                            'depth': len(new_path)
                        })
                        logger.info(f"🎯 找到{problem_type}分支最深处节点: spanId={deepest_node.get('spanId')}, 深度={len(new_path)}")
            else:
                # 继续递归处理子节点
                for child in children:
                    if child is not None:
                        find_deepest_in_branch(child, new_path, new_problem_nodes)

        # 处理所有根节点
        if isinstance(tree_data, list):
            for root in tree_data:
                find_deepest_in_branch(root)
        elif isinstance(tree_data, dict):
            find_deepest_in_branch(tree_data)

        return deepest_nodes

    def _process_slow_branches_children(self, deepest_nodes: list, tree_data: Any):
        """
        对缓慢分支进行子节点验证和更新
        在找到每个分支最深处的"缓慢"类型节点时，继续向下验证如果该"缓慢"节点的下层耗时
        与本"缓慢"节点的耗时相差15%以内，那么将该下层节点的level字段更新为"缓慢"
        """
        logger.info("🔍 开始对缓慢分支进行子节点验证...")

        slow_nodes = [item for item in deepest_nodes if item['type'] == '缓慢']

        if not slow_nodes:
            logger.info("未找到缓慢类型的分支")
            return

        logger.info(f"✅ 找到 {len(slow_nodes)} 个缓慢分支需要处理")

        for slow_item in slow_nodes:
            slow_node = slow_item['node']
            self._verify_and_update_children_level(slow_node)

    def _verify_and_update_children_level(self, slow_node: dict):
        """验证并更新缓慢节点的子节点level"""
        parent_duration = self._get_node_duration(slow_node)

        if parent_duration <= 0:
            logger.info(f"⚠️ 缓慢节点duration无效: {slow_node.get('spanId')}, duration={parent_duration}")
            return

        logger.info(f"🔍 验证缓慢节点 {slow_node.get('spanId')} 的子节点 (duration: {parent_duration}ms)")

        children = slow_node.get('children')
        if not children or not isinstance(children, list):
            logger.info(f"缓慢节点 {slow_node.get('spanId')} 没有子节点")
            return

        for child in children:
            if isinstance(child, dict):
                self._recursive_verify_child_duration(child, parent_duration)

    def _recursive_verify_child_duration(self, child_node: dict, parent_duration: float):
        """递归验证子节点的duration并更新level"""
        child_duration = self._get_node_duration(child_node)

        if child_duration <= 0:
            logger.info(f"⚠️ 子节点duration无效: {child_node.get('spanId')}, duration={child_duration}")
            return

        # 计算耗时差异百分比
        duration_diff = abs(parent_duration - child_duration) / parent_duration * 100

        if duration_diff <= 15:  # 15%以内
            # 更新子节点的level为"缓慢"
            original_level = child_node.get('level', '')
            child_node['level'] = '缓慢'
            logger.info(f"✅ 更新子节点level: {child_node.get('spanId')}, {original_level} -> 缓慢, duration={child_duration}ms, diff={duration_diff:.1f}%")
        else:
            logger.info(f"⏭️ 子节点耗时差异过大: {child_node.get('spanId')}, parent_duration={parent_duration}ms, child_duration={child_duration}ms, diff={duration_diff:.1f}% > 15%")

        # 继续递归处理子节点的子节点
        children = child_node.get('children')
        if children and isinstance(children, list):
            for grandchild in children:
                if isinstance(grandchild, dict):
                    self._recursive_verify_child_duration(grandchild, parent_duration)

    def _get_node_duration(self, node: dict) -> float:
        """获取节点的duration值"""
        try:
            duration = node.get('duration', 0)
            return float(duration) if duration is not None else 0.0
        except (ValueError, TypeError):
            return 0.0

    def _filter_valid_problem_nodes(self, deepest_nodes: list) -> list:
        """
        过滤掉client或gateway类型的分支
        如果当前"问题"分支最深处的节点类型属于client或gateway，那么放弃该分支的查找
        """
        valid_nodes = []

        for item in deepest_nodes:
            node = item['node']
            htype = node.get('htype', '').lower()

            if htype in ['client', 'gateway']:
                logger.info(f"⏭️ 过滤掉{htype}类型分支: spanId={node.get('spanId')}")
            else:
                valid_nodes.append(node)
                logger.info(f"✅ 保留有效分支: spanId={node.get('spanId')}, htype={htype}, level={node.get('level')}")

        return valid_nodes


class LogPreprocessor:
    """日志预处理器主类"""
    
    def __init__(self):
        self._data_sources = {}
        self._filters = {}
        self._setup_default_sources()
        self._setup_default_filters()
    
    def _setup_default_sources(self):
        """设置默认数据源"""
        # API数据源配置
        api_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'gray;': '',
            'lang': 'zh-cn',
            'origin': 'https://iwork.faw.cn',
            'priority': 'u=1, i',
            'qfc-user-para': '{"systemId":"BA-0214","appCode":"BA-0214_APP_DBD"}',
            'qfcsid': 'BA-0214',
            'qfctid': 'YQJT',
            'referer': 'https://iwork.faw.cn/',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'sw8': '1-NDQ5NzA3NTk0ZDAzYTQ4YQ==-MDAwMA==-0-V0stMDAwMV9BUFBfSVdL-aHR0cHM6Ly9uZXdhcHBzLWZjLW1vbml0b3IuZmF3LmNuLw==-L3dhdGNoLXNjcmVlbi1uZXd3ZWIvcmVxdWVzdExpc3Q=-aXdvcmsuZmF3LmNu',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'useragent': 'pc',
            'x-traceid': '449707594d03a48a'
        }
        
        api_url = 'https://newapps-fc-monitor.faw.cn/api-dev/watch-clientsearch-server/getyondata/allbytraceid'
        
        self.register_data_source('api', APIDataSource(api_url, api_headers))
        self.register_data_source('local', LocalFileDataSource())
    
    def _setup_default_filters(self):
        """设置默认过滤器"""
        self.register_filter('problem_nodes', ProblemNodeFilter())
    
    def register_data_source(self, name: str, source: DataSource):
        """注册数据源"""
        self._data_sources[name] = source
    
    def register_filter(self, name: str, filter_obj: LogFilter):
        """注册过滤器"""
        self._filters[name] = filter_obj
    
    def fetch_log_data(self, traceid: str, time: str = "2025-05-06 11:30:00", 
                      authorization: str = None, source_priority: list = None) -> Optional[Dict[str, Any]]:
        """
        按优先级获取日志数据
        
        Args:
            traceid: 追踪ID
            time: 日志时间
            authorization: 授权token（API数据源需要）
            source_priority: 数据源优先级列表，默认为['api', 'local']
        
        Returns:
            日志数据或None
        """
        if source_priority is None:
            source_priority = ['api', 'local']
        
        for source_name in source_priority:
            if source_name not in self._data_sources:
                logger.info(f"⚠️ 未找到数据源: {source_name}")
                continue
            
            source = self._data_sources[source_name]
            
            # 为API数据源添加authorization
            if source_name == 'api' and authorization:
                if hasattr(source, 'headers'):
                    source.headers['authorization'] = authorization
            
            # 为本地数据源提供文件路径
            kwargs = {}
            if source_name == 'local':
                # 尝试多种本地文件路径
                potential_paths = [
                    f"log_{traceid}.json",
                    str(Path(__file__).parent.parent / f"log_{traceid}.json")
                ]
                
                for path in potential_paths:
                    if Path(path).exists():
                        kwargs['file_path'] = path
                        break
            
            data = source.fetch_data(traceid, time, **kwargs)
            if data is not None:
                return data
        
        return None
    
    def filter_log_data(self, log_data: Dict[str, Any], filter_name: str = 'problem_nodes') -> str:
        """
        过滤日志数据
        
        Args:
            log_data: 原始日志数据
            filter_name: 过滤器名称
        
        Returns:
            过滤后的日志内容字符串
        """
        if filter_name not in self._filters:
            raise ValueError(f"未找到过滤器: {filter_name}")
        
        filter_obj = self._filters[filter_name]
        return filter_obj.filter(log_data)

