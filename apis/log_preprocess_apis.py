"""
日志预处理API模块

提供日志数据的获取、过滤和处理功能，支持多种数据源和过滤策略
"""

import json
import requests
from typing import Dict, Any, Optional, Protocol, Union
from pathlib import Path
from abc import ABC, abstractmethod
from utils.logger import logger


class DataSource(Protocol):
    """数据源协议接口"""
    
    def fetch_data(self, traceid: str, time: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取数据"""
        ...


class LogFilter(ABC):
    """日志过滤器抽象基类"""
    
    @abstractmethod
    def filter(self, log_data: Dict[str, Any]) -> str:
        """过滤日志数据"""
        ...


class APIDataSource:
    """API数据源实现"""
    
    def __init__(self, base_url: str, headers: Dict[str, str], timeout: int = 10):
        self.base_url = base_url
        self.headers = headers
        self.timeout = timeout
    
    def fetch_data(self, traceid: str, time: str, **kwargs) -> Optional[Dict[str, Any]]:
        """从API获取日志数据"""
        url = self.base_url
        data = {
            "time": time,
            "traceid": traceid,
            "isQueryIngress": kwargs.get("is_query_ingress", False)
        }
        
        try:
            logger.info(f"🔄 正在从API获取日志数据: traceid={traceid}, time={time}")
            response = requests.post(url, headers=self.headers, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            json_data = response.json()
            return self._validate_response(json_data)
            
        except requests.exceptions.Timeout:
            logger.info("❌ API请求超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.info("❌ 无法连接到API服务器")
            return None
        except requests.exceptions.HTTPError as e:
            logger.info(f"❌ HTTP请求失败: {e.response.status_code} - {e.response.reason}")
            return None
        except json.JSONDecodeError:
            logger.info("❌ API返回的数据不是有效的JSON格式")
            return None
        except Exception as e:
            logger.info(f"❌ 从API获取日志数据失败: {str(e)}")
            return None
    
    def _validate_response(self, json_data: Any) -> Optional[Dict[str, Any]]:
        """验证API响应数据"""
        if not json_data:
            logger.info("⚠️ API返回空数据")
            return None
            
        if not isinstance(json_data, dict):
            logger.info("⚠️ API返回的数据类型不是字典")
            return None
        
        # 检查错误响应
        if self._is_error_response(json_data):
            return None
        
        # 检查是否包含有效数据
        if self._has_valid_data(json_data):
            data_size = len(str(json_data))
            logger.info(f"✅ 成功从API获取日志数据，数据大小: {data_size} 字符")
            return json_data
        
        logger.info("⚠️ API返回的数据不包含有效的日志内容")
        return None
    
    def _is_error_response(self, json_data: Dict[str, Any]) -> bool:
        """检查是否是错误响应"""
        # 检查success字段
        if 'success' in json_data and json_data['success'] is False:
            error_msg = json_data.get('message', '未知错误')
            error_code = json_data.get('code', '未知代码')
            logger.info(f"❌ API返回错误: {error_code} - {error_msg}")
            return True
        
        # 检查5xx错误码
        if 'code' in json_data and str(json_data['code']).startswith('5'):
            error_msg = json_data.get('message', '服务器错误')
            logger.info(f"❌ API服务器错误: {json_data['code']} - {error_msg}")
            return True
        
        # 检查error字段
        if 'error' in json_data:
            error_msg = json_data.get('error', '未知错误')
            logger.info(f"❌ API返回错误: {error_msg}")
            return True
        
        return False
    
    def _has_valid_data(self, json_data: Dict[str, Any]) -> bool:
        """检查是否包含有效数据"""
        # 检查data字段
        if 'data' in json_data:
            data_content = json_data['data']
            if data_content and len(data_content) > 0:
                logger.info(f"✅ 检测到data字段，包含 {len(data_content)} 条记录")
                return True
            else:
                logger.info("⚠️ API返回的data字段为空")
                return False
        
        # 检查其他日志相关字段
        log_fields = ['logs', 'traces', 'entries', 'results']
        if any(key in json_data for key in log_fields):
            return True
        
        # 检查是否有足够的非状态字段
        basic_fields = {'code', 'message', 'success', 'error', 'status'}
        if len(json_data) > 3 and not set(json_data.keys()).issubset(basic_fields):
            return True
        
        return False


class LocalFileDataSource:
    """本地文件数据源实现"""
    
    def fetch_data(self, traceid: str, time: str, file_path: str = None, **kwargs) -> Optional[Dict[str, Any]]:
        """从本地文件加载日志数据"""
        if not file_path:
            # 默认使用traceid构建文件路径
            file_path = f"log_{traceid}.json"
        
        try:
            logger.info(f"📁 正在从本地文件加载数据: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"✅ 成功从本地文件加载数据")
            return data
        except FileNotFoundError:
            logger.info(f"❌ 文件不存在: {file_path}")
            return None
        except json.JSONDecodeError:
            logger.info(f"❌ 文件不是有效的JSON格式: {file_path}")
            return None
        except Exception as e:
            logger.info(f"❌ 从本地文件加载数据失败: {str(e)}")
            return None


class ProblemNodeFilter(LogFilter):
    """问题节点过滤器：筛选level为"缓慢"或"异常"的节点，智能提取问题分支的关键节点"""
    
    def __init__(self, problem_levels: list = None, max_length: int = 10000):
        self.problem_levels = problem_levels or ['缓慢', '异常']
        self.max_length = max_length
    
    def filter(self, log_data: Dict[str, Any]) -> str:
        """智能筛选问题分支，保留所有问题节点及其路径"""

        if 'data' in log_data and isinstance(log_data['data'], list):
            raw_data = log_data['data']
        elif 'data' in log_data and isinstance(log_data['data'], dict): # 兼容 data 是一个对象的情况
            raw_data = log_data['data']
        else:
            raw_data = log_data

        # 将原始数据保存在实例中，以便后续使用
        self.current_original_data = raw_data

        # 新增步骤：处理"缓慢"节点的子节点验证
        logger.info("🔍 开始处理缓慢节点的子节点验证...")
        self._process_slow_node_children(raw_data)

        # 第一步：找到所有问题节点
        problem_nodes = self._find_all_problem_nodes(raw_data)

        if not problem_nodes:
            logger.info("未找到任何问题节点")
            return ""

        logger.info(f"✅ 找到 {len(problem_nodes)} 个问题节点")
        
        # 第二步：基于问题节点重建树形结构
        # 创建问题节点的 spanId 集合，用于快速查找
        problem_node_map = {}
        for node in problem_nodes:
            span_id = node.get('spanId')
            if span_id is not None: # 接受空字符串 "" 但不接受 null/None
                # 使用 span_id 作为 key
                problem_node_map[span_id] = node
            else:
                # 如果 spanId 是 null，我们创建一个基于对象ID的临时key
                # 这确保了即使没有spanId，这个节点也能在map中被找到
                temp_key = f"temp_id_{id(node)}"
                node['__temp_id__'] = temp_key # 给节点打上临时标记
                problem_node_map[temp_key] = node

        # 注意：这里的 self.current_original_data 可能是扁平的，需要先建树
        # 我们复用您代码中已有的建树逻辑
        if isinstance(self.current_original_data, list) and any('pSpanId' in item for item in self.current_original_data if isinstance(item, dict)):
            logger.info("检测到扁平列表，先重建树...")
            tree_data = self._rebuild_tree_structure_from_flat(self.current_original_data)
        else:
            tree_data = self.current_original_data

        tree_structure = self._filter_tree_by_problem_nodes(tree_data, problem_node_map)
        
        if tree_structure:
            initial_result = json.dumps(tree_structure, ensure_ascii=False, indent=2)
            
            # 检查长度限制 (这部分逻辑可以保留)
            if len(initial_result) > self.max_length:
                logger.info(f"⚠️ 过滤结果长度 {len(initial_result)} 超过限制 {self.max_length}，进一步精简")
                simplified_structure = self._further_simplify_tree_structure(tree_structure)
                if simplified_structure:
                    final_result = json.dumps(simplified_structure, ensure_ascii=False, indent=2)
                    logger.info(f"✅ 进一步精简后，长度: {len(final_result)}")
                    return final_result
                else:
                    logger.info("⚠️ 无法进一步精简，返回原始结果")
                    return initial_result
            else:
                return initial_result
        else:
            logger.info("⚠️ 重建树形结构失败")
            return ""
        
    def _find_all_problem_nodes(self, data: Any) -> list:
        """递归遍历数据，找到所有level为'缓慢'或'异常'的节点"""
        problem_nodes = []
        
        def traverse_and_collect(node):
            if not isinstance(node, dict):
                return
            
            # 检查当前节点是否是问题节点
            node_level = str(node.get('level', '')).strip()
            if node_level in self.problem_levels:
                # 完整复制节点信息
                problem_nodes.append(node.copy())

            # 递归处理子节点
            children = node.get('children')
            if children and isinstance(children, list):
                for child in children:
                    traverse_and_collect(child)

        # 兼容输入是列表或单个字典的情况
        if isinstance(data, list):
            for item in data:
                traverse_and_collect(item)
        elif isinstance(data, dict):
            traverse_and_collect(data)
            
        return problem_nodes

    def _process_slow_node_children(self, data: Any):
        """
        处理"缓慢"节点的子节点验证：
        在找到每个分支最深处的"缓慢"类型节点时，继续向下验证如果该"缓慢"节点的下层耗时
        与本"缓慢"节点的耗时相差15%以内，那么将该下层节点的level字段更新为"缓慢"
        """
        logger.info("🔍 开始处理缓慢节点的子节点验证...")

        # 检查数据格式，如果是扁平列表，需要特殊处理
        if isinstance(data, list) and any(isinstance(item, dict) and 'pSpanId' in item for item in data if item is not None):
            # 扁平列表格式，需要在原始数据上进行更新
            self._process_slow_node_children_in_flat_data(data)
        else:
            # 树形结构，直接处理
            deepest_slow_nodes = self._find_deepest_slow_nodes_in_branches(data)

            if not deepest_slow_nodes:
                logger.info("未找到任何分支的最深处缓慢节点")
                return

            logger.info(f"✅ 找到 {len(deepest_slow_nodes)} 个分支的最深处缓慢节点")

            # 对每个最深处的缓慢节点，验证其子节点
            for slow_node in deepest_slow_nodes:
                self._verify_and_update_children_level(slow_node)

    def _process_slow_node_children_in_flat_data(self, flat_data: list):
        """在扁平列表数据中处理缓慢节点的子节点验证"""
        logger.info("检测到扁平列表格式，在原始数据上进行缓慢节点处理")

        # 创建节点映射表
        node_map = {item.get('spanId', ''): item for item in flat_data if isinstance(item, dict) and 'spanId' in item}

        # 先重建树形结构来找到最深处的缓慢节点
        tree_data = self._rebuild_tree_structure_from_flat(flat_data)
        if not tree_data:
            logger.info("⚠️ 无法重建树形结构")
            return

        # 直接在扁平数据中找到所有缓慢节点，然后确定哪些是最深处的
        slow_nodes_in_flat = []
        for node in flat_data:
            if isinstance(node, dict) and str(node.get('level', '')).strip() == '缓慢':
                slow_nodes_in_flat.append(node)

        if not slow_nodes_in_flat:
            logger.info("未找到任何缓慢节点")
            return

        # 对于每个缓慢节点，检查它是否是其分支中最深的缓慢节点
        deepest_slow_nodes_in_flat = []
        for slow_node in slow_nodes_in_flat:
            span_id = slow_node.get('spanId')
            is_deepest = True

            # 检查是否有子节点也是缓慢的
            for potential_child in flat_data:
                if (isinstance(potential_child, dict) and
                    potential_child.get('pSpanId') == span_id and
                    str(potential_child.get('level', '')).strip() == '缓慢'):
                    is_deepest = False
                    break

            if is_deepest:
                deepest_slow_nodes_in_flat.append(slow_node)
                logger.info(f"🎯 找到分支最深处缓慢节点: spanId={span_id}, duration={slow_node.get('duration', 0)}ms")

        if not deepest_slow_nodes_in_flat:
            logger.info("未找到任何分支的最深处缓慢节点")
            return

        logger.info(f"✅ 找到 {len(deepest_slow_nodes_in_flat)} 个分支的最深处缓慢节点")

        # 对每个最深处的缓慢节点，验证其子节点
        for slow_node in deepest_slow_nodes_in_flat:
            self._verify_and_update_children_level_in_flat_data(slow_node, node_map)

    def _find_deepest_slow_nodes_in_branches(self, data: Any) -> list:
        """找到所有分支中最深处的"缓慢"节点"""
        deepest_slow_nodes = []

        def traverse_branch_for_deepest_slow(node, current_path=None):
            """递归遍历分支，找到最深处的缓慢节点"""
            if current_path is None:
                current_path = []

            if not isinstance(node, dict):
                return

            # 将当前节点添加到路径中
            new_path = current_path + [node]

            # 递归处理子节点
            children = node.get('children')
            has_children = children and isinstance(children, list) and len(children) > 0

            if has_children:
                # 有子节点，继续递归
                for child in children:
                    if child is not None:
                        traverse_branch_for_deepest_slow(child, new_path)
            else:
                # 叶子节点或无子节点，检查路径中最深的缓慢节点
                deepest_slow_in_path = None
                for path_node in reversed(new_path):  # 从最深处开始查找
                    path_node_level = str(path_node.get('level', '')).strip()
                    if path_node_level == '缓慢':
                        deepest_slow_in_path = path_node
                        break

                if deepest_slow_in_path and deepest_slow_in_path not in deepest_slow_nodes:
                    deepest_slow_nodes.append(deepest_slow_in_path)
                    span_id = deepest_slow_in_path.get('spanId', 'unknown')
                    duration = deepest_slow_in_path.get('duration', 0)
                    logger.info(f"🎯 找到分支最深处缓慢节点: spanId={span_id}, duration={duration}ms")

        # 处理不同的数据格式
        if isinstance(data, list):
            # 检查是否为扁平列表格式
            has_parent_ids = any(isinstance(item, dict) and 'pSpanId' in item for item in data if item is not None)

            if has_parent_ids:
                # 扁平列表格式，先重建树形结构
                logger.info("检测到扁平列表格式，先重建树形结构进行分支遍历")
                tree_data = self._rebuild_tree_structure_from_flat(data)
                if tree_data:
                    for root_item in tree_data:
                        traverse_branch_for_deepest_slow(root_item)
            else:
                # 已经是树形结构
                for item in data:
                    if item is not None:
                        traverse_branch_for_deepest_slow(item)
        elif isinstance(data, dict):
            # 单个根节点
            traverse_branch_for_deepest_slow(data)

        return deepest_slow_nodes

    def _verify_and_update_children_level(self, slow_node: dict):
        """验证并更新缓慢节点的子节点level字段"""
        if not isinstance(slow_node, dict):
            return

        slow_duration = self._get_node_duration(slow_node)
        if slow_duration <= 0:
            logger.info(f"⚠️ 缓慢节点duration无效: {slow_duration}")
            return

        span_id = slow_node.get('spanId', 'unknown')
        logger.info(f"🔍 验证缓慢节点 {span_id} 的子节点 (duration: {slow_duration}ms)")

        # 递归验证所有子节点
        children = slow_node.get('children')
        if children and isinstance(children, list):
            for child in children:
                if child is not None:
                    self._recursive_verify_child_duration(child, slow_duration)

    def _recursive_verify_child_duration(self, child_node: dict, parent_duration: float):
        """递归验证子节点的耗时，如果相差15%以内则更新为"缓慢" """
        if not isinstance(child_node, dict):
            return

        child_duration = self._get_node_duration(child_node)
        child_span_id = child_node.get('spanId', 'unknown')
        child_level = str(child_node.get('level', '')).strip()

        # 计算耗时差异百分比
        if child_duration > 0 and parent_duration > 0:
            duration_diff_percent = abs(child_duration - parent_duration) / parent_duration * 100

            if duration_diff_percent <= 15.0:
                # 耗时相差15%以内，更新level为"缓慢"
                if child_level != '缓慢':
                    old_level = child_level
                    child_node['level'] = '缓慢'
                    logger.info(f"✅ 更新子节点level: {child_span_id} ({old_level} -> 缓慢), "
                              f"parent_duration={parent_duration}ms, child_duration={child_duration}ms, "
                              f"diff={duration_diff_percent:.1f}%")
                else:
                    logger.info(f"🔄 子节点已是缓慢: {child_span_id}, duration={child_duration}ms, "
                              f"diff={duration_diff_percent:.1f}%")

                # 继续递归验证该子节点的子节点
                grandchildren = child_node.get('children')
                if grandchildren and isinstance(grandchildren, list):
                    for grandchild in grandchildren:
                        if grandchild is not None:
                            self._recursive_verify_child_duration(grandchild, child_duration)
            else:
                logger.info(f"⏭️ 子节点耗时差异过大: {child_span_id}, "
                          f"parent_duration={parent_duration}ms, child_duration={child_duration}ms, "
                          f"diff={duration_diff_percent:.1f}% > 15%")
        else:
            logger.info(f"⚠️ 子节点duration无效: {child_span_id}, duration={child_duration}")

    def _get_node_duration(self, node: dict) -> float:
        """安全地获取节点的duration值"""
        if not isinstance(node, dict):
            return 0.0

        duration = node.get('duration', 0)
        try:
            return float(duration) if duration is not None else 0.0
        except (ValueError, TypeError):
            return 0.0

    def _verify_and_update_children_level_in_flat_data(self, slow_node: dict, node_map: dict):
        """在扁平数据中验证并更新缓慢节点的子节点level字段"""
        if not isinstance(slow_node, dict):
            return

        slow_duration = self._get_node_duration(slow_node)
        if slow_duration <= 0:
            logger.info(f"⚠️ 缓慢节点duration无效: {slow_duration}")
            return

        span_id = slow_node.get('spanId', 'unknown')
        logger.info(f"🔍 验证缓慢节点 {span_id} 的子节点 (duration: {slow_duration}ms)")

        # 在扁平数据中找到所有子节点
        children_span_ids = []
        for node in node_map.values():
            if node.get('pSpanId') == span_id:
                children_span_ids.append(node.get('spanId'))

        # 递归验证所有子节点
        for child_span_id in children_span_ids:
            if child_span_id in node_map:
                child_node = node_map[child_span_id]
                self._recursive_verify_child_duration_in_flat_data(child_node, slow_duration, node_map)

    def _recursive_verify_child_duration_in_flat_data(self, child_node: dict, parent_duration: float, node_map: dict):
        """在扁平数据中递归验证子节点的耗时，如果相差15%以内则更新为"缓慢" """
        if not isinstance(child_node, dict):
            return

        child_duration = self._get_node_duration(child_node)
        child_span_id = child_node.get('spanId', 'unknown')
        child_level = str(child_node.get('level', '')).strip()

        # 计算耗时差异百分比
        if child_duration > 0 and parent_duration > 0:
            duration_diff_percent = abs(child_duration - parent_duration) / parent_duration * 100

            if duration_diff_percent <= 15.0:
                # 耗时相差15%以内，更新level为"缓慢"
                if child_level != '缓慢':
                    old_level = child_level
                    child_node['level'] = '缓慢'
                    logger.info(f"✅ 更新子节点level: {child_span_id} ({old_level} -> 缓慢), "
                              f"parent_duration={parent_duration}ms, child_duration={child_duration}ms, "
                              f"diff={duration_diff_percent:.1f}%")
                else:
                    logger.info(f"🔄 子节点已是缓慢: {child_span_id}, duration={child_duration}ms, "
                              f"diff={duration_diff_percent:.1f}%")

                # 继续递归验证该子节点的子节点
                grandchildren_span_ids = []
                for node in node_map.values():
                    if node.get('pSpanId') == child_span_id:
                        grandchildren_span_ids.append(node.get('spanId'))

                for grandchild_span_id in grandchildren_span_ids:
                    if grandchild_span_id in node_map:
                        grandchild_node = node_map[grandchild_span_id]
                        self._recursive_verify_child_duration_in_flat_data(grandchild_node, child_duration, node_map)
            else:
                logger.info(f"⏭️ 子节点耗时差异过大: {child_span_id}, "
                          f"parent_duration={parent_duration}ms, child_duration={child_duration}ms, "
                          f"diff={duration_diff_percent:.1f}% > 15%")
        else:
            logger.info(f"⚠️ 子节点duration无效: {child_span_id}, duration={child_duration}")

    def _filter_tree_by_problem_nodes(self, original_data: Any, problem_node_map: dict) -> Any:
        """
        基于原始树形结构和问题节点列表进行过滤，
        保留所有问题节点及其所有祖先节点。
        """
        
        def filter_node(node):
            """
            递归过滤单个节点。
            返回处理后的节点(如果需要保留)，或None(如果需要剪枝)。
            """
            if not isinstance(node, dict):
                return None
            
            # 优先使用 spanId，如果不存在，则检查我们之前打上的临时标记
            span_id = node.get('spanId')
            lookup_key = node.get('__temp_id__', span_id)
            
            # 1. 递归处理子节点
            filtered_children = []
            has_problem_descendant = False
            children = node.get('children')
            if children and isinstance(children, list):
                for child in children:
                    filtered_child = filter_node(child)
                    if filtered_child:
                        # 只要有一个子节点被保留，就说明当前节点拥有了“问题后代”
                        filtered_children.append(filtered_child)
                        has_problem_descendant = True 
            
            # 2. 判断是否保留当前节点
            is_problem_node = lookup_key in problem_node_map
            
            if is_problem_node or has_problem_descendant:
                # 复制当前节点以避免修改原始数据
                result_node = node.copy()
                # 无论子节点列表是否为空，都进行赋值
                result_node['children'] = filtered_children if filtered_children else None
                return result_node
            
            # 如果当前节点不是问题节点，也没有问题的后代，则剪掉它
            return None

        # 处理根节点（可能是一个列表或单个对象）
        if isinstance(original_data, list):
            filtered_roots = []
            for item in original_data:
                filtered_item = filter_node(item)
                if filtered_item:
                    filtered_roots.append(filtered_item)
            return filtered_roots
        elif isinstance(original_data, dict):
            return filter_node(original_data)
        else:
            # 如果数据格式不正确，返回空
            return None
    
    def _find_problem_branches(self, data: Any) -> list:
        """找到所有包含问题节点的分支路径"""
        problem_branches = []
        
        # 检查数据格式：扁平列表还是树形结构
        if isinstance(data, list):
            # 检查是否为基于spanId/pSpanId的扁平列表
            has_parent_ids = any(isinstance(item, dict) and 'pSpanId' in item for item in data if item is not None)
            
            if has_parent_ids:
                # 扁平列表格式，直接遍历所有节点找问题节点
                logger.info("🔍 检测到扁平列表格式，直接遍历查找问题节点")
                self._find_problem_nodes_in_flat_list(data, problem_branches)
            else:
                # 已经是树形结构，使用递归遍历
                logger.info("🔍 检测到树形结构，递归遍历查找问题节点")
                self._find_problem_nodes_in_tree(data, problem_branches)
        else:
            # 单个根节点的树形结构
            logger.info("🔍 检测到单根节点树形结构，递归遍历查找问题节点")
            self._find_problem_nodes_in_tree(data, problem_branches)
        
        # 保存分支信息供后续树形结构重建使用
        self.current_problem_branches = problem_branches
        
        logger.info(f"🔍 找到 {len(problem_branches)} 个问题分支")
        return problem_branches
    
    def _find_problem_nodes_in_flat_list(self, flat_data: list, problem_branches: list):
        """在扁平列表中找到所有问题节点，并重建其分支路径"""
        if not flat_data:
            return
        
        # 创建节点映射表
        node_map = {item.get('spanId', ''): item for item in flat_data if isinstance(item, dict) and 'spanId' in item}
        
        # 为每个问题节点构建其完整的分支路径
        for item in flat_data:
            if not isinstance(item, dict):
                continue
            
            # 检查当前节点是否是问题节点
            node_level = str(item.get('level', '')).strip()
            is_problem = node_level in self.problem_levels
            
            if is_problem:
                # 安全地获取request值，处理None情况
                request_value = item.get('request', 'unknown')
                if request_value is None:
                    request_value = 'None'
                request_display = str(request_value)[:50]
                
                logger.info(f"🎯 找到问题节点: level={node_level}, htype={item.get('htype', 'unknown')}, request={request_display}")
                
                # 构建从根节点到当前问题节点的完整路径
                branch_path = self._build_branch_path_from_flat(item, node_map)
                if branch_path:
                    problem_branches.append(branch_path)
    
    def _build_branch_path_from_flat(self, target_node: dict, node_map: dict) -> list:
        """从扁平列表中构建到目标节点的完整分支路径"""
        path = []
        current_node = target_node
        
        # 从目标节点向上追溯到根节点
        while current_node is not None:
            # 复制节点，排除children字段
            node_copy = {k: v for k, v in current_node.items() if k != 'children'}
            path.insert(0, node_copy)  # 插入到路径开头
            
            # 查找父节点
            parent_span_id = current_node.get('pSpanId', '')
            if parent_span_id == '-1' or parent_span_id == '' or parent_span_id is None:
                # 到达根节点
                break
            
            current_node = node_map.get(parent_span_id)
            if current_node is None:
                logger.warning(f"⚠️ 找不到父节点: {parent_span_id}")
                break
        
        return path
    
    def _find_problem_nodes_in_tree(self, data: Any, problem_branches: list):
        """在树形结构中递归查找问题节点"""
        
        def traverse_and_find_branches(node, current_path=None):
            """递归遍历树形结构，找到所有问题节点的分支路径"""
            if current_path is None:
                current_path = []
            if isinstance(node, dict):
                # 将当前节点添加到路径中
                current_node_copy = {k: v for k, v in node.items() if k != 'children'}
                new_path = current_path + [current_node_copy]
                
                # 检查当前节点是否是问题节点
                node_level = str(node.get('level', '')).strip()
                is_problem = node_level in self.problem_levels
                
                if is_problem:
                    # 安全地获取request值，处理None情况
                    request_value = node.get('request', 'unknown')
                    if request_value is None:
                        request_value = 'None'
                    request_display = str(request_value)[:50]
                    
                    logger.info(f"🎯 找到问题节点: level={node_level}, htype={node.get('htype', 'unknown')}, request={request_display}")
                    problem_branches.append(new_path)
                
                # 递归处理子节点 - 特别处理children为None的情况
                children = node.get('children')
                if children is not None and isinstance(children, list):
                    for child in children:
                        if child is not None:  # 确保子节点也不是None
                            traverse_and_find_branches(child, new_path)
                        
            elif isinstance(node, list):
                for item in node:
                    if item is not None:  # 确保列表项不是None
                        traverse_and_find_branches(item, current_path)
        
        traverse_and_find_branches(data)
    
    def _further_simplify_tree_structure(self, tree_structure: Any) -> Any:
        """
        当结果超长时，进行更智能的精简。
        策略：保持树形结构，但只保留最重要的问题分支，确保输出在长度限制内。
        """

        # 1. 收集所有问题节点分支
        problem_branches = self._collect_problem_branches(tree_structure)

        if not problem_branches:
            logger.info("⚠️ 在精简步骤中未找到问题分支，无法精简")
            return None

        logger.info(f"🌿 精简开始，找到 {len(problem_branches)} 个问题分支")

        # 2. 对问题分支进行优先级排序
        def get_branch_priority(branch_info):
            """计算分支的优先级"""
            _, leaf_node = branch_info
            level = str(leaf_node.get('level', '')).strip()
            htype = leaf_node.get('htype', '')
            duration = float(leaf_node.get('duration', 0))

            # 优先级: 慢SQL > 慢Server > 其他慢节点，同类型按耗时降序
            if level in self.problem_levels:
                if htype == 'sql':
                    return (0, -duration)  # 最高优先级，耗时越长越重要
                elif htype == 'server':
                    return (1, -duration)
                else:
                    return (2, -duration)
            return (3, -duration)  # 最低优先级

        try:
            sorted_branches = sorted(problem_branches, key=get_branch_priority)
        except (ValueError, TypeError):
            # 如果排序失败，保持原顺序
            sorted_branches = problem_branches

        # 3. 逐个添加分支，直到接近长度限制
        final_structure = []
        current_length = 2  # 初始为 '[]' 的长度

        for branch_info in sorted_branches:
            branch, leaf_node = branch_info

            # 尝试将这个分支加入结果
            temp_structure = final_structure + [branch]
            temp_json = json.dumps(temp_structure, ensure_ascii=False, indent=2)

            if len(temp_json) <= self.max_length:
                # 如果没超长，就接受这个分支
                final_structure = temp_structure
                current_length = len(temp_json)
                logger.info(f"✅ 添加分支 (叶子节点: {leaf_node.get('spanId')})，当前长度: {current_length}")
            else:
                # 如果加入这个分支就超长了，停止添加
                logger.info(f"📏 添加分支 (叶子节点: {leaf_node.get('spanId')}) 会使长度超过限制，停止添加")
                break

        # 4. 边缘情况处理：如果最重要的一个分支本身就超长
        if not final_structure and sorted_branches:
            first_branch, first_leaf = sorted_branches[0]
            logger.warning(f"⚠️ 最重要的分支 (叶子节点: {first_leaf.get('spanId')}) 本身就可能超长，但仍将保留它")
            final_structure = [first_branch]

        if final_structure:
            logger.info(f"✅ 精简完成，最终保留 {len(final_structure)} 个最重要的问题分支")
            return final_structure
        else:
            logger.warning("⚠️ 经过精简后，没有分支可以放入结果中")
            return None

    def _collect_problem_branches(self, tree_structure: Any) -> list:
        """收集所有包含问题节点的独立分支"""
        problem_branches = []

        def find_problem_subtrees(node, current_path=None):
            """找到所有包含问题节点的子树"""
            if current_path is None:
                current_path = []

            if not isinstance(node, dict):
                return

            # 将当前节点添加到路径中
            new_path = current_path + [node]

            # 检查当前节点是否是问题节点
            level = str(node.get('level', '')).strip()
            is_problem_node = level in self.problem_levels

            # 检查子节点
            children = node.get('children')
            has_children = children and isinstance(children, list) and len(children) > 0

            if is_problem_node:
                # 当前节点是问题节点，检查是否应该作为独立分支
                if not current_path:
                    # 根节点就是问题节点，整个树作为一个分支
                    problem_branches.append((node.copy(), node))
                else:
                    # 非根问题节点，从根到此节点构建分支
                    branch = self._build_branch_from_path(new_path)
                    if branch:
                        problem_branches.append((branch, node))

                # 继续检查子节点，可能有更深的问题节点
                if has_children:
                    for child in children:
                        if child is not None:
                            find_problem_subtrees(child, new_path)
            else:
                # 当前节点不是问题节点，继续递归查找
                if has_children:
                    for child in children:
                        if child is not None:
                            find_problem_subtrees(child, new_path)

        # 处理不同的数据格式
        if isinstance(tree_structure, list):
            for root_node in tree_structure:
                find_problem_subtrees(root_node)
        elif isinstance(tree_structure, dict):
            find_problem_subtrees(tree_structure)

        # 去重：如果一个分支完全包含另一个分支，只保留较小的分支
        unique_branches = []
        for i, (branch1, problem_node1) in enumerate(problem_branches):
            is_contained = False
            for j, (branch2, _) in enumerate(problem_branches):
                if i != j and self._is_branch_contained(branch1, branch2):
                    is_contained = True
                    break
            if not is_contained:
                unique_branches.append((branch1, problem_node1))

        return unique_branches

    def _is_branch_contained(self, branch1: dict, branch2: dict) -> bool:
        """检查branch1是否完全包含在branch2中"""
        # 简单的实现：检查branch1的根节点spanId是否在branch2的路径中
        if not isinstance(branch1, dict) or not isinstance(branch2, dict):
            return False

        branch1_span_id = branch1.get('spanId')
        if not branch1_span_id:
            return False

        # 递归检查branch2中是否包含branch1的spanId
        def contains_span_id(node, target_span_id):
            if not isinstance(node, dict):
                return False

            if node.get('spanId') == target_span_id:
                return True

            children = node.get('children')
            if children and isinstance(children, list):
                for child in children:
                    if contains_span_id(child, target_span_id):
                        return True

            return False

        return contains_span_id(branch2, branch1_span_id)

    def _build_branch_from_path(self, path: list) -> dict:
        """从节点路径构建完整的分支树"""
        if not path:
            return None

        # 从最后一个节点开始，逆向构建树
        result = None
        for i in range(len(path) - 1, -1, -1):
            node = path[i].copy()

            if result is not None:
                # 将之前构建的结果作为当前节点的子节点
                node['children'] = [result]
            else:
                # 叶子节点，清空children
                node['children'] = None

            result = node

        return result
    
    def _filter_original_tree(self, original_data: Any, node_map: dict) -> Any:
        """基于原始树形结构进行过滤，保持父子关系"""
        
        def filter_node(node):
            """递归过滤单个节点"""
            if not isinstance(node, dict):
                return None
        
            span_id = node.get('spanId', '')
            
            # 检查当前节点是否被选中
            is_selected = span_id in node_map
            
            # 递归处理子节点
            filtered_children = []
            children = node.get('children')
            
            if children and isinstance(children, list):
                for child in children:
                    if child is not None:
                        filtered_child = filter_node(child)
                        if filtered_child is not None:
                            if isinstance(filtered_child, list):
                                # 子节点返回了列表（被提升的节点）
                                filtered_children.extend(filtered_child)
                            else:
                                # 子节点返回了单个节点
                                filtered_children.append(filtered_child)
            
            # 决定如何处理当前节点
            if is_selected:
                # 当前节点被选中，保留它及其过滤后的子节点
                result_node = node_map[span_id].copy()  # 使用优化后的节点数据
                
                if filtered_children:
                    result_node['children'] = filtered_children
                else:
                    result_node['children'] = None
                
                logger.info(f"🔗 保留节点: {span_id[:20]}... 及其 {len(filtered_children)} 个子节点")
                return result_node
            else:
                # 当前节点未被选中
                if filtered_children:
                    # 有被选中的子节点，提升它们
                    if len(filtered_children) == 1:
                        logger.info(f"⬆️ 提升单个子节点，跳过未选中的父节点: {span_id[:20]}...")
                        return filtered_children[0]
                    else:
                        logger.info(f"⬆️ 提升 {len(filtered_children)} 个子节点，跳过未选中的父节点: {span_id[:20]}...")
                        return filtered_children
                else:
                    # 没有被选中的子节点，跳过整个分支
                    return None
        
        # 检查原始数据是否存在
        if not original_data:
            logger.info("⚠️ 原始数据为空，回退到平坦列表")
            return list(node_map.values())
        
        # 检查原始数据格式：扁平列表还是树形结构
        if isinstance(original_data, list):
            # 检查是否为基于spanId/pSpanId的扁平列表
            has_parent_ids = any(isinstance(item, dict) and 'pSpanId' in item for item in original_data if item is not None)
            
            if has_parent_ids:
                # 扁平列表格式，需要先重建树形结构再过滤
                logger.info("🔄 检测到扁平列表格式（spanId/pSpanId），先重建树形结构")
                tree_data = self._rebuild_tree_structure_from_flat(original_data)
                if tree_data:
                    logger.info(f"✅ 扁平列表重建为树形结构: {len(tree_data)} 个根节点")
                    # 递归过滤重建后的树形结构
                    filtered_roots = []
                    for root_item in tree_data:
                        filtered_item = filter_node(root_item)
                        if filtered_item is not None:
                            if isinstance(filtered_item, list):
                                filtered_roots.extend(filtered_item)
                            else:
                                filtered_roots.append(filtered_item)
                    
                    logger.info(f"🌳 树形过滤完成: {len(filtered_roots)} 个根节点")
                    return filtered_roots
                else:
                    logger.info("⚠️ 扁平列表重建失败，回退到平坦列表")
                    return list(node_map.values())
            else:
                # 已经是树形结构的根节点列表
                filtered_roots = []
                for item in original_data:
                    filtered_item = filter_node(item)
                    if filtered_item is not None:
                        if isinstance(filtered_item, list):
                            filtered_roots.extend(filtered_item)
                        else:
                            filtered_roots.append(filtered_item)
                
                logger.info(f"🌳 过滤完成: {len(filtered_roots)} 个根节点")
                return filtered_roots
        else:
            # 根级是单个对象
            return filter_node(original_data)
    
    def _rebuild_tree_structure_from_flat(self, flat_data: list) -> list:
        """从扁平的spanId/pSpanId列表重建树形结构"""
        if not flat_data:
            return []
        
        # 创建所有节点的映射表
        node_map = {}
        root_nodes = []
        
        # 第一步：建立节点映射
        for item in flat_data:
            if isinstance(item, dict) and 'spanId' in item:
                span_id = item['spanId']
                # 深拷贝节点，避免修改原始数据
                node = {}
                for key, value in item.items():
                    if key != 'children':  # 忽略原有的children字段
                        node[key] = value
                node['children'] = []  # 初始化空的children数组
                node_map[span_id] = node
        
        # 第二步：建立父子关系
        for item in flat_data:
            if isinstance(item, dict) and 'spanId' in item:
                span_id = item['spanId']
                parent_span_id = item.get('pSpanId', '')
                
                current_node = node_map.get(span_id)
                if current_node is None:
                    continue
                
                # 检查是否为根节点
                if parent_span_id == '-1' or parent_span_id == '' or parent_span_id is None:
                    # 根节点
                    root_nodes.append(current_node)
                else:
                    # 子节点 - 查找父节点
                    parent_node = node_map.get(parent_span_id)
                    if parent_node is not None:
                        parent_node['children'].append(current_node)
                    else:
                        # 找不到父节点，作为根节点处理
                        logger.info(f"⚠️ 找不到父节点 {parent_span_id[:20]}...，将 {span_id[:20]}... 作为根节点")
                        root_nodes.append(current_node)
        
        # 第三步：清理空的children数组
        def cleanup_empty_children(node):
            if isinstance(node, dict):
                children = node.get('children', [])
                if children:
                    # 递归清理子节点
                    for child in children:
                        cleanup_empty_children(child)
                else:
                    # 空children数组设置为None
                    node['children'] = None
            elif isinstance(node, list):
                for item in node:
                    cleanup_empty_children(item)
        
        for root in root_nodes:
            cleanup_empty_children(root)
        
        logger.info(f"🔄 从扁平列表重建树形结构: {len(flat_data)} 个节点 -> {len(root_nodes)} 个根节点")
        return root_nodes


class LogPreprocessor:
    """日志预处理器主类"""
    
    def __init__(self):
        self._data_sources = {}
        self._filters = {}
        self._setup_default_sources()
        self._setup_default_filters()
    
    def _setup_default_sources(self):
        """设置默认数据源"""
        # API数据源配置
        api_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'gray;': '',
            'lang': 'zh-cn',
            'origin': 'https://iwork.faw.cn',
            'priority': 'u=1, i',
            'qfc-user-para': '{"systemId":"BA-0214","appCode":"BA-0214_APP_DBD"}',
            'qfcsid': 'BA-0214',
            'qfctid': 'YQJT',
            'referer': 'https://iwork.faw.cn/',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'sw8': '1-NDQ5NzA3NTk0ZDAzYTQ4YQ==-MDAwMA==-0-V0stMDAwMV9BUFBfSVdL-aHR0cHM6Ly9uZXdhcHBzLWZjLW1vbml0b3IuZmF3LmNuLw==-L3dhdGNoLXNjcmVlbi1uZXd3ZWIvcmVxdWVzdExpc3Q=-aXdvcmsuZmF3LmNu',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'useragent': 'pc',
            'x-traceid': '449707594d03a48a'
        }
        
        api_url = 'https://newapps-fc-monitor.faw.cn/api-dev/watch-clientsearch-server/getyondata/allbytraceid'
        
        self.register_data_source('api', APIDataSource(api_url, api_headers))
        self.register_data_source('local', LocalFileDataSource())
    
    def _setup_default_filters(self):
        """设置默认过滤器"""
        self.register_filter('problem_nodes', ProblemNodeFilter())
    
    def register_data_source(self, name: str, source: DataSource):
        """注册数据源"""
        self._data_sources[name] = source
    
    def register_filter(self, name: str, filter_obj: LogFilter):
        """注册过滤器"""
        self._filters[name] = filter_obj
    
    def fetch_log_data(self, traceid: str, time: str = "2025-05-06 11:30:00", 
                      authorization: str = None, source_priority: list = None) -> Optional[Dict[str, Any]]:
        """
        按优先级获取日志数据
        
        Args:
            traceid: 追踪ID
            time: 日志时间
            authorization: 授权token（API数据源需要）
            source_priority: 数据源优先级列表，默认为['api', 'local']
        
        Returns:
            日志数据或None
        """
        if source_priority is None:
            source_priority = ['api', 'local']
        
        for source_name in source_priority:
            if source_name not in self._data_sources:
                logger.info(f"⚠️ 未找到数据源: {source_name}")
                continue
            
            source = self._data_sources[source_name]
            
            # 为API数据源添加authorization
            if source_name == 'api' and authorization:
                if hasattr(source, 'headers'):
                    source.headers['authorization'] = authorization
            
            # 为本地数据源提供文件路径
            kwargs = {}
            if source_name == 'local':
                # 尝试多种本地文件路径
                potential_paths = [
                    f"log_{traceid}.json",
                    str(Path(__file__).parent.parent / f"log_{traceid}.json")
                ]
                
                for path in potential_paths:
                    if Path(path).exists():
                        kwargs['file_path'] = path
                        break
            
            data = source.fetch_data(traceid, time, **kwargs)
            if data is not None:
                return data
        
        return None
    
    def filter_log_data(self, log_data: Dict[str, Any], filter_name: str = 'problem_nodes') -> str:
        """
        过滤日志数据
        
        Args:
            log_data: 原始日志数据
            filter_name: 过滤器名称
        
        Returns:
            过滤后的日志内容字符串
        """
        if filter_name not in self._filters:
            raise ValueError(f"未找到过滤器: {filter_name}")
        
        filter_obj = self._filters[filter_name]
        return filter_obj.filter(log_data)

