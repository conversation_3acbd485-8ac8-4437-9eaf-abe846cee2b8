"""
日志预处理API模块

提供日志数据的获取、过滤和处理功能，支持多种数据源和过滤策略
"""

import json
import requests
from typing import Dict, Any, Optional, Protocol, Union
from pathlib import Path
from abc import ABC, abstractmethod
from utils.logger import logger


class DataSource(Protocol):
    """数据源协议接口"""
    
    def fetch_data(self, traceid: str, time: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取数据"""
        ...


class LogFilter(ABC):
    """日志过滤器抽象基类"""
    
    @abstractmethod
    def filter(self, log_data: Dict[str, Any]) -> str:
        """过滤日志数据"""
        ...


class APIDataSource:
    """API数据源实现"""
    
    def __init__(self, base_url: str, headers: Dict[str, str], timeout: int = 10):
        self.base_url = base_url
        self.headers = headers
        self.timeout = timeout
    
    def fetch_data(self, traceid: str, time: str, **kwargs) -> Optional[Dict[str, Any]]:
        """从API获取日志数据"""
        url = self.base_url
        data = {
            "time": time,
            "traceid": traceid,
            "isQueryIngress": kwargs.get("is_query_ingress", False)
        }
        
        try:
            logger.info(f"🔄 正在从API获取日志数据: traceid={traceid}, time={time}")
            response = requests.post(url, headers=self.headers, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            json_data = response.json()
            return self._validate_response(json_data)
            
        except requests.exceptions.Timeout:
            logger.info("❌ API请求超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.info("❌ 无法连接到API服务器")
            return None
        except requests.exceptions.HTTPError as e:
            logger.info(f"❌ HTTP请求失败: {e.response.status_code} - {e.response.reason}")
            return None
        except json.JSONDecodeError:
            logger.info("❌ API返回的数据不是有效的JSON格式")
            return None
        except Exception as e:
            logger.info(f"❌ 从API获取日志数据失败: {str(e)}")
            return None
    
    def _validate_response(self, json_data: Any) -> Optional[Dict[str, Any]]:
        """验证API响应数据"""
        if not json_data:
            logger.info("⚠️ API返回空数据")
            return None
            
        if not isinstance(json_data, dict):
            logger.info("⚠️ API返回的数据类型不是字典")
            return None
        
        # 检查错误响应
        if self._is_error_response(json_data):
            return None
        
        # 检查是否包含有效数据
        if self._has_valid_data(json_data):
            data_size = len(str(json_data))
            logger.info(f"✅ 成功从API获取日志数据，数据大小: {data_size} 字符")
            return json_data
        
        logger.info("⚠️ API返回的数据不包含有效的日志内容")
        return None
    
    def _is_error_response(self, json_data: Dict[str, Any]) -> bool:
        """检查是否是错误响应"""
        # 检查success字段
        if 'success' in json_data and json_data['success'] is False:
            error_msg = json_data.get('message', '未知错误')
            error_code = json_data.get('code', '未知代码')
            logger.info(f"❌ API返回错误: {error_code} - {error_msg}")
            return True
        
        # 检查5xx错误码
        if 'code' in json_data and str(json_data['code']).startswith('5'):
            error_msg = json_data.get('message', '服务器错误')
            logger.info(f"❌ API服务器错误: {json_data['code']} - {error_msg}")
            return True
        
        # 检查error字段
        if 'error' in json_data:
            error_msg = json_data.get('error', '未知错误')
            logger.info(f"❌ API返回错误: {error_msg}")
            return True
        
        return False
    
    def _has_valid_data(self, json_data: Dict[str, Any]) -> bool:
        """检查是否包含有效数据"""
        # 检查data字段
        if 'data' in json_data:
            data_content = json_data['data']
            if data_content and len(data_content) > 0:
                logger.info(f"✅ 检测到data字段，包含 {len(data_content)} 条记录")
                return True
            else:
                logger.info("⚠️ API返回的data字段为空")
                return False
        
        # 检查其他日志相关字段
        log_fields = ['logs', 'traces', 'entries', 'results']
        if any(key in json_data for key in log_fields):
            return True
        
        # 检查是否有足够的非状态字段
        basic_fields = {'code', 'message', 'success', 'error', 'status'}
        if len(json_data) > 3 and not set(json_data.keys()).issubset(basic_fields):
            return True
        
        return False


class LocalFileDataSource:
    """本地文件数据源实现"""
    
    def fetch_data(self, traceid: str, time: str, file_path: str = None, **kwargs) -> Optional[Dict[str, Any]]:
        """从本地文件加载日志数据"""
        if not file_path:
            # 默认使用traceid构建文件路径
            file_path = f"log_{traceid}.json"
        
        try:
            logger.info(f"📁 正在从本地文件加载数据: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"✅ 成功从本地文件加载数据")
            return data
        except FileNotFoundError:
            logger.info(f"❌ 文件不存在: {file_path}")
            return None
        except json.JSONDecodeError:
            logger.info(f"❌ 文件不是有效的JSON格式: {file_path}")
            return None
        except Exception as e:
            logger.info(f"❌ 从本地文件加载数据失败: {str(e)}")
            return None


class ProblemNodeFilter(LogFilter):
    """问题节点过滤器：筛选level为"缓慢"或"异常"的节点"""
    
    def __init__(self, problem_levels: list = None, max_length: int = 10000):
        self.problem_levels = problem_levels or ['缓慢', '异常']
        self.max_length = max_length
    
    def filter(self, log_data: Dict[str, Any]) -> str:
        """递归筛选出所有层级中level为问题级别的元素及其父级元素"""
        
        def filter_recursive(node, parent_chain=None):
            if parent_chain is None:
                parent_chain = []
            
            if isinstance(node, dict):
                # 检查是否是问题节点
                node_level = str(node.get('level', '')).strip()
                is_problem = node_level in self.problem_levels
                
                # 创建当前节点副本（不包含children）
                filtered_node = {k: v for k, v in node.items() if k != 'children'}
                
                # 处理children字段
                if 'children' in node and isinstance(node['children'], list) and node['children']:
                    filtered_children = []
                    for child in node['children']:
                        filtered_child = filter_recursive(child, parent_chain + [filtered_node])
                        if filtered_child is not None:
                            filtered_children.append(filtered_child)
                    
                    if filtered_children:
                        filtered_node['children'] = filtered_children
                        return filtered_node
                
                # 当前节点是问题节点
                if is_problem:
                    logger.info(f"找到问题节点，level={node_level}, request={node.get('request', '未知')}")
                    return filtered_node
                
                return None
                
            elif isinstance(node, list):
                filtered_list = []
                for item in node:
                    filtered_item = filter_recursive(item, parent_chain)
                    if filtered_item is not None:
                        filtered_list.append(filtered_item)
                return filtered_list if filtered_list else None
            
            return None
        
        # 处理不同的数据结构
        if 'data' in log_data and isinstance(log_data['data'], list):
            filtered_data = filter_recursive(log_data['data'])
        else:
            filtered_data = filter_recursive(log_data)
        
        if filtered_data:
            logger.info(f"筛选出含有问题节点的层级结构")
            initial_result = json.dumps(filtered_data, ensure_ascii=False, indent=2)
            
            # 检查长度是否超过限制
            if len(initial_result) > self.max_length:
                logger.info(f"⚠️ 过滤结果长度 {len(initial_result)} 超过限制 {self.max_length}，提取最深层children内容")
                deepest_children = self._extract_deepest_children(filtered_data)
                if deepest_children:
                    final_result = json.dumps(deepest_children, ensure_ascii=False, indent=2)
                    logger.info(f"✅ 提取最深层children后，长度: {len(final_result)}")
                    return final_result
                else:
                    logger.info("⚠️ 未找到最深层children，返回原始过滤结果")
                    return initial_result
            else:
                return initial_result
        else:
            logger.info("未找到任何问题节点")
            return ""
    
    def _extract_deepest_children(self, data: Any) -> Any:
        """提取数据结构中最深层的children内容，优先提取htype为server的节点"""
        
        def find_max_depth(node, current_depth=0):
            """递归查找最大深度"""
            if isinstance(node, dict):
                if 'children' in node and isinstance(node['children'], list) and node['children']:
                    max_child_depth = 0
                    for child in node['children']:
                        child_depth = find_max_depth(child, current_depth + 1)
                        max_child_depth = max(max_child_depth, child_depth)
                    return max_child_depth
                else:
                    return current_depth
            elif isinstance(node, list):
                max_depth = 0
                for item in node:
                    item_depth = find_max_depth(item, current_depth)
                    max_depth = max(max_depth, item_depth)
                return max_depth
            else:
                return current_depth
        
        def extract_nodes_at_depth(node, target_depth, current_depth=0):
            """提取指定深度的节点"""
            if current_depth == target_depth:
                # 到达目标深度，返回当前节点（去除children）
                if isinstance(node, dict):
                    return {k: v for k, v in node.items() if k != 'children'}
                else:
                    return node
            
            if isinstance(node, dict) and 'children' in node and isinstance(node['children'], list):
                deepest_nodes = []
                for child in node['children']:
                    extracted = extract_nodes_at_depth(child, target_depth, current_depth + 1)
                    if extracted is not None:
                        if isinstance(extracted, list):
                            deepest_nodes.extend(extracted)
                        else:
                            deepest_nodes.append(extracted)
                return deepest_nodes if deepest_nodes else None
            elif isinstance(node, list):
                deepest_nodes = []
                for item in node:
                    extracted = extract_nodes_at_depth(item, target_depth, current_depth)
                    if extracted is not None:
                        if isinstance(extracted, list):
                            deepest_nodes.extend(extracted)
                        else:
                            deepest_nodes.append(extracted)
                return deepest_nodes if deepest_nodes else None
            else:
                return None
        
        def prioritize_server_nodes(nodes_by_depth):
            """优先提取htype为server或sql的节点"""
            # 按深度从深到浅遍历
            for depth in sorted(nodes_by_depth.keys(), reverse=True):
                nodes = nodes_by_depth[depth]
                if not nodes:
                    continue
                
                # 分离server/sql节点和其他节点
                target_nodes = []
                other_nodes = []
                
                for node in nodes:
                    if isinstance(node, dict) and node.get('htype') in ['server', 'sql']:
                        target_nodes.append(node)
                    else:
                        other_nodes.append(node)
                
                # 如果有server或sql节点，优先返回这些节点
                if target_nodes:
                    logger.info(f"🎯 在深度 {depth} 找到 {len(target_nodes)} 个htype为server或sql的节点，优先返回")
                    return target_nodes
                
                # 如果没有server/sql节点但有其他节点，记录但继续寻找更深层的目标节点
                if other_nodes:
                    logger.info(f"📍 在深度 {depth} 找到 {len(other_nodes)} 个节点，但没有htype为server或sql的节点")
            
            # 如果没有找到任何server或sql节点，返回最深层的其他节点
            if nodes_by_depth:
                max_depth = max(nodes_by_depth.keys())
                deepest_nodes = nodes_by_depth[max_depth]
                logger.info(f"⚠️ 未找到htype为server或sql的节点，返回最深层 {len(deepest_nodes)} 个节点")
                return deepest_nodes
            
            return None
        
        # 找到最大深度
        max_depth = find_max_depth(data)
        logger.info(f"📊 数据结构最大深度: {max_depth}")
        
        if max_depth > 0:
            # 提取每个深度的节点
            nodes_by_depth = {}
            for depth in range(max_depth + 1):
                nodes = extract_nodes_at_depth(data, depth)
                if nodes:
                    nodes_by_depth[depth] = nodes
            
            # 优先提取server节点
            result_nodes = prioritize_server_nodes(nodes_by_depth)
            if result_nodes:
                logger.info(f"📋 最终提取到 {len(result_nodes)} 个节点")
                return result_nodes
        
        return None


class LogPreprocessor:
    """日志预处理器主类"""
    
    def __init__(self):
        self._data_sources = {}
        self._filters = {}
        self._setup_default_sources()
        self._setup_default_filters()
    
    def _setup_default_sources(self):
        """设置默认数据源"""
        # API数据源配置
        api_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'gray;': '',
            'lang': 'zh-cn',
            'origin': 'https://iwork.faw.cn',
            'priority': 'u=1, i',
            'qfc-user-para': '{"systemId":"BA-0214","appCode":"BA-0214_APP_DBD"}',
            'qfcsid': 'BA-0214',
            'qfctid': 'YQJT',
            'referer': 'https://iwork.faw.cn/',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'sw8': '1-NDQ5NzA3NTk0ZDAzYTQ4YQ==-MDAwMA==-0-V0stMDAwMV9BUFBfSVdL-aHR0cHM6Ly9uZXdhcHBzLWZjLW1vbml0b3IuZmF3LmNuLw==-L3dhdGNoLXNjcmVlbi1uZXd3ZWIvcmVxdWVzdExpc3Q=-aXdvcmsuZmF3LmNu',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'useragent': 'pc',
            'x-traceid': '449707594d03a48a'
        }
        
        api_url = 'https://newapps-fc-monitor.faw.cn/api-dev/watch-clientsearch-server/getyondata/allbytraceid'
        
        self.register_data_source('api', APIDataSource(api_url, api_headers))
        self.register_data_source('local', LocalFileDataSource())
    
    def _setup_default_filters(self):
        """设置默认过滤器"""
        self.register_filter('problem_nodes', ProblemNodeFilter())
    
    def register_data_source(self, name: str, source: DataSource):
        """注册数据源"""
        self._data_sources[name] = source
    
    def register_filter(self, name: str, filter_obj: LogFilter):
        """注册过滤器"""
        self._filters[name] = filter_obj
    
    def fetch_log_data(self, traceid: str, time: str = "2025-05-06 11:30:00", 
                      authorization: str = None, source_priority: list = None) -> Optional[Dict[str, Any]]:
        """
        按优先级获取日志数据
        
        Args:
            traceid: 追踪ID
            time: 日志时间
            authorization: 授权token（API数据源需要）
            source_priority: 数据源优先级列表，默认为['api', 'local']
        
        Returns:
            日志数据或None
        """
        if source_priority is None:
            source_priority = ['api', 'local']
        
        for source_name in source_priority:
            if source_name not in self._data_sources:
                logger.info(f"⚠️ 未找到数据源: {source_name}")
                continue
            
            source = self._data_sources[source_name]
            
            # 为API数据源添加authorization
            if source_name == 'api' and authorization:
                if hasattr(source, 'headers'):
                    source.headers['authorization'] = authorization
            
            # 为本地数据源提供文件路径
            kwargs = {}
            if source_name == 'local':
                # 尝试多种本地文件路径
                potential_paths = [
                    f"log_{traceid}.json",
                    str(Path(__file__).parent.parent / f"log_{traceid}.json")
                ]
                
                for path in potential_paths:
                    if Path(path).exists():
                        kwargs['file_path'] = path
                        break
            
            data = source.fetch_data(traceid, time, **kwargs)
            if data is not None:
                return data
        
        return None
    
    def filter_log_data(self, log_data: Dict[str, Any], filter_name: str = 'problem_nodes') -> str:
        """
        过滤日志数据
        
        Args:
            log_data: 原始日志数据
            filter_name: 过滤器名称
        
        Returns:
            过滤后的日志内容字符串
        """
        if filter_name not in self._filters:
            raise ValueError(f"未找到过滤器: {filter_name}")
        
        filter_obj = self._filters[filter_name]
        return filter_obj.filter(log_data)

