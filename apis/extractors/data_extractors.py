"""
数据提取器模块

提供各种数据提取功能，包括文件名、表名、接口名、服务器和时间信息的提取
"""

import re
from typing import Dict, Any, Tuple, Set, List
from abc import ABC, abstractmethod
from utils.logger import logger


class DataExtractor(ABC):
    """数据提取器抽象基类"""
    
    @abstractmethod
    def extract(self, data: Dict[str, Any]) -> <PERSON>ple[List[str], str]:
        """提取数据并返回列表和字符串格式"""
        ...


class FileNameExtractor(DataExtractor):
    """文件名提取器（增强版：包含app_code信息）"""
    
    def extract(self, error_locations: Dict[str, Any]) -> <PERSON><PERSON>[List[Dict[str, str]], str]:
        """从错误位置信息中提取不重复的文件名（附带app_code信息）
        
        Returns:
            Tuple[List[Dict[str, str]], str]: 
            - 第一个元素：包含文件名和app_code的字典列表，格式为 [{"file_name": "xx.java", "app_code": "SERVICE_001"}]
            - 第二个元素：文件名字符串，格式为 "文件名1(SERVICE_001)，文件名2(SERVICE_002)"
        """
        file_info_set: Set[Tuple[str, str]] = set()  # (file_name, app_code) 的集合
        
        # 针对新的JSON结构进行特定提取
        if "issues" in error_locations and isinstance(error_locations["issues"], list):
            for issue in error_locations["issues"]:
                if isinstance(issue, dict):
                    self._extract_from_issue(issue, file_info_set)
        
        # 如果新结构没有找到文件名，使用递归提取方法作为后备
        if not file_info_set:
            self._extract_recursively(error_locations, file_info_set)
        
        # 转换为字典列表并排序
        file_info_list = [
            {"file_name": file_name, "app_code": app_code}
            for file_name, app_code in sorted(file_info_set)
        ]
        
        # 构建文件名字符串
        file_names_str = "，".join([
            f"{info['file_name']}({info['app_code']})" if info['app_code'] else info['file_name']
            for info in file_info_list
        ])
        
        return file_info_list, file_names_str
    
    def _extract_from_issue(self, issue: Dict[str, Any], file_info_set: Set[Tuple[str, str]]):
        """从单个issue中提取文件名和app_code（按优先级顺序）
        
        优先级：root_cause > propagation_chain > final_manifestation
        只有在高优先级位置没有找到文件名时，才会查找低优先级位置
        """
        initial_size = len(file_info_set)
        
        # 优先级1：从起源点提取
        if "root_cause" in issue and "origin_point" in issue["root_cause"]:
            self._extract_file_name_from_location(issue["root_cause"]["origin_point"], file_info_set)
            
            # 如果在root_cause中找到了文件名，就不再查找其他位置
            if len(file_info_set) > initial_size:
                return
        
        # 优先级2：从传播链提取（仅在root_cause中未找到时）
        if "propagation_chain" in issue and isinstance(issue["propagation_chain"], list):
            for node in issue["propagation_chain"]:
                if isinstance(node, dict) and "location" in node:
                    self._extract_file_name_from_location(node["location"], file_info_set)
                    
                    # 如果在propagation_chain中找到了文件名，就不再查找final_manifestation
                    if len(file_info_set) > initial_size:
                        return
        
        # 优先级3：从最终表现位置提取（仅在前两者都未找到时）
        if "final_manifestation" in issue and "location" in issue["final_manifestation"]:
            self._extract_file_name_from_location(issue["final_manifestation"]["location"], file_info_set)
    
    def _extract_file_name_from_location(self, location: Dict[str, Any], file_info_set: Set[Tuple[str, str]]):
        """从location字典中提取文件名和app_code"""
        if not isinstance(location, dict):
            return
            
        file_name = None
        app_code = ""
        
        # 提取文件名
        if "file_name" in location and isinstance(location["file_name"], str):
            file_name = location["file_name"].split('/')[-1].split('\\')[-1]
        
        # 提取app_code
        if "app_code" in location and isinstance(location["app_code"], str):
            app_code = location["app_code"]
        
        # 添加到集合中
        if file_name and ('.' in file_name):
            file_info_set.add((file_name, app_code))
    
    def _extract_recursively(self, data: Any, file_info_set: Set[Tuple[str, str]]):
        """递归提取文件名（兼容旧格式）"""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(key, str) and ('file' in key.lower() or 'path' in key.lower()) and isinstance(value, str):
                    file_name = value.split('/')[-1].split('\\')[-1]
                    if file_name and ('.' in file_name):
                        file_info_set.add((file_name, ""))  # 旧格式没有app_code
                self._extract_recursively(value, file_info_set)
        elif isinstance(data, list):
            for item in data:
                self._extract_recursively(item, file_info_set)


class TableNameExtractor(DataExtractor):
    """表名提取器（增强版：包含app_code信息）"""

    def extract(self, error_locations: Dict[str, Any]) -> Tuple[List[Dict[str, str]], str]:
        """从错误位置信息中提取不重复的表名（附带app_code信息）

        Returns:
            Tuple[List[Dict[str, str]], str]:
            - 第一个元素：包含表名和app_code的字典列表，格式为 [{"table_name": "user_table", "app_code": "SERVICE_001"}]
            - 第二个元素：表名字符串，格式为 "表名1(SERVICE_001)，表名2(SERVICE_002)"
        """
        table_info_set: Set[Tuple[str, str]] = set()  # (table_name, app_code) 的集合

        # 针对新的JSON结构进行特定提取
        if "issues" in error_locations and isinstance(error_locations["issues"], list):
            for issue in error_locations["issues"]:
                if isinstance(issue, dict):
                    self._extract_from_issue(issue, table_info_set)

        # 转换为字典列表并排序
        table_info_list = [
            {"table_name": table_name, "app_code": app_code}
            for table_name, app_code in sorted(table_info_set)
        ]

        # 构建表名字符串
        table_names_str = "，".join([
            f"{info['table_name']}({info['app_code']})" if info['app_code'] else info['table_name']
            for info in table_info_list
        ])

        return table_info_list, table_names_str

    def _extract_from_issue(self, issue: Dict[str, Any], table_info_set: Set[Tuple[str, str]]):
        """从单个issue中提取表名和app_code（按优先级顺序）

        优先级：root_cause > propagation_chain > final_manifestation
        只有在高优先级位置没有找到表名时，才会查找低优先级位置
        """
        initial_size = len(table_info_set)

        # 优先级1：从起源点提取
        if "root_cause" in issue and "origin_point" in issue["root_cause"]:
            self._extract_from_location(issue["root_cause"]["origin_point"], table_info_set)

            # 如果在root_cause中找到了表名，就不再查找其他位置
            if len(table_info_set) > initial_size:
                return

        # 优先级2：从传播链提取（仅在root_cause中未找到时）
        if "propagation_chain" in issue and isinstance(issue["propagation_chain"], list):
            for node in issue["propagation_chain"]:
                if isinstance(node, dict) and "location" in node:
                    self._extract_from_location(node["location"], table_info_set)

                    # 如果在propagation_chain中找到了表名，就不再查找final_manifestation
                    if len(table_info_set) > initial_size:
                        return

        # 优先级3：从最终表现位置提取（仅在前两者都未找到时）
        if "final_manifestation" in issue and "location" in issue["final_manifestation"]:
            self._extract_from_location(issue["final_manifestation"]["location"], table_info_set)

    def _extract_from_location(self, location: Dict[str, Any], table_info_set: Set[Tuple[str, str]]):
        """从location字典中提取表名和app_code"""
        if not isinstance(location, dict):
            return

        app_code = ""

        # 提取app_code
        if "app_code" in location and isinstance(location["app_code"], str):
            app_code = location["app_code"]

        # 从table_name_list字段提取
        if "table_name_list" in location and isinstance(location["table_name_list"], list):
            for table in location["table_name_list"]:
                if table and isinstance(table, str):
                    table_info_set.add((table, app_code))

        # 从SQL中提取表名
        if "sql" in location and isinstance(location["sql"], str):
            sql = location["sql"]
            table_pattern = re.compile(r'(?:FROM|JOIN)\s+(\w+)', re.IGNORECASE)
            for match in table_pattern.finditer(sql):
                table = match.group(1)
                if table and len(table) > 1:
                    table_info_set.add((table, app_code))


class InterfaceNameExtractor(DataExtractor):
    """接口名提取器（包含app_code信息）"""

    def extract(self, error_locations: Dict[str, Any]) -> Tuple[List[Dict[str, str]], str]:
        """从错误位置信息中提取不重复的接口名（附带app_code信息）

        Returns:
            Tuple[List[Dict[str, str]], str]:
            - 第一个元素：包含接口名和app_code的字典列表，格式为 [{"interface_name": "/api/v1/user", "app_code": "SERVICE_001"}]
            - 第二个元素：接口名字符串，格式为 "接口名1(SERVICE_001)，接口名2(SERVICE_002)"
        """
        interface_info_set: Set[Tuple[str, str]] = set()  # (interface_name, app_code) 的集合

        # 针对新的JSON结构进行特定提取
        if "issues" in error_locations and isinstance(error_locations["issues"], list):
            for issue in error_locations["issues"]:
                if isinstance(issue, dict):
                    self._extract_from_issue(issue, interface_info_set)

        # 转换为字典列表并排序
        interface_info_list = [
            {"interface_name": interface_name, "app_code": app_code}
            for interface_name, app_code in sorted(interface_info_set)
        ]

        # 构建接口名字符串
        interface_names_str = "，".join([
            f"{info['interface_name']}({info['app_code']})" if info['app_code'] else info['interface_name']
            for info in interface_info_list
        ])

        return interface_info_list, interface_names_str

    def _extract_from_issue(self, issue: Dict[str, Any], interface_info_set: Set[Tuple[str, str]]):
        """从单个issue中提取接口名和app_code（按优先级顺序）

        优先级：root_cause > propagation_chain > final_manifestation
        只有在高优先级位置没有找到接口名时，才会查找低优先级位置
        """
        initial_size = len(interface_info_set)

        # 优先级1：从起源点提取
        if "root_cause" in issue and "origin_point" in issue["root_cause"]:
            self._extract_from_location(issue["root_cause"]["origin_point"], interface_info_set)

            # 如果在root_cause中找到了接口名，就不再查找其他位置
            if len(interface_info_set) > initial_size:
                return

        # 优先级2：从传播链提取（仅在root_cause中未找到时）
        if "propagation_chain" in issue and isinstance(issue["propagation_chain"], list):
            for node in issue["propagation_chain"]:
                if isinstance(node, dict) and "location" in node:
                    self._extract_from_location(node["location"], interface_info_set)

                    # 如果在propagation_chain中找到了接口名，就不再查找final_manifestation
                    if len(interface_info_set) > initial_size:
                        return

        # 优先级3：从最终表现位置提取（仅在前两者都未找到时）
        if "final_manifestation" in issue and "location" in issue["final_manifestation"]:
            self._extract_from_location(issue["final_manifestation"]["location"], interface_info_set)

    def _extract_from_location(self, location: Dict[str, Any], interface_info_set: Set[Tuple[str, str]]):
        """从location字典中提取接口名和app_code"""
        if not isinstance(location, dict):
            return

        app_code = ""

        # 提取app_code
        if "app_code" in location and isinstance(location["app_code"], str):
            app_code = location["app_code"]

        # 从service_url字段提取
        if "service_url" in location and isinstance(location["service_url"], str):
            service_url = location["service_url"].strip()
            if service_url:
                interface_info_set.add((service_url, app_code))


class ServerTimeInfoExtractor:
    """服务器和时间信息提取器"""

    def extract(self, error_locations: Dict[str, Any]) -> Dict[str, Any]:
        """从错误位置信息中提取服务名称和时间信息"""
        server_names: Set[str] = set()
        time_info = {
            "start_times": [],
            "end_times": [],
            "durations": [],
            "occurrence_times": []
        }

        if "issues" in error_locations and isinstance(error_locations["issues"], list):
            for issue in error_locations["issues"]:
                if isinstance(issue, dict):
                    self._extract_from_issue(issue, server_names, time_info)

        return {
            "server_names": sorted(list(server_names)),
            "server_names_str": "，".join(sorted(list(server_names))) if server_names else "",
            "time_info": time_info
        }

    def _extract_from_issue(self, issue: Dict[str, Any], server_names: Set[str], time_info: Dict[str, Any]):
        """从单个issue中提取服务器和时间信息（按优先级顺序）

        优先级：root_cause > propagation_chain > final_manifestation
        只有在高优先级位置没有找到服务器或时间信息时，才会查找低优先级位置
        """
        initial_server_count = len(server_names)
        initial_time_count = sum(len(times) for times in time_info.values())

        # 优先级1：从起源点提取
        if "root_cause" in issue and "origin_point" in issue["root_cause"]:
            self._extract_from_location(issue["root_cause"]["origin_point"], "起源点", server_names, time_info)

            # 如果在root_cause中找到了服务器或时间信息，就不再查找其他位置
            current_server_count = len(server_names)
            current_time_count = sum(len(times) for times in time_info.values())
            if current_server_count > initial_server_count or current_time_count > initial_time_count:
                return

        # 优先级2：从传播链提取（仅在root_cause中未找到时）
        if "propagation_chain" in issue and isinstance(issue["propagation_chain"], list):
            for i, node in enumerate(issue["propagation_chain"]):
                if isinstance(node, dict) and "location" in node:
                    self._extract_from_location(node["location"], f"传播节点{i+1}", server_names, time_info)

                    # 如果在propagation_chain中找到了服务器或时间信息，就不再查找final_manifestation
                    current_server_count = len(server_names)
                    current_time_count = sum(len(times) for times in time_info.values())
                    if current_server_count > initial_server_count or current_time_count > initial_time_count:
                        return

        # 优先级3：从最终表现位置提取（仅在前两者都未找到时）
        if "final_manifestation" in issue and "location" in issue["final_manifestation"]:
            self._extract_from_location(issue["final_manifestation"]["location"], "最终表现", server_names, time_info)

    def _extract_from_location(self, location: Dict[str, Any], context: str,
                              server_names: Set[str], time_info: Dict[str, Any]):
        """从location字典中提取信息"""
        if not isinstance(location, dict):
            return

        # 提取服务名称
        if "server_name" in location and isinstance(location["server_name"], str):
            server_names.add(location["server_name"])

        # 提取时间信息
        if "timestamp" in location and isinstance(location["timestamp"], dict):
            timestamp = location["timestamp"]

            # 时间类型映射到time_info中的键
            time_type_mapping = {
                "start_time": "start_times",
                "end_time": "end_times",
                "duration_ms": "durations",
                "occurrence_time": "occurrence_times"
            }

            for time_type, time_key in time_type_mapping.items():
                if time_type in timestamp and timestamp[time_type]:
                    time_info[time_key].append(f"{context}: {timestamp[time_type]}")
