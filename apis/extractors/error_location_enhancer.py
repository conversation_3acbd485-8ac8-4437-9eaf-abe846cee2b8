#!/usr/bin/env python3
"""
错误位置信息增强器

该模块用于从过滤后的日志内容中提取完整信息，
并增强error_locations中的各个字段，提升问题定位的准确性。

当前支持的增强功能：
- SQL语句内容增强
- app_code格式清理
- 后续可扩展：文件路径增强、方法信息增强等
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from utils.logger import logger


class ErrorLocationEnhancer:
    """错误位置信息增强器"""
    
    def __init__(self):
        """初始化错误位置增强器"""
        self.time_tolerance_ms = 1000  # 时间匹配容差，毫秒
    
    def enhance_error_locations(self, 
                              error_locations: Dict[str, Any], 
                              filtered_log: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        增强error_locations中的各种内容
        
        Args:
            error_locations: 错误位置信息
            filtered_log: 过滤后的日志内容
            
        Returns:
            增强后的错误位置信息
        """
        logger.info(f"🔧 开始错误位置信息增强处理...")
        logger.info(f"   - 错误位置数量: {len(error_locations.get('issues', []))}")
        logger.info(f"   - 过滤日志数量: {len(filtered_log)}")
        
        enhanced_locations = error_locations.copy()
        enhancement_summary = {
            "total_issues": len(error_locations.get('issues', [])),
            "sql_enhanced_count": 0,
            "app_code_cleaned_count": 0,
            "failed_count": 0
        }
        
        # 首先进行app_code格式清理（全局处理）
        self._clean_app_code_format(enhanced_locations, enhancement_summary)
        
        # 处理每个issue
        for i, issue in enumerate(enhanced_locations.get('issues', [])):
            issue_id = f"Issue-{i+1}"
            logger.info(f"\n📍 处理 {issue_id}")
            
            try:
                # 处理根因原点
                if 'root_cause' in issue and 'origin_point' in issue['root_cause']:
                    origin_enhanced = self._enhance_location_info(
                        issue['root_cause']['origin_point'], 
                        filtered_log, 
                        f"{issue_id}-origin"
                    )
                    if origin_enhanced:
                        enhancement_summary["sql_enhanced_count"] += 1
                    else:
                        enhancement_summary["failed_count"] += 1
                
                # 处理传播链
                for j, chain_item in enumerate(issue.get('propagation_chain', [])):
                    if 'location' in chain_item:
                        chain_enhanced = self._enhance_location_info(
                            chain_item['location'], 
                            filtered_log,
                            f"{issue_id}-chain-{j+1}"
                        )
                        if chain_enhanced:
                            enhancement_summary["sql_enhanced_count"] += 1
                
                # 处理最终表现
                if 'final_manifestation' in issue and 'location' in issue['final_manifestation']:
                    final_enhanced = self._enhance_location_info(
                        issue['final_manifestation']['location'], 
                        filtered_log,
                        f"{issue_id}-final"
                    )
                    if final_enhanced:
                        enhancement_summary["sql_enhanced_count"] += 1
                        
            except Exception as e:
                logger.info(f"❌ 处理 {issue_id} 时出错: {str(e)}")
                enhancement_summary["failed_count"] += 1
        
        # 不修改原始数据结构，增强摘要仅用于日志输出
        
        logger.info(f"\n📊 错误位置信息增强完成:")
        logger.info(f"   - 成功增强: {enhancement_summary['sql_enhanced_count']}")
        logger.info(f"   - app_code清理: {enhancement_summary['app_code_cleaned_count']}")
        logger.info(f"   - 增强失败: {enhancement_summary['failed_count']}")
        
        return enhanced_locations
    
    def _clean_app_code_format(self, error_locations: Dict[str, Any], enhancement_summary: Dict[str, Any]) -> None:
        """
        清理app_code格式，去掉方括号部分
        例如：MA-0603_MSA_SQU[ma-0603-msa-squ] -> MA-0603_MSA_SQU
        
        Args:
            error_locations: 错误位置信息
            enhancement_summary: 增强摘要统计
        """
        logger.info(f"🧹 开始app_code格式清理...")
        
        def clean_data_recursively(data):
            """递归清理数据中的app_code字段"""
            
            if isinstance(data, dict):
                for key, value in data.items():
                    if key == "app_code" and isinstance(value, str):
                        # 使用正则表达式去掉方括号及其内容
                        original_value = value
                        cleaned_value = re.sub(r'\[.*?\]', '', value).strip()
                        
                        if cleaned_value != original_value:
                            data[key] = cleaned_value
                            enhancement_summary["app_code_cleaned_count"] += 1
                            logger.info(f"   🧹 清理app_code格式: '{original_value}' -> '{cleaned_value}'")
                    else:
                        # 递归处理嵌套结构
                        clean_data_recursively(value)
            elif isinstance(data, list):
                for item in data:
                    clean_data_recursively(item)
        
        # 执行清理
        clean_data_recursively(error_locations)
        
        if enhancement_summary["app_code_cleaned_count"] > 0:
            logger.info(f"✅ app_code格式清理完成: 共清理 {enhancement_summary['app_code_cleaned_count']} 个字段")
        else:
            logger.info("📊 未发现需要清理的app_code格式")
    
    def _enhance_location_info(self, 
                              location: Dict[str, Any], 
                              filtered_log: List[Dict[str, Any]], 
                              location_id: str) -> Optional[Dict[str, Any]]:
        """
        在指定位置信息中增强各种内容
        
        Args:
            location: 位置信息
            filtered_log: 过滤后的日志内容
            location_id: 位置标识符
            
        Returns:
            增强后的位置信息，如果找不到匹配则返回None
        """
        # 提取匹配关键信息
        app_code = location.get('app_code', '')
        timestamp_info = location.get('timestamp', {})
        service_url = location.get('service_url', None)
        
        if not app_code or not timestamp_info:
            logger.info(f"   ⚠️ {location_id}: 缺少app_code或timestamp信息")
            return None
        
        # 获取时间信息
        occurrence_time = timestamp_info.get('occurrence_time') or timestamp_info.get('start_time')
        if not occurrence_time:
            logger.info(f"   ⚠️ {location_id}: 缺少occurrence_time信息")
            return None
        
        logger.info(f"   🔍 {location_id}: 查找匹配 app_code={app_code}, time={occurrence_time}")
        
        # 查找匹配的日志条目
        matched_log_entry = self._find_matching_log_entry(
            app_code, occurrence_time, filtered_log, service_url
        )
        
        if not matched_log_entry:
            logger.info(f"   ❌ {location_id}: 未找到匹配的日志条目")
            return None
        
        # 执行各种增强处理
        enhancements = []
        enhanced_count = 0
        
        # 1. SQL内容增强
        sql_enhanced = self._enhance_sql_content(location, matched_log_entry)
        if sql_enhanced:
            enhanced_count += 1
            enhancements.append("sql_content")
        # 2. service_url增强
        service_url_enhanced = self._enhance_service_url(location, filtered_log, location_id)
        if service_url_enhanced:
            enhanced_count += 1
            enhancements.append("service_url")
        
        # 2. 可扩展：其他字段增强
        # file_path_enhanced = self._enhance_file_path(location, matched_log_entry)
        # method_info_enhanced = self._enhance_method_info(location, matched_log_entry)
        
        if enhanced_count > 0:
            logger.info(f"   ✅ {location_id}: 成功增强 {enhanced_count} 个字段: {', '.join(enhancements)}")
            return location
        
        return None
    
    def _enhance_sql_content(self, 
                           location: Dict[str, Any], 
                           log_entry: Dict[str, Any]) -> bool:
        """
        增强SQL内容
        
        Args:
            location: 位置信息
            log_entry: 匹配的日志条目
            
        Returns:
            是否成功增强
        """
        sql_statement = self._extract_sql_from_log_entry(log_entry)
        
        if sql_statement:
            # 只更新SQL字段内容，不添加额外字段
            location['sql'] = sql_statement
            
            logger.info(f"      🔧 SQL内容增强成功 ({len(sql_statement)} 字符)")
            return True
        
        return False
    
    def _enhance_service_url(self, location: Dict[str, Any], filtered_log: List[Dict[str, Any]], location_id: str) -> bool:
        """
        用filtered_log中的request内容增强service_url字段（更健壮的路径匹配）
        """
        service_url = location.get('service_url')
        print(f"service_url: {service_url}")
        if not service_url:
            return False
        service_url = service_url.strip()
        print(f"service_url: {service_url}")
        for log_entry in filtered_log:
            request = log_entry.get('request')
            print(f"request: {request}")
            if not request:
                continue
            request = request.strip()
            print(f"request: {request}")
            # 剥离POST:/xxx、GET:/xxx等前缀
            if ':' in request:
                method, req_path = request.split(':', 1)
                req_path = req_path.strip()
            else:
                req_path = request
            print(f"req_path: {req_path}")
            # 精确匹配路径
            if req_path == service_url:
                location['service_url'] = request
                logger.info(f"   ✅ {location_id}: service_url增强成功，更新为更完整内容: {request}")
                return True
            # 兼容原有“包含”逻辑
            if service_url in request:
                location['service_url'] = request
                logger.info(f"   ✅ {location_id}: service_url增强成功（模糊匹配），更新为: {request}")
                return True
        logger.info(f"   ❌ {location_id}: 未找到可增强的service_url, 当前: {service_url}")
        return False
    
    def _find_matching_log_entry(self, 
                                app_code: str, 
                                target_time: str, 
                                filtered_log: List[Dict[str, Any]],
                                service_url: str = None) -> Optional[Dict[str, Any]]:
        """
        根据app_code和时间查找匹配的日志条目，支持service_url路径精确匹配
        """
        try:
            target_dt = self._parse_datetime(target_time)
            if not target_dt:
                return None

            best_match = None
            min_time_diff = float('inf')

            for log_entry in filtered_log:
                # 新增：service_url路径精确匹配
                if service_url:
                    request = log_entry.get('request', '').strip()
                    if ':' in request:
                        _, req_path = request.split(':', 1)
                        req_path = req_path.strip()
                    else:
                        req_path = request
                    print(f"[DEBUG] service_url: {service_url}, request: {request}, req_path: {req_path}")
                    if req_path == service_url.strip():
                        # 时间判断
                        log_time = log_entry.get('time', '')
                        log_dt = self._parse_datetime(log_time)
                        if not log_dt:
                            continue
                        time_diff = abs((target_dt - log_dt).total_seconds() * 1000)
                        if time_diff <= self.time_tolerance_ms and time_diff < min_time_diff:
                            min_time_diff = time_diff
                            best_match = log_entry
                        continue  # 跳过后续app_code匹配

                # 原有app_code匹配逻辑
                servername = log_entry.get('servername', '')
                if '[' in servername:
                    servername = servername.split('[')[0]
                if servername != app_code:
                    continue
                # 检查是否是SQL类型的日志
                log_type = log_entry.get('type', '')
                htype = log_entry.get('htype', '')
                if log_type != 'SQL' and htype != 'sql':
                    continue
                # 检查时间匹配
                log_time = log_entry.get('time', '')
                if not log_time:
                    continue
                log_dt = self._parse_datetime(log_time)
                if not log_dt:
                    continue
                time_diff = abs((target_dt - log_dt).total_seconds() * 1000)
                if time_diff <= self.time_tolerance_ms and time_diff < min_time_diff:
                    min_time_diff = time_diff
                    best_match = log_entry

            if best_match:
                print(f"[DEBUG] 匹配到日志条目，时间差: {min_time_diff}ms")
                return best_match
            return None
        except Exception as e:
            print(f"[DEBUG] 查找匹配条目时出错: {str(e)}")
            return None
    
    def _extract_sql_from_log_entry(self, log_entry: Dict[str, Any]) -> Optional[str]:
        """
        从日志条目中提取SQL语句
        
        Args:
            log_entry: 日志条目
            
        Returns:
            提取的SQL语句，如果提取失败则返回None
        """
        try:
            detail = log_entry.get('detail', [])
            if not detail or not isinstance(detail, list):
                return None
            
            # 查找SQL语句
            for item in detail:
                if isinstance(item, str) and item.startswith('SQL：'):
                    # 提取SQL：后面的内容
                    sql_statement = item[4:].strip()  # 去掉"SQL："前缀
                    if sql_statement:
                        return sql_statement
            
            return None
            
        except Exception as e:
            logger.info(f"      提取SQL语句时出错: {str(e)}")
            return None
    
    def _parse_datetime(self, time_str: str) -> Optional[datetime]:
        """
        解析时间字符串
        
        Args:
            time_str: 时间字符串
            
        Returns:
            解析后的datetime对象，如果解析失败则返回None
        """
        try:
            # 支持的时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S.%f',  # 2025-06-30 09:58:54.113
                '%Y-%m-%d %H:%M:%S',     # 2025-06-30 09:58:54
                '%Y-%m-%d %H:%M',        # 2025-06-30 09:58
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            return None
            
        except Exception:
            return None
    
    def enhance_from_files(self, 
                          error_locations_file: str, 
                          filtered_log_file: str, 
                          output_file: str = None) -> Dict[str, Any]:
        """
        从文件中读取数据并进行错误位置信息增强
        
        Args:
            error_locations_file: 错误位置文件路径
            filtered_log_file: 过滤日志文件路径
            output_file: 输出文件路径，如果不提供则覆盖原文件
            
        Returns:
            增强后的错误位置信息
        """
        try:
            logger.info(f"📁 读取文件进行错误位置信息增强...")
            logger.info(f"   - 错误位置文件: {error_locations_file}")
            logger.info(f"   - 过滤日志文件: {filtered_log_file}")
            
            # 读取错误位置文件
            with open(error_locations_file, 'r', encoding='utf-8') as f:
                error_locations = json.load(f)
            
            # 读取过滤日志文件
            with open(filtered_log_file, 'r', encoding='utf-8') as f:
                filtered_log = json.load(f)
            
            # 执行增强
            enhanced_locations = self.enhance_error_locations(error_locations, filtered_log)
            
            # 保存结果
            output_path = output_file or error_locations_file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(enhanced_locations, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 增强结果已保存到: {output_path}")
            return enhanced_locations
            
        except Exception as e:
            logger.info(f"❌ 文件处理过程中出错: {str(e)}")
            raise

