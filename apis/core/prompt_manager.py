"""
提示词模板管理器模块

管理各种提示词模板，支持动态构建和自定义模板
"""

from typing import Dict, Optional
from utils.logger import logger


class PromptTemplateManager:
    """提示词模板管理器"""
    
    def __init__(self, system_prompt: Optional[str] = None):
        self.system_prompt = system_prompt or "你是一个智能助手，采用ReAct（Reasoning-Acting-Observation）模式来解决问题。"
        self.templates = self._load_default_templates()
    
    def _load_default_templates(self) -> Dict[str, str]:
        """加载默认提示词模板"""
        return {
            "initial_reasoning": """
{system_prompt}

可用工具：
{tools_description}

用户查询：{query}

请仔细分析这个问题，并进行初始推理：

1. 理解用户的查询意图
2. 分析需要什么信息来解决这个问题
3. 考虑应该使用哪些工具
4. 制定初步的解决策略

请详细说明你的推理过程，不超过300字。直接输出推理内容，不要使用任何标签或格式化。
""",
            
            "action_decision": """
基于之前的推理和观察，现在需要决定下一步行动。

用户查询：{query}

可用工具：
{tools_description}

对话历史：{recent_history}

当前迭代：{current_iteration}/{max_iterations}

请决定下一步行动。你有两种选择：

1. 使用工具：如果需要获取更多信息或执行某个操作
   输出格式：
   ACTION: USE_TOOL
   TOOL_NAME: [工具名称]
   ARGUMENTS: {{"参数名": "参数值", ...}}
   REASON: [使用此工具的原因]

2. 完成任务：如果已经获得足够信息可以回答用户查询
   输出格式：
   ACTION: FINISH
   REASON: [为什么认为任务已完成]

请直接输出你的决策，不要有其他内容。
""",
            
            "continue_reasoning": """
基于最新的观察结果，请继续推理：

用户查询：{query}

最近的对话历史：{recent_history}

当前进度：已完成 {successful_tools}/{total_tools} 个工具调用

请分析：
1. 从最新的工具执行结果中得到了什么信息？
2. 这些信息是否足以回答用户的查询？
3. 还需要什么额外的信息？
4. 下一步应该采取什么行动？

请简洁地说明你的推理过程，不超过200字。直接输出推理内容。
"""
        }
    
    def build_prompt(self, template_name: str, **kwargs) -> str:
        """构建提示词"""
        if template_name not in self.templates:
            raise ValueError(f"未找到模板: {template_name}")
        
        template = self.templates[template_name]
        
        # 添加系统提示词到kwargs
        kwargs["system_prompt"] = self.system_prompt
        
        try:
            return template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"模板 {template_name} 缺少参数: {e}")
    
    def update_template(self, template_name: str, template_content: str):
        """更新模板"""
        self.templates[template_name] = template_content
    
    def add_template(self, template_name: str, template_content: str):
        """添加新模板"""
        self.templates[template_name] = template_content
