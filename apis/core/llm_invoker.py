"""
LLM调用器模块

统一处理LLM调用逻辑，支持多种LLM客户端
"""

from typing import Union
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
from config.model_config import CustomRestfulLLM, clean_analysis_content
from utils.logger import logger
import os
import requests
from pathlib import Path


class LLMAPIException(Exception):
    """LLM API调用异常"""

    def __init__(self, message: str, error_type: str = None, original_error: Exception = None):
        self.message = message
        self.error_type = error_type
        self.original_error = original_error
        super().__init__(self.message)


class LLMInvoker:
    """LLM调用器，统一处理LLM调用逻辑"""
    
    def __init__(self, llm_client: Union[ChatOpenAI, CustomRestfulLLM]):
        self.llm_client = llm_client
    
    def invoke_with_prompt_info(self, prompt: str, task_name: str = "LLM推理", traceid: str = None) -> str:
        """
        调用LLM并输出prompt信息

        Args:
            prompt: 提示词
            task_name: 任务名称，用于日志显示
            traceid: 追踪ID

        Returns:
            清理后的LLM响应内容

        Raises:
            LLMAPIException: LLM API调用失败时抛出
        """
        # 保存prompt到文件
        with open(f"{task_name}prompt.txt", "w", encoding="utf-8") as f:
            f.write(prompt)
        logger.info(f"📏 {task_name} - Prompt总长度: {len(prompt)} 字符")

        try:
            logger.info(f"🤖 开始调用LLM API - {task_name}")
            response = self.llm_client.invoke([HumanMessage(content=prompt)])

            # 检查响应是否有效
            if not response or not hasattr(response, 'content') or not response.content:
                error_msg = f"LLM API返回空响应 - {task_name}"
                logger.info(f"❌ {error_msg}")
                raise LLMAPIException(
                    message=error_msg,
                    error_type="empty_response"
                )

            # 打印原始响应内容
            logger.info(f"🔍 原始响应内容:\n{response.content}")

            # 清理响应内容，移除 <think></think> 标签
            cleaned_content = clean_analysis_content(response.content)

            if response.content != cleaned_content:
                logger.info("⚠️ 检测到并移除了think标签内容")

            logger.info(f"✅ {task_name} 调用成功，响应长度: {len(cleaned_content)} 字符")
            return cleaned_content

        except Exception as e:
            # 检查是否是网络相关异常
            error_str = str(e).lower()
            if any(keyword in error_str for keyword in ['timeout', 'connection', 'network', 'request']):
                error_msg = f"LLM API调用失败 - {task_name}: {str(e)}"
                logger.info(f"❌ {error_msg}")
                raise LLMAPIException(
                    message=error_msg,
                    error_type="api_failure",
                    original_error=e
                )
            else:
                # 其他类型的异常直接重新抛出
                logger.info(f"❌ {task_name}失败: {str(e)}")
                raise

    def save_prompt(self, prompt, task_name, traceid=None):
        """保存prompt到指定目录，支持traceid，并在文件名中加入时间戳"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{task_name}prompt_{timestamp}.txt"
        if traceid:
            folder = Path("code") / "report" / str(traceid)
            folder.mkdir(parents=True, exist_ok=True)
            file_path = folder / filename
        else:
            file_path = Path(filename)
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(prompt)
        logger.info(f"📏 {task_name} - Prompt总长度: {len(prompt)} 字符, 路径: {file_path}")
