"""
错误恢复管理器模块

处理工具调用过程中的各种错误，提供自动恢复机制
"""

from typing import Dict, Any
from utils.logger import logger


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self, max_retries: int = 2):
        self.max_retries = max_retries
        self.error_patterns = {
            "parameter_error": self._handle_parameter_error,
            "tool_not_found": self._handle_tool_not_found,
            "timeout_error": self._handle_timeout_error,
            "json_parse_error": self._handle_json_parse_error
        }
        self.retry_counts = {}
    
    def should_retry(self, error: Exception, tool_name: str) -> bool:
        """判断是否应该重试"""
        retry_key = f"{tool_name}_{type(error).__name__}"
        current_retries = self.retry_counts.get(retry_key, 0)
        return current_retries < self.max_retries
    
    def handle_error(self, error: Exception, tool_name: str, 
                    arguments: Dict[str, Any]) -> Dict[str, Any]:
        """处理错误并尝试恢复"""
        retry_key = f"{tool_name}_{type(error).__name__}"
        current_retries = self.retry_counts.get(retry_key, 0)
        
        if current_retries >= self.max_retries:
            return {
                "status": "failed",
                "error": str(error),
                "recovery_attempted": True,
                "max_retries_reached": True
            }
        
        # 增加重试计数
        self.retry_counts[retry_key] = current_retries + 1
        
        # 分类错误并尝试恢复
        error_type = self._classify_error(error)
        
        if error_type in self.error_patterns:
            logger.info(f"🔄 尝试恢复错误: {error_type} (第{current_retries + 1}次重试)")
            return self.error_patterns[error_type](error, tool_name, arguments)
        
        return {
            "status": "failed",
            "error": str(error),
            "recovery_attempted": False,
            "error_type": error_type
        }
    
    def _classify_error(self, error: Exception) -> str:
        """分类错误类型"""
        error_msg = str(error).lower()
        
        if "parameter" in error_msg or "argument" in error_msg or "missing" in error_msg:
            return "parameter_error"
        elif "not found" in error_msg or "unknown tool" in error_msg:
            return "tool_not_found"
        elif "timeout" in error_msg or "time out" in error_msg:
            return "timeout_error"
        elif "json" in error_msg or "parse" in error_msg:
            return "json_parse_error"
        else:
            return "unknown_error"
    
    def _handle_parameter_error(self, error: Exception, tool_name: str, 
                               arguments: Dict[str, Any]) -> Dict[str, Any]:
        """处理参数错误"""
        # 简单的参数修正策略
        corrected_args = {}
        for key, value in arguments.items():
            if value is None or value == "":
                # 为空值提供默认值
                corrected_args[key] = "default_value"
            else:
                corrected_args[key] = value
        
        return {
            "status": "retry",
            "corrected_arguments": corrected_args,
            "error_type": "parameter_error",
            "original_error": str(error)
        }
    
    def _handle_tool_not_found(self, error: Exception, tool_name: str, 
                              arguments: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具未找到错误"""
        return {
            "status": "failed",
            "error": f"工具 {tool_name} 不存在",
            "suggestion": "请检查工具名称是否正确",
            "error_type": "tool_not_found"
        }
    
    def _handle_timeout_error(self, error: Exception, tool_name: str, 
                             arguments: Dict[str, Any]) -> Dict[str, Any]:
        """处理超时错误"""
        return {
            "status": "retry",
            "corrected_arguments": arguments,
            "error_type": "timeout_error",
            "suggestion": "工具执行超时，将重试"
        }
    
    def _handle_json_parse_error(self, error: Exception, tool_name: str, 
                                arguments: Dict[str, Any]) -> Dict[str, Any]:
        """处理JSON解析错误"""
        return {
            "status": "failed",
            "error": "JSON解析失败",
            "error_type": "json_parse_error",
            "suggestion": "检查工具返回的数据格式"
        }
    
    def reset_retries(self):
        """重置重试计数"""
        self.retry_counts.clear()
