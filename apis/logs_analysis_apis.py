"""
日志分析API模块

提供日志错误位置分析和问题诊断功能，支持think标签清理和自定义工具调用
"""

# 导入拆分后的模块
from apis.core.llm_invoker import LLMInvoker
from apis.extractors.data_extractors import (
    FileNameExtractor,
    TableNameExtractor,
    InterfaceNameExtractor,
    ServerTimeInfoExtractor
)
from apis.callers.tool_callers import ReActToolCaller, ToolCaller
from utils.logger import logger

import re
import json
from typing import Dict, Any, Tuple, List, Union
from langchain_openai import ChatOpenAI
from config.model_config import CustomRestfulLLM



class LogAnalyzer:
    """日志分析器主类"""

    def __init__(self, llm_client: Union[ChatOpenAI, CustomRestfulLLM], use_react: bool = False, max_react_iterations: int = 5):
        """
        初始化日志分析器

        Args:
            llm_client: LLM客户端
            use_react: 是否使用ReAct模式进行工具调用，默认False
            max_react_iterations: ReAct模式的最大迭代次数，默认5
        """
        self.llm_invoker = LLMInvoker(llm_client)

        # 根据配置选择工具调用器
        if use_react:
            self.tool_caller = ReActToolCaller(self.llm_invoker, max_react_iterations)
            logger.info(f"🧠 已启用ReAct模式工具调用器 (最大迭代次数: {max_react_iterations})")
        else:
            self.tool_caller = ToolCaller(self.llm_invoker)
            logger.info("🔧 已启用标准工具调用器")

        self.use_react = use_react
        self.file_name_extractor = FileNameExtractor()
        self.table_name_extractor = TableNameExtractor()
        self.interface_name_extractor = InterfaceNameExtractor()
        self.server_time_extractor = ServerTimeInfoExtractor()

    def analyze_error_locations(self, log_content: str) -> Dict[str, Any]:
        """分析日志内容，提取错误/异常位置信息"""
        prompt = self._build_error_location_prompt(log_content)
        cleaned_result = ""

        try:
            cleaned_result = self.llm_invoker.invoke_with_prompt_info(prompt, "错误位置分析")

            logger.info("\n==== 错误位置分析结果 ====")
            logger.info(f"结果长度: {len(cleaned_result)}")
            logger.info(cleaned_result)
            logger.info("==== 分析结束 ====\n")

            # 尝试解析JSON结果
            json_match = re.search(r'\{.*\}', cleaned_result, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            return json.loads(cleaned_result)

        except Exception as e:
            logger.info(f"⚠️ 错误位置分析失败: {str(e)}")
            if cleaned_result:
                logger.info(f"尝试解析的内容: {repr(cleaned_result[:200])}")
                return {"raw_analysis": cleaned_result, "error": str(e)}
            else:
                return {"raw_analysis": "LLM调用失败，未获取到响应", "error": str(e)}

    def extract_file_names(self, error_locations: Dict[str, Any]) -> Tuple[List[Dict[str, str]], str]:
        """提取文件名（附带app_code信息）"""
        return self.file_name_extractor.extract(error_locations)

    def extract_table_names(self, error_locations: Dict[str, Any]) -> Tuple[List[Dict[str, str]], str]:
        """提取表名（附带app_code信息）"""
        return self.table_name_extractor.extract(error_locations)

    def extract_interface_names(self, error_locations: Dict[str, Any]) -> Tuple[List[Dict[str, str]], str]:
        """提取接口名（附带app_code信息）"""
        return self.interface_name_extractor.extract(error_locations)

    def extract_server_time_info(self, error_locations: Dict[str, Any]) -> Dict[str, Any]:
        """提取服务器和时间信息"""
        return self.server_time_extractor.extract(error_locations)

    def call_tools(self, tools: List, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        调用工具

        Args:
            tools: 工具列表
            query: 用户查询
            context: 额外的上下文信息，会传递给工具调用器

        Returns:
            包含执行结果的字典，ReAct模式会包含额外的对话历史和推理过程
        """
        result = self.tool_caller.call_tools(tools, query, context=context)

        # 如果使用ReAct模式，打印详细的执行总结
        if self.use_react and "final_summary" in result:
            logger.info("\n" + "="*60)
            logger.info("🎯 ReAct执行完成 - 详细总结")
            logger.info("="*60)
            logger.info(result["final_summary"])
            logger.info("="*60)

            # 显示关键迭代信息
            if "exit_reason_formatted" in result:
                logger.info(f"\n📊 迭代执行详情:")
                logger.info(f"- 实际执行: {result.get('iterations', 0)}/{result.get('max_iterations', 0)} 次迭代")
                logger.info(f"- 退出原因: {result['exit_reason_formatted']}")
                logger.info(f"- 工具成功率: {(result['summary']['successful_tools']/result['summary']['total_tools_used']*100) if result['summary']['total_tools_used'] > 0 else 0:.1f}%")

            # 显示推理过程概览
            if "conversation_history" in result:
                reasoning_steps = [h for h in result["conversation_history"] if h[0] == "reasoning"]
                if reasoning_steps:
                    logger.info(f"\n🧠 推理过程概览 ({len(reasoning_steps)} 个推理步骤):")
                    for i, (_, reasoning) in enumerate(reasoning_steps, 1):
                        logger.info(f"\n推理步骤 {i}:")
                        logger.info(f"{reasoning[:200]}..." if len(reasoning) > 200 else reasoning)

        return result

    def analyze_problem(self, traceid: str, log_content: str, error_locations: Dict[str, Any],
                       tool_results: str) -> str:
        """生成综合问题分析报告"""
        prompt = self._build_problem_analysis_prompt(traceid, log_content, error_locations, tool_results)
        
        # 新增：先保存prompt
        self.llm_invoker.save_prompt(prompt, "问题分析", traceid)
        
        try:
            analysis_report = self.llm_invoker.invoke_with_prompt_info(prompt, "问题分析")

            logger.info("\n==== 问题分析结果 ====")
            logger.info(f"结果长度: {len(analysis_report)}")
            logger.info(analysis_report)
            logger.info("==== 分析结束 ====\n")

            return analysis_report

        except Exception as e:
            logger.info(f"⚠️ 问题分析失败: {str(e)}")
            return f"""# 系统故障分析报告

## 概述
TraceID: {traceid}
发现问题数量: 0
分析时间: 未知

## 错误信息
分析过程中发生错误: {str(e)}

## 总结
由于分析过程中发生错误，无法生成完整的分析报告。请检查系统配置和网络连接。
"""

    def _build_error_location_prompt(self, log_content: str) -> str:
        """构建错误位置分析提示词"""
        return """你是一位经验丰富的日志分析专家。请分析以下日志内容，精确提取所有异常/缓慢发生的起源点位置信息和异常传播链。

任务要求：
1. **起源点定位**：识别所有异常/缓慢发生的起源点具体位置，包括文件名、类名、方法名和行号，或具体代码（包括SQL语句），以及起源点涉及的中间件
2. **异常传播链提取**：追踪异常从起源点到最终表现的完整传播路径，记录每个传播节点的位置信息
3. **多层次分析**：前端和后端的问题都需要准确识别，后端问题可能涉及.java文件、服务接口地址，前端问题通常涉及.js文件，SQL语句问题可能涉及xml文件
4. **结构化输出**：以JSON格式返回结果，不要有任何其他内容
5. **性能问题处理**：如果是性能缓慢问题，请记录耗时信息和性能瓶颈点
6. **SQL问题处理**：如果是SQL问题，请提取SQL语句中涉及的表名，放入table_name_list字段
7. **服务和时间信息提取**：精确提取问题发生的服务名称（servername）和时间信息（timestamp），包括开始时间、结束时间或持续时间

期望的JSON格式：
{
  "issues": [
    {
      "issue_type": "异常或缓慢",
      "root_cause": {
        "origin_point": {
          "file_name": "起源点文件名.扩展名",
          "class_name": "起源点类名",
          "method_name": "起源点方法名",
          "line_number": 起源点行号,
          "service_url": "起源点接口,Controller或Handler中常规写法，如/api/v1/user/list",
          "sql": "起源点SQL语句",
          "table_name_list": ["表名1", "表名2", "表名3"],
          "app_code": "起源点服务名称(该节点servername字段)",
          "middleware": "起源点涉及的中间件，如Redis，kafka，RabbitMq，Mysql，人大金仓，Easy Search，MangoDB"
          "timestamp": {
            "start_time": "起源点开始时间",
            "end_time": "起源点结束时间",
            "duration_ms": "起源点持续时间(毫秒)",
            "occurrence_time": "起源点发生时间"
          }
        },
        "error_description": "起源点错误描述"
      },
      "propagation_chain": [
        {
          "sequence": 1,
          "location": {
            "file_name": "传播节点1文件名.扩展名",
            "class_name": "传播节点1类名",
            "method_name": "传播节点1方法名",
            "line_number": 传播节点1行号,
            "service_url": "传播节点1接口",
            "app_code": "传播节点1服务名称(该节点servername字段)",
            "timestamp": {
              "start_time": "传播节点1开始时间",
              "end_time": "传播节点1结束时间",
              "duration_ms": "传播节点1持续时间(毫秒)",
              "occurrence_time": "传播节点1发生时间"
            }
          },
          "propagation_type": "调用/抛出/传递",
          "error_transformation": "错误在此节点的变化描述"
        },
        {
          "sequence": 2,
          "location": {
            "file_name": "传播节点2文件名.扩展名",
            "class_name": "传播节点2类名",
            "method_name": "传播节点2方法名",
            "line_number": 传播节点2行号,
            "service_url": "传播节点2接口",
            "app_code": "传播节点2服务名称(该节点servername字段)",
            "timestamp": {
              "start_time": "传播节点2开始时间",
              "end_time": "传播节点2结束时间",
              "duration_ms": "传播节点2持续时间(毫秒)",
              "occurrence_time": "传播节点2发生时间"
            }
          },
          "propagation_type": "调用/抛出/传递",
          "error_transformation": "错误在此节点的变化描述"
        }
      ],
      "final_manifestation": {
        "location": {
          "file_name": "最终表现位置文件名.扩展名",
          "class_name": "最终表现位置类名",
          "method_name": "最终表现位置方法名",
          "line_number": 最终表现位置行号,
          "service_url": "最终表现位置接口",
          "app_code": "最终表现位置服务名称(该节点servername字段)",
          "timestamp": {
            "start_time": "最终表现位置开始时间",
            "end_time": "最终表现位置结束时间",
            "duration_ms": "最终表现位置持续时间(毫秒)",
            "occurrence_time": "最终表现位置发生时间"
          }
        },
        "error_type": "最终错误类型",
        "error_info": "最终错误关键信息",
        "duration": "耗时(毫秒)",
        "impact_scope": "影响范围描述"
      }
    },
    ...更多问题...
  ]
}

分析重点：
- **起源点识别**：找到问题的真正源头，不是症状表现点
- **传播路径**：完整追踪异常如何从起源点传播到最终表现
- **调用链分析**：分析方法调用关系，识别异常传播的每个环节
- **性能瓶颈**：对于缓慢问题，识别真正的性能瓶颈点
- **依赖关系**：分析组件间依赖关系，找出异常传播的关键路径
- **服务信息提取**：准确识别问题发生的服务名称，可能出现在日志的服务名、主机名、应用名等字段中
- **时间信息提取**：精确提取时间戳，包括请求开始时间、异常发生时间、处理结束时间，计算准确的耗时
- **各信息提取**：所有信息需从日志中精确提取，如果没有找到则保留""的形式即可，不要添加任何人为分析的内容

以下是需要分析的日志内容：
""" + log_content

    def _build_problem_analysis_prompt(self, traceid: str, log_content: str,
                                     error_locations: Dict[str, Any], tool_results: str) -> str:
        """构建问题分析提示词"""
        return f"""你是一位资深的Java应用性能诊断与故障排查专家，擅长从日志中精准定位问题根因并提供系统级解决方案。

请基于以下信息对系统故障进行分析，并提供专业的诊断报告：

===== TraceID =====
{traceid}

===== 错误位置信息 =====
{json.dumps(error_locations, ensure_ascii=False, indent=2)}

===== 代码上下文 =====
{tool_results}

## 分析要求
1. 错误位置信息包含了起源点，异常调用链，最终表现的相关信息，识别错误位置信息中每一个来自起源点的错误/异常，为每个根源问题提供单独的分析
2. 重点基于代码上下文和错误位置信息中的真实内容进行分析和提供修改建议
3. 强调一定要基于真实的信息做分析而非臆测
4. 如果信息不足，请明确说明"信息不足"而非推测

## 输出格式
请以文本形式输出分析报告，按以下结构组织：

# 系统故障分析报告

## 概述
TraceID: {traceid}
发现问题数量: [根据分析确定]
分析时间: [当前时间]

## 问题详细分析

### 问题1: [问题标题]
**问题类型**: [性能问题/功能异常]
**问题位置**: [完整包名.类名.方法名:行号]
**发生时间**: [从日志提取的时间信息]
**执行耗时**: [对于性能问题，显示具体耗时]

**异常表现**:
[基于错误位置信息的具体描述]

**根因分析**:
[基于真实代码上下文和错误位置信息的分析，不要臆测]

**解决方案**:
- 紧急修复: [立即可执行的解决方案]
- 长期解决: [根本性解决方案]
- 代码修改建议: [基于真实代码上下文的具体修改建议，如无代码上下文则说明]
- 预防措施: [避免类似问题的措施]

---

[如有多个问题，按同样格式继续分析]

## 总结
[整体问题总结和建议]

注意：所有分析必须基于提供的真实信息，不得进行臆测。如果某部分信息不足，请明确说明。
"""














