# 大模型线上资源：
import requests
import json

def read_content_from_file(file_path):
    """从文件中读取内容"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().strip()

def test_llm_api(model_type='qwen', content_file='prompt.txt'):
    """测试大模型API
    Args:
        model_type: 模型类型,可选qwen或deepseek
        content_file: 提示词文件路径
    """
    # 从文件读取content内容
    content = read_content_from_file(content_file)
    
    # API配置
    api_config = {
        'qwen': {
            'url': 'https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/QWEN3-32B/completions',
            'model': 'qwen3-32B'
        },
        'deepseek': {
            'url': 'https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/DEEPSEEK-R1-671B/completions',
            'model': 'DeepSeek-R1'
        }
    }
    
    # 构建请求
    headers = {
        'Content-Type': 'application/json'
    }
    
    params = {
        'access_token': '4958809802354e0d92ca7aa51d393c72'
    }
    
    data = {
        'model': api_config[model_type]['model'],
        'messages': [{
            'role': 'user',
            'content': content
        }],
        'stream': False,
        'temperature': 0.6
    }
    
    # 发送请求
    response = requests.post(
        api_config[model_type]['url'],
        headers=headers,
        params=params,
        json=data
    )
    
    print(f'请求结果: {response.text}')

if __name__ == '__main__':
    # 测试qwen模型
    # test_llm_api('qwen', 'prompt.txt')
    
    # 测试deepseek模型
    test_llm_api('deepseek', '问题分析prompt0.txt')