from typing import Optional
from pydantic import BaseModel, Field, model_validator

class LogProcessDashboardParams(BaseModel):
    """日志处理仪表盘查询参数模型"""
    trace_id: Optional[str] = Field(default="", description="traceID")
    log_type: Optional[str] = Field(default="", description="异常分类")
    solution_status: Optional[str] = Field(default="", description="方案状态")
    solution_confirm_status: Optional[str] = Field(default="", description="方案确认状态")
    start_time: Optional[str] = Field(default="", description="方案生成开始时间，格式如：2025-06-15 11:02:21")
    end_time: Optional[str] = Field(default="", description="方案生成结束时间，格式如：2025-06-16 11:02:21")
    page_num: int = Field(default=1, description="页码，默认为1")
    page_size: int = Field(default=10, description="每页条数，默认为10")

    @model_validator(mode='after')
    def validate_time_range(self):
        """验证时间范围的合法性"""
        if self.start_time and not self.end_time:
            raise ValueError("开始时间存在时，结束时间不能为空")
        if self.end_time and not self.start_time:
            raise ValueError("结束时间存在时，开始时间不能为空")
        return self
