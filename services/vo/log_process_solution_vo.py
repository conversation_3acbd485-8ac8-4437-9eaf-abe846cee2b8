from dataclasses import dataclass
from typing import Optional

@dataclass
class LogProcessSolution:
    trace_id: str
    request_time: str
    error_info: Optional[str]
    log_type: Optional[str]
    current_stage: Optional[str]
    start_time: Optional[str]
    end_time: Optional[str]
    solution_status: Optional[str]
    user_name: Optional[str]
    solution_confirm_status: Optional[str]
    solution_issued_status: Optional[str]
    version: Optional[str]
    status: Optional[str]
    generate_time: Optional[str]
