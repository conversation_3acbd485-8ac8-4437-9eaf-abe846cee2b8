# log_handle_service.py
import json

from dotenv import load_dotenv

from common.common_enum import LogProcessStatus
from services.db.issue_service import IssueService
from services.db.log_process_service import LogProcessService
from services.db.solution_service import SolutionService
from utils.logger import logger

load_dotenv()


class LogStoreService:
    def __init__(self):
        self.issue_service = IssueService()
        self.solution_service = SolutionService()
        self.log_process_service = LogProcessService()

    def save_root_cause(self, trace_id, error_locations):
        """"根因落库"""
        try:
            logger.info(f"开始根因落库:{trace_id}")
            log_process = self.log_process_service.get_log_process_by_trace_id(trace_id)
            trace_hash = ""
            if log_process:
                trace_hash = log_process.trace_hash
                fields_to_update = {
                    "status": LogProcessStatus.AI_PROCESSING_DONE_RCA.value,
                }
                self.log_process_service.update_common(log_process, fields_to_update )
            for error_location in error_locations["issues"]:
                issue = self.root_cause_to_dict(error_location, trace_id=trace_id, trace_hash=trace_hash)
                self.issue_service.create_issue(issue)
        except Exception as e:
            logger.exception(f"根因落库失败：{str(e)}")

    def save_solution(self, trace_id, analysis_report):
        """解决方案落库"""
        try:
            logger.info(f"开始解决方案落库:{trace_id}")
            log_process = self.log_process_service.get_log_process_by_trace_id(trace_id)
            if log_process:
                trace_hash = log_process.trace_hash
                fields_to_update = {
                    "status": LogProcessStatus.AI_PROCESSING_DONE_REPORT.value
                }
                self.log_process_service.update_common(log_process, fields_to_update)

            solution = self.solution_service.get_solution_by_trace_id_and_version(trace_id, log_process.version)
            fields_to_update = {"raw_json": json.dumps(analysis_report, ensure_ascii=False, indent=2)}
            self.solution_service.update_solution_fields(solution, fields_to_update)
        except Exception as e:
            logger.exception(f"解决方案落库失败：{str(e)}")

    def root_cause_to_dict(self, data: dict, trace_id: str = None, trace_hash: str = None):
        root_cause = data.get("root_cause", {})
        origin_point = root_cause.get("origin_point", {})
        op_timestamp = origin_point.get("timestamp", {})

        final_manifestation = data.get("final_manifestation", {})
        fm_location = final_manifestation.get("location", {})
        fm_timestamp = fm_location.get("timestamp", {})

        return {
            "trace_hash" : trace_hash,
            "issue_type" : data.get("issue_type"),
            "trace_id" : trace_id,

            # root_cause.origin_point
            "rc_file_name" : origin_point.get("file_name"),
            "rc_class_name" : origin_point.get("class_name"),
            "rc_method_name" : origin_point.get("method_name"),
            "rc_line_number" : origin_point.get("line_number"),
            "rc_service_url" : origin_point.get("service_url"),
            "rc_sql" : origin_point.get("sql"),
            "rc_table_names" : ",".join(origin_point.get("table_name_list")) if origin_point.get(
                "table_name_list") else "",
            "rc_app_code" : origin_point.get("app_code"),
            "rc_middleware" : origin_point.get("middleware"),
            "rc_start_time" : op_timestamp.get("start_time"),
            "rc_end_time" : op_timestamp.get("end_time"),
            "rc_duration_ms" : op_timestamp.get("duration_ms"),
            "rc_occurrence_time" : op_timestamp.get("occurrence_time"),
            "rc_error_description" : root_cause.get("error_description"),

            # final_manifestation.location
            "fm_file_name" : fm_location.get("file_name"),
            "fm_class_name" : fm_location.get("class_name"),
            "fm_method_name" : fm_location.get("method_name"),
            "fm_line_number" : fm_location.get("line_number"),
            "fm_service_url" : fm_location.get("service_url"),
            "fm_app_code" : fm_location.get("app_code"),

            # final_manifestation.location.timestamp
            "fm_start_time" : fm_timestamp.get("start_time"),
            "fm_end_time" : fm_timestamp.get("end_time"),
            "fm_duration_ms" : fm_timestamp.get("duration_ms"),
            "fm_occurrence_time" : fm_timestamp.get("occurrence_time"),

            # final_manifestation 其他字段
            "fm_error_type" : final_manifestation.get("error_type"),
            "fm_error_info" : final_manifestation.get("error_info"),
            "fm_duration" : final_manifestation.get("duration"),
            "fm_impact_scope" : final_manifestation.get("impact_scope"),
            "raw_json" : json.dumps(data, ensure_ascii=False),
            "tenant_id" : ""
        }

# if __name__ == "__main__":
#     log_store = LogStoreService()
#     log_store.run()
