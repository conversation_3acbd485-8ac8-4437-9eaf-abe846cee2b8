import json
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod
from dataclasses import dataclass

# 导入自定义模块
from config.model_config import ModelConfig, CustomRestfulLLM
from utils.logger import logger
from apis.log_preprocess_apis import LogPreprocessor
from apis.logs_analysis_apis import LogAnalyzer
# from services.log_store_service import LogStoreService
from tools.code_tools import extract_tool_results
from tools.app_code_tools import search_code_by_app_code
from tools.ddl_info_tools import get_table_ddl_info
from apis.extractors.error_location_enhancer import ErrorLocationEnhancer


@dataclass
class TraceAnalysisRequest:
    """外部分析请求数据模型"""
    id: str
    trace_id: str
    request: str
    request_time: str
    sys_code: str
    count: int
    log_type: str
    job_trigger_id: str
    status: str
    user_id: str
    trace_data: Dict[str, Any]  # 相当于DataFetchStep的输出结果
    log_data: Optional[Dict[str, Any]] = None
    tenant_id: Optional[str] = None
    version: Optional[str] = None
    create_user: Optional[str] = None
    create_time: Optional[str] = None
    last_modify_user: Optional[str] = None
    last_modify_time: Optional[str] = None
    comment: Optional[str] = None
    solution_id: Optional[int] = None
    trace_hash: Optional[str] = None
    source_type: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TraceAnalysisRequest':
        """从字典创建实例"""
        return cls(**data)

class FileManager:
    """文件管理器，统一处理文件保存逻辑"""
    
    def __init__(self, base_path: str = None, enable_debug: bool = False):
        self.base_path = Path(base_path) if base_path else Path(__file__).parent.parent
        self.enable_debug = enable_debug
        self.generated_files = []
    
    def save_json(self, data: Dict[str, Any], filename: str, traceid: str = None) -> bool:
        """保存JSON数据到文件"""
        if not self.enable_debug:
            return False
        
        try:
            # 如果提供了traceid，创建对应的文件夹
            if traceid:
                folder_path = self.base_path / "code" / "report" / traceid
                folder_path.mkdir(parents=True, exist_ok=True)
                file_path = folder_path / filename
            else:
                file_path = self.base_path / filename
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ 已保存JSON文件: {file_path}")
            self.generated_files.append(str(file_path))
            return True
        except Exception as e:
            logger.info(f"❌ 保存JSON文件失败: {str(e)}")
            return False
    
    def save_text(self, content: str, filename: str, traceid: str = None) -> bool:
        """保存文本内容到文件"""
        if not self.enable_debug:
            return False
        
        try:
            # 如果提供了traceid，创建对应的文件夹
            if traceid:
                folder_path = self.base_path / "code" / "report" / traceid
                folder_path.mkdir(parents=True, exist_ok=True)  
                file_path = folder_path / filename
            else:
                file_path = self.base_path / filename
                
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ 已保存文本文件: {file_path}")
            self.generated_files.append(str(file_path))
            return True
        except Exception as e:
            logger.info(f"❌ 保存文本文件失败: {str(e)}")
            return False
    
    def get_generated_files(self) -> List[str]:
        """获取已生成的文件列表"""
        return self.generated_files.copy()


class AnalysisStep(ABC):
    """分析步骤抽象基类"""
    
    def __init__(self, name: str, step_number: int, total_steps: int):
        self.name = name
        self.step_number = step_number
        self.total_steps = total_steps
    
    @abstractmethod
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行分析步骤"""
        ...
    
    def log_start(self):
        """记录步骤开始"""
        logger.info(f"\n[{self.step_number}/{self.total_steps}] {self.name}...")
    
    def log_success(self, message: str = ""):
        """记录步骤成功"""
        if message:
            logger.info(f"✅ {message}")
        else:
            logger.info(f"✅ {self.name}完成")
    
    def log_error(self, error: str):
        """记录步骤错误"""
        logger.info(f"❌ {self.name}失败: {error}")


class DataFetchStep(AnalysisStep):
    """数据获取步骤（仅开发调试模式使用）"""
    
    def __init__(self, preprocessor: LogPreprocessor, step_number: int = 1, total_steps: int = 5):
        super().__init__("获取日志数据", step_number, total_steps)
        self.preprocessor = preprocessor
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()
        
        time = context.get("time")
        traceid = context.get("traceid")
        authorization = context.get("authorization")
        show_debug = context.get("show_debug", False)
        
        # 首先尝试从API获取数据
        log_data = self.preprocessor.fetch_log_data(traceid, time, authorization)
        api_success = log_data is not None
        
        # 检查API获取的数据是否有效并保存（仅在调试模式下）
        if api_success and show_debug:
            file_manager = context.get("file_manager")
            if file_manager:
                file_manager.save_json(log_data, f'log_{traceid}.json', traceid)
        
        # 如果API获取失败，尝试本地缓存
        if not api_success:
            logger.info("❌ API获取失败，尝试使用本地缓存数据")
            log_data = self.preprocessor.fetch_log_data(traceid, time, source_priority=['local'])
            
            if log_data is not None:
                logger.info(f"✅ 成功从本地缓存读取trace数据: {traceid}")
        
        # 确保成功获取到日志数据
        if log_data is None:
            error_msg = "无法获取日志数据，请检查网络连接、认证Token或Trace ID"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
        
        self.log_success("已成功获取日志数据")
        return {"success": True, "log_data": log_data}


class DataFilterStep(AnalysisStep):
    """数据过滤步骤"""
    
    def __init__(self, preprocessor: LogPreprocessor, step_number: int = 2, total_steps: int = 5):
        super().__init__("过滤日志内容", step_number, total_steps)
        self.preprocessor = preprocessor
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()
        
        log_data = context.get("log_data")
        if not log_data:
            error_msg = "缺少日志数据"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
        
        try:
            log_content = self.preprocessor.filter_log_data(log_data)
            logger.info(f"过滤后的日志内容长度: {len(log_content)} 字符")
            
            # 保存过滤后的日志内容（仅在调试模式下）
            if context.get("show_debug", False):
                file_manager = context.get("file_manager")
                trace_data = context.get("log_data")
                proactivate_log_data = context.get("proactivate_log_data")
                if file_manager:
                    timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
                    traceid = context.get("traceid")
                    
                    # 根据log_content的类型选择保存方法
                    # filter方法返回的是JSON字符串，应该用save_text保存
                    if isinstance(log_content, str):
                        file_manager.save_text(log_content, f'filtered_log_{traceid}_{timestamp}.json', traceid)
                    else:
                        file_manager.save_json(log_content, f'filtered_log_{traceid}_{timestamp}.json', traceid)
                    
                    # 保存原始数据（如果存在）
                    if trace_data:
                        file_manager.save_json(trace_data, f'trace_data_{traceid}_{timestamp}.json', traceid)
                    if proactivate_log_data:
                        file_manager.save_json(proactivate_log_data, f'proactivate_log_data_{traceid}_{timestamp}.json', traceid)
            
            self.log_success()
            return {"success": True, "log_content": log_content}
            
        except Exception as e:
            error_msg = f"过滤日志内容失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}


class ErrorLocationAnalysisStep(AnalysisStep):
    """错误位置分析步骤"""
    
    def __init__(self, analyzer: LogAnalyzer, step_number: int = 3, total_steps: int = 4):
        super().__init__("分析错误位置", step_number, total_steps)
        self.analyzer = analyzer
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()
        
        log_content = context.get("log_content")
        if not log_content:
            error_msg = "缺少日志内容"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
        
        try:
            error_locations = self.analyzer.analyze_error_locations(log_content)
            
            # 保存错误位置分析结果（仅在调试模式下）
            if context.get("show_debug", False):
                file_manager = context.get("file_manager")
                if file_manager:
                    timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
                    traceid = context.get("traceid")
                    file_manager.save_json(error_locations, f'error_locations_{traceid}_{timestamp}.json', traceid)
            
            # 提取文件名、表名和接口名（现在都包含app_code信息）
            file_names_list, file_names_str = self.analyzer.extract_file_names(error_locations)
            table_names_list, table_names_str = self.analyzer.extract_table_names(error_locations)
            interface_names_list, interface_names_str = self.analyzer.extract_interface_names(error_locations)
            
            logger.info(f"发现的文件: {file_names_str}")
            logger.info(f"发现的表名: {table_names_str}")
            logger.info(f"发现的接口: {interface_names_str}")
            
            self.log_success()
            return {
                "success": True,
                "error_locations": error_locations,
                "file_names_list": file_names_list,
                "file_names_str": file_names_str,
                "table_names_list": table_names_list,
                "table_names_str": table_names_str,
                "interface_names_list": interface_names_list,
                "interface_names_str": interface_names_str
            }
            
        except Exception as e:
            error_msg = f"分析错误位置失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
    



class CodeContextStep(AnalysisStep):
    """获取代码上下文步骤"""
    
    def __init__(self, analyzer: LogAnalyzer, step_number: int = 5, total_steps: int = 6):
        super().__init__("获取代码上下文", step_number, total_steps)
        self.analyzer = analyzer
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()
        
        error_locations = context.get("error_locations")
        if not error_locations:
            error_msg = "缺少错误位置信息"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
        
        try:
            # 创建工具列表
            tools = [search_code_by_app_code, get_table_ddl_info]
            
            # 构建查询语句
            query = self._build_code_query(context, error_locations)
            
            logger.info("开始查询代码...")
            logger.info("使用自定义工具调用实现（基于 RESTful API）...")
            
            # 使用自定义工具调用实现，传递 context
            result = self.analyzer.call_tools(tools, query, context=context)
            logger.info("工具调用完成")
            
            # 保存原始结果（仅在调试模式下）
            if context.get("show_debug", False):
                file_manager = context.get("file_manager")
                if file_manager:
                    timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
                    traceid = context.get("traceid")
                    file_manager.save_text(str(result), f'agent_invoke_result_{traceid}_{timestamp}.txt', traceid)
            
            # 从结果中提取工具调用的结果
            tool_results = extract_tool_results(result)
            
            # 保存代码上下文结果（仅在调试模式下）
            if context.get("show_debug", False):
                file_manager = context.get("file_manager")
                if file_manager:
                    timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
                    traceid = context.get("traceid")
                    file_manager.save_text(tool_results, f'code_context_results_{traceid}_{timestamp}.txt', traceid)
            
            self.log_success()
            return {"success": True, "tool_results": tool_results}
            
        except Exception as e:
            error_msg = f"获取代码上下文失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
    
    def _build_code_query(self, context: Dict[str, Any], error_locations: Dict[str, Any]) -> str:
        """动态构建代码查询语句，根据错误位置信息的内容智能生成"""
        file_names_str = context.get("file_names_str", "")
        table_names_str = context.get("table_names_str", "")
        interface_names_str = context.get("interface_names_str", "")
        
        # 准备查询，包含已经分析出的错误位置信息
        error_locations_str = json.dumps(error_locations, ensure_ascii=False, indent=2)
        
        # 分析错误位置信息中的具体内容
        analysis_info = self._analyze_error_locations_content(error_locations)
        
        # 动态构建查询指导语句
        query_guidance = self._build_dynamic_query_guidance(
            file_names_str, table_names_str, interface_names_str, analysis_info
        )
        logger.info(f"动态构建的代码上下文查询指导语句：{query_guidance}")
        return f"""根据以下已分析出的错误位置信息，请智能选择合适的工具进行代码上下文查找：

{error_locations_str}

{query_guidance}

请根据错误位置信息中的具体内容，选择最相关的查询策略。"""
    
    def _analyze_error_locations_content(self, error_locations: Dict[str, Any]) -> Dict[str, Any]:
        """分析错误位置信息的内容特征"""
        analysis_info = {
            "has_sql": False,
            "has_java_files": False,
            "has_js_files": False,
            "has_xml_files": False,
            "has_specific_methods": False,
            "has_line_numbers": False,
            "has_service_urls": False,
            "has_middleware": False,
            "sql_fragments": [],
            "file_types": set(),
            "middleware_types": set()
        }
        
        def analyze_location_data(data):
            """递归分析位置数据"""
            if isinstance(data, dict):
                # 检查SQL
                if "sql" in data and data["sql"]:
                    analysis_info["has_sql"] = True
                    analysis_info["sql_fragments"].append(data["sql"])
                
                # 检查文件名
                if "file_name" in data and data["file_name"]:
                    file_name = data["file_name"]
                    if file_name.endswith('.java'):
                        analysis_info["has_java_files"] = True
                        analysis_info["file_types"].add("java")
                    elif file_name.endswith('.js'):
                        analysis_info["has_js_files"] = True
                        analysis_info["file_types"].add("js")
                    elif file_name.endswith('.xml'):
                        analysis_info["has_xml_files"] = True
                        analysis_info["file_types"].add("xml")
                
                # 检查方法名和行号
                if "method_name" in data and data["method_name"]:
                    analysis_info["has_specific_methods"] = True
                if "line_number" in data and data["line_number"]:
                    analysis_info["has_line_numbers"] = True
                
                # 检查服务URL
                if "service_url" in data and data["service_url"]:
                    analysis_info["has_service_urls"] = True
                
                # 检查中间件
                if "middleware" in data and data["middleware"]:
                    analysis_info["has_middleware"] = True
                    analysis_info["middleware_types"].add(data["middleware"])
                
                # 递归检查嵌套结构
                for value in data.values():
                    analyze_location_data(value)
                    
            elif isinstance(data, list):
                for item in data:
                    analyze_location_data(item)
        
        analyze_location_data(error_locations)
        return analysis_info
    
    def _build_dynamic_query_guidance(self, file_names_str: str, table_names_str: str, 
                                    interface_names_str: str, analysis_info: Dict[str, Any]) -> str:
        """根据分析信息动态构建查询指导语句"""
        guidance_parts = []
        
        # 根据不同情况组合生成指导语
        has_sql = analysis_info.get("has_sql", False)
        
        # 情况1：有SQL且有表名，但没有文件名 -> 只提示使用DDL工具
        if has_sql and table_names_str and not file_names_str:
            guidance_parts.append(f"🗃️ **表结构查找**：发现涉及表({table_names_str})，如果判断为索引问题可使用 get_table_ddl_info 工具查找这些表的索引定义")
        
        # 情况2：有SQL且有表名，且有文件名 -> 提示使用DDL工具和代码查找工具
        elif has_sql and table_names_str and file_names_str:
            guidance_parts.append(f"🗃️ **表结构查找**：发现涉及表({table_names_str})，如果判断为索引问题可使用 get_table_ddl_info 工具查找这些表的索引定义")
            
            file_types = list(analysis_info["file_types"])
            if file_types:
                file_types_str = "、".join(file_types)
                guidance_parts.append(f"📁 **文件上下文查找**：发现 {file_types_str} 类型文件({file_names_str})，可使用 search_code_by_app_code 工具查找这些文件的具体内容")
            else:
                guidance_parts.append(f"📁 **文件上下文查找**：发现文件({file_names_str})，可使用 search_code_by_app_code 工具查找相关代码")
        
        # 情况3：有文件名但不符合上述SQL+表名的情况
        elif file_names_str:
            file_types = list(analysis_info["file_types"])
            if file_types:
                file_types_str = "、".join(file_types)
                guidance_parts.append(f"📁 **文件上下文查找**：发现 {file_types_str} 类型文件({file_names_str})，可使用 search_code_by_app_code 工具查找这些文件的具体内容")
            else:
                guidance_parts.append(f"📁 **文件上下文查找**：发现文件({file_names_str})，可使用 search_code_by_app_code 工具查找相关代码")
        
         # 情况4：除了上述两种情况外，有接口名 -> 提示使用代码查找工具
        elif interface_names_str:
            guidance_parts.append(f"🌐 **接口实现查找**：发现服务接口({interface_names_str})，可使用 search_code_by_app_code 工具查找对应的Controller或Handler实现")
        
        # 情况5：有服务URL但没有具体接口名
        elif analysis_info.get("has_service_urls"):
            guidance_parts.append(f"🌐 **接口实现查找**：包含服务接口地址，可查找对应的Controller或Handler实现")
        
        # 如果没有找到特定内容，提供通用指导
        if not guidance_parts:
            guidance_parts.append(f"🔍 **通用查找**：可以基于错误信息中的请求url使用search_code_by_app_code 工具进行代码追踪")
        
        # 添加查询策略建议
        # strategy_guidance = "\n\n**建议查询策略**："
        # if analysis_info.get("has_sql") and table_names_str:
        #     strategy_guidance += "\n1. 优先查找涉及的表DDL结构"
            
        # elif file_names_str:
        #     strategy_guidance += f"\n1. 直接查找错误文件的代码实现"
        #     if analysis_info.get("has_specific_methods"):
        #         strategy_guidance += "\n2. 重点关注出错的具体方法"
        # elif interface_names_str:
        #     strategy_guidance += f"\n1. 基于接口地址查找Controller实现"
       
        
        # 添加服务编号使用指导
        # 构建服务编号使用指导
        # service_guidance = ""
        # if file_names_str or table_names_str or interface_names_str:
        #     service_guidance = f"\n\n🏷️ **服务编号使用指导**：已从错误位置信息中提取到服务编号信息，建议在使用 search_code_by_app_code 工具时指定对应的服务编号，这样可以精确定位到对应服务的代码库进行查找。"

        return "\n\n".join(guidance_parts) 


class ErrorLocationEnhancementStep(AnalysisStep):
    """错误位置信息增强步骤"""
    
    def __init__(self, step_number: int = 4, total_steps: int = 5):
        super().__init__("增强错误位置信息", step_number, total_steps)
        self.enhancer = ErrorLocationEnhancer()
        # self.log_store_service = LogStoreService()

    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()
        
        error_locations = context.get("error_locations")
        log_content = context.get("log_content")
        
        if not error_locations:
            error_msg = "缺少错误位置信息"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
        
        if not log_content:
            error_msg = "缺少日志内容"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}
        
        try:
            # 解析过滤后的日志内容
            filtered_log = []
            if isinstance(log_content, str):
                try:
                    import json
                    filtered_log = json.loads(log_content)
                except json.JSONDecodeError:
                    logger.info("⚠️ 无法解析日志内容为JSON格式，跳过错误位置信息增强")
                    # 如果无法解析，直接返回原有的error_locations
                    self.log_success("跳过错误位置信息增强（日志格式不支持）")
                    return {"success": True, "error_locations": error_locations}
            elif isinstance(log_content, list):
                filtered_log = log_content
            else:
                logger.info("⚠️ 日志内容格式不支持，跳过错误位置信息增强")
                self.log_success("跳过错误位置信息增强（日志格式不支持）")
                return {"success": True, "error_locations": error_locations}
            
            # 执行错误位置信息增强（包括app_code格式清理）
            enhanced_error_locations = self.enhancer.enhance_error_locations(
                error_locations, filtered_log
            )
            
            # 保存增强后的错误位置分析结果（仅在调试模式下）
            if context.get("show_debug", False):
                file_manager = context.get("file_manager")
                if file_manager:
                    timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
                    traceid = context.get("traceid")
                    file_manager.save_json(
                        enhanced_error_locations, 
                        f'enhanced_error_locations_{traceid}_{timestamp}.json', 
                        traceid
                    )
                    # 保存根因
                    # self.log_store_service.save_root_cause(traceid, enhanced_error_locations)

            self.log_success()
            return {"success": True, "error_locations": enhanced_error_locations}
            
        except Exception as e:
            error_msg = f"错误位置信息增强失败: {str(e)}"
            self.log_error(error_msg)
            # 即使增强失败，也返回原有的error_locations，不影响后续流程
            logger.info("⚠️ 错误位置信息增强失败，使用原有的错误位置信息继续处理")
            return {"success": True, "error_locations": error_locations}


class FrontendResourceConfirmationStep(AnalysisStep):
    """前端资源确认步骤"""

    def __init__(self, step_number: int = 3, total_steps: int = 4):
        super().__init__("前端资源确认", step_number, total_steps)
        # 导入前端资源验证器
        from apis.frontend.resource_validator import FrontendResourceValidator
        self.resource_validator = FrontendResourceValidator()

    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()

        log_content = context.get("log_content")
        if not log_content:
            error_msg = "缺少日志内容"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}

        try:
            # 解析日志内容
            if isinstance(log_content, str):
                log_data = json.loads(log_content)
            else:
                log_data = log_content

            # 提取前端资源信息
            frontend_resources = self.resource_validator.extract_frontend_resources(log_data)

            if not frontend_resources:
                error_msg = "未找到任何前端资源，终止前端问题分析流程"
                self.log_error(error_msg)
                return {"success": False, "error": error_msg}

            logger.info(f"✅ 找到 {len(frontend_resources)} 个前端资源")

            # 获取资源文件大小
            resources_with_size = self.resource_validator.validate_resources_with_size(frontend_resources)

            if not resources_with_size:
                error_msg = "无法获取任何前端资源的文件大小信息，终止前端问题分析流程"
                self.log_error(error_msg)
                return {"success": False, "error": error_msg}

            self.log_success(f"成功确认 {len(resources_with_size)} 个前端资源")

            # 保存前端资源信息到文件
            self._save_frontend_resources(context, resources_with_size)

            return {
                "success": True,
                "frontend_resources": resources_with_size,
                "resource_count": len(resources_with_size)
            }

        except Exception as e:
            error_msg = f"前端资源确认失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}

    def _save_frontend_resources(self, context: Dict[str, Any], resources: List[Dict[str, Any]]) -> None:
        """保存前端资源信息到文件"""
        try:
            file_manager = context.get("file_manager")
            if not file_manager:
                logger.info("⚠️ 无法保存前端资源信息：文件管理器不可用")
                return

            traceid = context.get("traceid", "unknown")
            timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))

            # 保存为JSON文件
            filename = f"frontend_resources_{traceid}_{timestamp}.json"
            file_content = json.dumps(resources, ensure_ascii=False, indent=2)

            file_manager.save_json(file_content,filename, traceid)
            logger.info(f"✅ 前端资源信息已保存到文件: {filename}")

        except Exception as e:
            logger.info(f"⚠️ 保存前端资源信息失败: {str(e)}")


class FrontendProblemAnalysisStep(AnalysisStep):
    """前端问题分析步骤"""

    def __init__(self, analyzer: LogAnalyzer, step_number: int = 4, total_steps: int = 4):
        super().__init__("生成前端问题分析报告", step_number, total_steps)
        self.analyzer = analyzer

    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()

        frontend_resources = context.get("frontend_resources")
        if not frontend_resources:
            error_msg = "缺少前端资源信息"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}

        try:
            # 获取基本信息
            traceid = context.get("traceid", "unknown")
            log_type = context.get("log_type")

            # 调用LLM进行前端问题分析
            logger.info("🤖 开始LLM前端问题分析...")
            analysis_report = self.analyzer.analyze_frontend_problem(traceid, frontend_resources, log_type)

            self.log_success("前端问题分析完成")

            # 保存分析报告
            self._save_analysis_report(context, analysis_report)

            return {
                "success": True,
                "analysis_report": analysis_report,
                "frontend_resources": frontend_resources,
                "analysis_type": "frontend"
            }

        except Exception as e:
            error_msg = f"前端问题分析失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}

    def _save_analysis_report(self, context: Dict[str, Any], analysis_report: str) -> None:
        """保存前端问题分析报告"""
        try:
            file_manager = context.get("file_manager")
            if not file_manager:
                logger.info("⚠️ 无法保存分析报告：文件管理器不可用")
                return

            traceid = context.get("traceid", "unknown")
            timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))

            # 保存分析报告
            filename = f"frontend_analysis_report_{traceid}_{timestamp}.txt"
            file_manager.save_text(analysis_report,filename,traceid)
            logger.info(f"✅ 前端问题分析报告已保存到文件: {filename}")

        except Exception as e:
            logger.info(f"⚠️ 保存前端问题分析报告失败: {str(e)}")


class ProblemAnalysisStep(AnalysisStep):
    """问题分析步骤（后端问题）"""

    def __init__(self, analyzer: LogAnalyzer, step_number: int = 6, total_steps: int = 6):
        super().__init__("生成问题分析报告", step_number, total_steps)
        self.analyzer = analyzer
        # self.log_store_service = LogStoreService()

    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        self.log_start()
        
        # 根据log_type决定是否验证tool_results
        log_type = context.get("log_type")
        if log_type == "resource_load_slow":
            # 对于resource_load_slow类型，跳过tool_results验证
            required_keys = ["traceid", "log_content", "error_locations"]
            logger.info(f"🚫 检测到log_type为'{log_type}'，跳过tool_results验证")
        else:
            # 标准验证包含tool_results
            required_keys = ["traceid", "log_content", "error_locations", "tool_results"]
        
        for key in required_keys:
            if key not in context:
                error_msg = f"缺少必要数据: {key}"
                self.log_error(error_msg)
                return {"success": False, "error": error_msg}
        
        try:
            traceid = context["traceid"]
            log_content = context["log_content"]
            error_locations = context["error_locations"]
            
            # 对于resource_load_slow类型，使用默认的tool_results
            if log_type == "resource_load_slow":
                tool_results = "⚠️ 资源加载缓慢类型问题，跳过代码上下文查找步骤"
                logger.info("📝 使用默认tool_results内容")
            else:
                tool_results = context["tool_results"]
            
            logger.info(f"📊 调用analyze_problem函数...")
            logger.info(f"   - traceid: {traceid}")
            logger.info(f"   - log_content长度: {len(log_content)}")
            logger.info(f"   - error_locations类型: {type(error_locations)}")
            logger.info(f"   - tool_results长度: {len(tool_results)}")
            
            # analyze_problem现在返回文本报告而非JSON对象
            analysis_report = self.analyzer.analyze_problem(traceid, log_content, error_locations, tool_results)

            # 保存分析报告到文件（仅在调试模式下）
            if context.get("show_debug", False):
                file_manager = context.get("file_manager")
                if file_manager:
                    timestamp = context.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
                    # 使用save_text保存文本报告
                    file_manager.save_text(analysis_report, f'problem_analysis_report_{traceid}_{timestamp}.txt', traceid)

            self.log_success()
            return {"success": True, "analysis_report": analysis_report}
            
        except Exception as e:
            error_msg = f"生成问题分析报告失败: {str(e)}"
            self.log_error(error_msg)
            return {"success": False, "error": error_msg}


class LogAnalysisService:
    """日志分析服务主类"""
    
    def __init__(self, use_react_mode: bool = False, max_react_iterations: int = 5):
        """
        初始化日志分析服务
        
        Args:
            use_react_mode: 是否使用ReAct模式进行工具调用，默认False
            max_react_iterations: ReAct模式的最大迭代次数，默认5
        """
        self.model_config = ModelConfig()
        self.preprocessor = LogPreprocessor()
        self.use_react_mode = use_react_mode
        self.max_react_iterations = max_react_iterations
        
        if use_react_mode:
            logger.info(f"🧠 日志分析服务已启用ReAct模式 (最大迭代次数: {max_react_iterations})")
        else:
            logger.info("🔧 日志分析服务使用标准模式")
        
    def _create_analyzer(self, model_type: str = "error_location") -> LogAnalyzer:
        """创建日志分析器"""
        llm_client = self.model_config.get_model(model_type)
        return LogAnalyzer(
            llm_client, 
            use_react=self.use_react_mode, 
            max_react_iterations=self.max_react_iterations
        )
    
    def _configure_api_settings(self, api_timeout: int, max_retries: int):
        """配置API设置"""
        CustomRestfulLLM.timeout = api_timeout
        CustomRestfulLLM.max_retries = max_retries
        logger.info(f"🔧 API配置已更新: timeout={api_timeout}s, max_retries={max_retries}")
    
    def _create_context_for_debug(self, time: str, traceid: str, authorization: str,
                                 api_timeout: int, max_retries: int, api_temperature: float,
                                 show_debug: bool, log_type: str = None) -> Dict[str, Any]:
        """创建开发调试模式的分析上下文"""
        # 配置API设置
        self._configure_api_settings(api_timeout, max_retries)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_manager = FileManager(enable_debug=show_debug)
        
        return {
            "mode": "debug",
            "time": time,
            "traceid": traceid,
            "authorization": authorization,
            "api_timeout": api_timeout,
            "max_retries": max_retries,
            "api_temperature": api_temperature,
            "show_debug": show_debug,
            "timestamp": timestamp,
            "file_manager": file_manager,
            "log_type": log_type  # 添加log_type到上下文
        }
    
    def _create_context_for_external(self, request: TraceAnalysisRequest,
                                   api_timeout: int, max_retries: int, api_temperature: float,
                                   show_debug: bool) -> Dict[str, Any]:
        """创建外部触发模式的分析上下文"""
        # 配置API设置
        self._configure_api_settings(api_timeout, max_retries)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_manager = FileManager(enable_debug=show_debug)
        
        return {
            "mode": "external",
            "request_data": request,
            "traceid": request.trace_id,
            "sys_code": request.sys_code,
            "log_data": request.trace_data,  # 外部传入的trace_data作为log_data
            "proactivate_log_data": request.log_data,  # 外部传入的log_data重命名为proactivate_log_data
            "log_type": request.log_type,  # 外部传入的log_type
            "api_timeout": api_timeout,
            "max_retries": max_retries,
            "api_temperature": api_temperature,
            "show_debug": show_debug,
            "timestamp": timestamp,
            "file_manager": file_manager
        }
    
    def _create_debug_steps(self, log_type: str = None) -> List[AnalysisStep]:
        """创建开发调试模式的分析步骤（包含DataFetchStep）

        Args:
            log_type: 日志类型，用于决定分析流程
        """
        # 判断是否为前端问题
        frontend_log_types = ["resource_load_slow", "access_use_slow", "white_screen_time_slow"]
        is_frontend_problem = log_type in frontend_log_types

        if is_frontend_problem:
            logger.info(f"🎨 检测到log_type为'{log_type}'，将使用前端问题分析流程")
            # 前端问题分析流程：日志过滤 -> 前端资源确认 -> 前端问题分析
            return [
                DataFetchStep(self.preprocessor, 1, 4),
                DataFilterStep(self.preprocessor, 2, 4),
                FrontendResourceConfirmationStep(3, 4),
                FrontendProblemAnalysisStep(self._create_analyzer("problem_analysis"), 4, 4)
            ]
        else:
            logger.info(f"🔧 检测到log_type为'{log_type}'，将使用后端问题分析流程")
            # 后端问题分析流程（原分析流程）
            return [
                DataFetchStep(self.preprocessor, 1, 6),
                DataFilterStep(self.preprocessor, 2, 6),
                ErrorLocationAnalysisStep(self._create_analyzer("error_location"), 3, 6),
                ErrorLocationEnhancementStep(4, 6),
                CodeContextStep(self._create_analyzer("code_context"), 5, 6),
                ProblemAnalysisStep(self._create_analyzer("problem_analysis"), 6, 6)
            ]
    
    def _create_external_steps(self, log_type: str = None) -> List[AnalysisStep]:
        """创建外部触发模式的分析步骤（跳过DataFetchStep）

        Args:
            log_type: 日志类型，用于决定分析流程
        """
        # 判断是否为前端问题
        frontend_log_types = ["resource_load_slow", "access_use_slow", "white_screen_time_slow"]
        is_frontend_problem = log_type in frontend_log_types

        if is_frontend_problem:
            logger.info(f"🎨 检测到log_type为'{log_type}'，将使用前端问题分析流程")
            # 前端问题分析流程：日志过滤 -> 前端资源确认 -> 前端问题分析
            return [
                DataFilterStep(self.preprocessor, 1, 3),
                FrontendResourceConfirmationStep(2, 3),
                FrontendProblemAnalysisStep(self._create_analyzer("problem_analysis"), 3, 3)
            ]
        else:
            logger.info(f"🔧 检测到log_type为'{log_type}'，将使用后端问题分析流程")
            # 后端问题分析流程（原分析流程）
            return [
                DataFilterStep(self.preprocessor, 1, 5),
                ErrorLocationAnalysisStep(self._create_analyzer("error_location"), 2, 5),
                ErrorLocationEnhancementStep(3, 5),
                CodeContextStep(self._create_analyzer("code_context"), 4, 5),
                ProblemAnalysisStep(self._create_analyzer("problem_analysis"), 5, 5)
            ]
    
    def process_log_analysis_debug(self,
                                       time: str,
                                       traceid: str,
                                       authorization: str,
                                       api_timeout: int = 1800,
                                       max_retries: int = 1,
                                       api_temperature: float = 0.0,
                                       show_debug: bool = False,
                                       use_react_mode: bool = None,
                                       max_react_iterations: int = None,
                                       log_type: str = None) -> Dict[str, Any]:
        """
        开发调试模式的日志分析（包含DataFetchStep）
        
        Args:
            time: 日志时间
            traceid: 追踪ID
            authorization: 授权令牌
            api_timeout: API超时时间
            max_retries: 最大重试次数
            api_temperature: 模型温度
            show_debug: 是否显示调试信息
            use_react_mode: 是否使用ReAct模式（如果提供，会覆盖服务实例的设置）
            max_react_iterations: ReAct模式的最大迭代次数（如果提供，会覆盖服务实例的设置）
            log_type: 日志类型，用于决定分析流程（如"resource_load_slow"会跳过代码上下文步骤）

        Returns:
            包含分析结果的字典
        """
        try:
            # 如果方法调用时指定了ReAct参数，临时覆盖实例设置
            original_use_react = self.use_react_mode
            original_max_iterations = self.max_react_iterations
            
            if use_react_mode is not None:
                self.use_react_mode = use_react_mode
            if max_react_iterations is not None:
                self.max_react_iterations = max_react_iterations
            
            logger.info("==== 开始开发调试模式日志分析流程 ====")
            logger.info(f"TraceID: {traceid}")
            logger.info(f"时间: {time}")
            if log_type:
                logger.info(f"日志类型: {log_type}")
            logger.info(f"API配置: 超时{api_timeout}s, 重试{max_retries}次, 温度{api_temperature}")
            if self.use_react_mode:
                logger.info(f"🧠 ReAct模式: 启用 (最大迭代次数: {self.max_react_iterations})")
            else:
                logger.info("🔧 工具调用模式: 标准模式")
            
            # 创建开发调试模式上下文
            context = self._create_context_for_debug(time, traceid, authorization, api_timeout,
                                                    max_retries, api_temperature, show_debug, log_type)
            
            # 创建开发调试模式分析步骤
            analysis_steps = self._create_debug_steps(log_type)
            
            # 执行分析步骤
            for step in analysis_steps:
                result = step.execute(context)
                
                if not result.get("success", False):
                    return {
                        "success": False,
                        "data": None,
                        "error": result.get("error", f"{step.name}失败")
                    }
                
                # 将步骤结果合并到上下文中
                context.update(result)
            
            # 完成提示
            logger.info("✅ 开发调试模式分析完成！")
            logger.info("\n==== 日志分析完成 ====")
            
            # 构建返回结果
            file_manager = context.get("file_manager")
            result = {
                "success": True,
                "data": {
                    "traceid": traceid,
                    "analysis_report": context.get("analysis_report"),
                    "error_locations": context.get("error_locations"),
                    "tool_results": context.get("tool_results"),
                    "files_generated": file_manager.get_generated_files() if file_manager else []
                },
                "error": None
            }
            
            # 恢复原始设置
            self.use_react_mode = original_use_react
            self.max_react_iterations = original_max_iterations
            
            return result
            
        except Exception as e:
            error_msg = f"开发调试模式分析过程中出现错误: {str(e)}"
            logger.info(f"❌ {error_msg}")
            
            # 恢复原始设置（即使出错也要恢复）
            self.use_react_mode = original_use_react
            self.max_react_iterations = original_max_iterations
            
            return {
                "success": False,
                "data": None,
                "error": error_msg
            }
    
    def process_log_analysis_external(self,
                                          request: TraceAnalysisRequest,
                                          api_timeout: int = 1800,
                                          max_retries: int = 1,
                                          api_temperature: float = 0.0,
                                          show_debug: bool = False,
                                          use_react_mode: bool = None,
                                          max_react_iterations: int = None) -> Dict[str, Any]:
        """
        外部触发模式的日志分析（跳过DataFetchStep）
        
        Args:
            request: 外部分析请求数据
            api_timeout: API超时时间
            max_retries: 最大重试次数
            api_temperature: 模型温度
            show_debug: 是否显示调试信息
            use_react_mode: 是否使用ReAct模式（如果提供，会覆盖服务实例的设置）
            max_react_iterations: ReAct模式的最大迭代次数（如果提供，会覆盖服务实例的设置）
        
        Returns:
            包含分析结果的字典
        """
        try:
            # 如果方法调用时指定了ReAct参数，临时覆盖实例设置
            original_use_react = self.use_react_mode
            original_max_iterations = self.max_react_iterations
            
            if use_react_mode is not None:
                self.use_react_mode = use_react_mode
            if max_react_iterations is not None:
                self.max_react_iterations = max_react_iterations
            
            logger.info("==== 开始外部触发模式日志分析流程 ====")
            logger.info(f"请求ID: {request.id}")
            logger.info(f"TraceID: {request.trace_id}")
            logger.info(f"系统编号: {request.sys_code}")
            if request.log_type:
                logger.info(f"日志类型: {request.log_type}")
            logger.info(f"API配置: 超时{api_timeout}s, 重试{max_retries}次, 温度{api_temperature}")
            if self.use_react_mode:
                logger.info(f"🧠 ReAct模式: 启用 (最大迭代次数: {self.max_react_iterations})")
            else:
                logger.info("🔧 工具调用模式: 标准模式")
            
            # 创建外部触发模式上下文
            context = self._create_context_for_external(request, api_timeout,
                                                       max_retries, api_temperature, show_debug)
            
            # 创建外部触发模式分析步骤（跳过DataFetchStep）
            analysis_steps = self._create_external_steps(request.log_type)
            
            # 执行分析步骤
            for step in analysis_steps:
                result = step.execute(context)
                
                if not result.get("success", False):
                    return {
                        "success": False,
                        "data": None,
                        "error": result.get("error", f"{step.name}失败"),
                        "request_id": request.id,
                        "trace_id": request.trace_id
                    }
                
                # 将步骤结果合并到上下文中
                context.update(result)
            
            # 完成提示
            logger.info("✅ 外部触发模式分析完成！")
            logger.info("\n==== 日志分析完成 ====")
            
            # 构建返回结果
            file_manager = context.get("file_manager")
            result = {
                "success": True,
                "data": {
                    "request_id": request.id,
                    "traceid": request.trace_id,
                    "sys_code": request.sys_code,
                    "analysis_report": context.get("analysis_report"),
                    "error_locations": context.get("error_locations"),
                    "tool_results": context.get("tool_results"),
                    "files_generated": file_manager.get_generated_files() if file_manager else []
                },
                "error": None
            }
            
            # 恢复原始设置
            self.use_react_mode = original_use_react
            self.max_react_iterations = original_max_iterations
            
            return result
            
        except Exception as e:
            error_msg = f"外部触发模式分析过程中出现错误: {str(e)}"
            logger.info(f"❌ {error_msg}")
            
            # 恢复原始设置（即使出错也要恢复）
            self.use_react_mode = original_use_react
            self.max_react_iterations = original_max_iterations
            
            return {
                "success": False,
                "data": None,
                "error": error_msg,
                "request_id": request.id,
                "trace_id": request.trace_id
            }


# ========== 便捷的外部接口函数 ==========

def process_log_analysis_from_external_data(
    request_data: Dict[str, Any],
    api_timeout: int = 1200,
    max_retries: int = 1,
    api_temperature: float = 0.0,
    show_debug: bool = False,
    use_react_mode: bool = False,
    max_react_iterations: int = 5
) -> Dict[str, Any]:
    """
    外部数据驱动的日志分析接口（跳过DataFetchStep）
    
    Args:
        request_data: 包含完整字段的外部请求数据字典
        api_timeout: API超时时间，默认1800秒
        max_retries: 最大重试次数，默认1次
        api_temperature: 模型温度，默认0.0
        show_debug: 是否显示调试信息，默认False
        use_react_mode: 是否使用ReAct模式，默认False
        max_react_iterations: ReAct模式的最大迭代次数，默认5
    
    Returns:
        包含分析结果的字典
    """
    try:
        # 将字典数据转换为TraceAnalysisRequest对象
        request = TraceAnalysisRequest.from_dict(request_data)
        
        # 创建服务实例
        service = LogAnalysisService(use_react_mode=use_react_mode, max_react_iterations=max_react_iterations)
        
        # 执行外部触发模式分析
        return service.process_log_analysis_external(
            request, api_timeout, max_retries, api_temperature, show_debug
        )
        
    except Exception as e:
        return {
            "success": False,
            "data": None,
            "error": f"处理外部数据时出现错误: {str(e)}"
        }


# ========== 向后兼容的函数接口 ==========

def process_log_analysis(
    time: str, 
    traceid: str, 
    authorization: str,
    api_timeout: int = 1200,
    max_retries: int = 1,
    api_temperature: float = 0.0,
    show_debug: bool = False,
    use_react_mode: bool = False,
    max_react_iterations: int = 5,
    log_type: str = None
) -> Dict[str, Any]:
    """
    向后兼容的函数接口（开发调试模式）
    
    Args:
        time: 日志时间
        traceid: 追踪ID
        authorization: 授权令牌
        api_timeout: API超时时间
        max_retries: 最大重试次数
        api_temperature: 模型温度
        show_debug: 是否显示调试信息
        use_react_mode: 是否使用ReAct模式
        max_react_iterations: ReAct模式的最大迭代次数
        log_type: 日志类型，用于决定分析流程（如"resource_load_slow"会跳过代码上下文步骤）

    Returns:
        包含分析结果的字典
    """
    service = LogAnalysisService(use_react_mode=use_react_mode, max_react_iterations=max_react_iterations)
    return service.process_log_analysis_debug(
        time, traceid, authorization, api_timeout,
        max_retries, api_temperature, show_debug,
        use_react_mode, max_react_iterations, log_type
    ) 