from datetime import datetime
from dao.solution_dao import SolutionDAO, Solution
from utils.logger import logger


class SolutionService:
    def __init__(self):
        self.dao = SolutionDAO()

    def create_solution(self, solution_data) -> int:
        """
        创建一个新的 Solution 记录。
        :param solution_data: 包含解决方案字段的字典或 Solution 对象
        :return: 返回生成的 ID
        """
        try:
            logger.debug("Creating new solution...")
            if isinstance(solution_data, dict):
                solution = self._dict_to_solution(solution_data)
            else:
                solution = solution_data

            solution.create_time = datetime.now().replace(microsecond=0)
            solution.last_modify_time = datetime.now().replace(microsecond=0)
            return self.dao.create(solution)
        except Exception as e:
            logger.error(f"Error occurred while creating solution: {e}")
            raise

    def update_solution_fields(self, solution, fields: dict) -> bool:
        """
        更新指定 solution 的字段，并自动更新 last_modify_time 和 version（乐观锁）
        :param solution_id: 解决方案 ID
        :param fields: 要更新的字段字典
        :return: 是否更新成功
        """
        try:
            for k, v in fields.items():
                if hasattr(solution, k):
                    setattr(solution, k, v)

            solution.last_modify_time = datetime.now()
            return self.dao.update(solution, fields)
        except Exception as e:
            logger.error(f"Error occurred while updating solution: {e}")
            return False

    def get_solution_by_id(self, solution_id: int):
        """
        根据 ID 获取 solution 数据
        :param solution_id: 解决方案 ID
        :return: Solution 对象或 None
        """
        try:
            return self.dao.get_by_id(solution_id)
        except Exception as e:
            logger.error(f"Error fetching solution by ID: {e}")
            return None

    def get_solution_by_trace_id(self, trace_id: int):
        """
        根据 ID 获取 solution 数据
        :param solution_id: 解决方案 ID
        :return: Solution 对象或 None
        """
        try:
            return self.dao.get_by_trace_id(trace_id)
        except Exception as e:
            logger.error(f"Error fetching solution by ID: {e}")
            return None

    def get_solution_by_trace_id_and_version(self, trace_id: int, version: int):
        """
        根据 ID 获取 solution 数据
        :param solution_id: 解决方案 ID
        :return: Solution 对象或 None
        """
        try:
            return self.dao.get_by_trace_id_and_version(trace_id, version)
        except Exception as e:
            logger.error(f"Error fetching solution by ID: {e}")
            return None
    def get_all_solutions(self):
        """
        获取所有 solution 记录
        :return: Solution 列表
        """
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all solutions: {e}")
            return []

    def _dict_to_solution(self, data: dict) -> "Solution":
        """
        将字典转换为 Solution 对象
        """
        from dataclasses import asdict

        # 过滤掉不在 Solution 中的字段
        valid_fields = {f: data[f] for f in data if f in asdict(Solution()).keys()}
        return Solution(**valid_fields)
