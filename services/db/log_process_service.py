import time
from datetime import datetime
from typing import Optional, List

from common.common_enum import LogProcessStatus
from dao.log_process_dao import LogProcessDAO, LogProcess
from dataclasses import asdict

from services.vo.log_process_solution_vo import LogProcessSolution
from utils.common_util import get_with_retry
from utils.logger import logger
from services.vo.log_dashboard_req_vo import LogProcessDashboardParams


class LogProcessService:
    def __init__(self):
        self.dao = LogProcessDAO()

    def create_log_process(self, data) -> int:
        try:
            logger.debug("Creating new log process...")
            if isinstance(data, dict):
                log_process = self._dict_to_log_process(data)
            else:
                log_process = data
            log_process.create_time = datetime.now().replace(microsecond=0)
            log_process.last_modify_time = datetime.now().replace(microsecond=0)
            log_process.version = 1
            return self.dao.create(log_process)
        except Exception as e:
            logger.error(f"Error occurred while creating log process: {e}")
            raise



    def update_status(self, log_id: int, status: str, comment: str) -> bool:
        try:
            log_dup = get_with_retry(lambda: self.dao.get_by_id(log_id), max_retries=1, delay=1)
            if not log_dup:
                logger.warning(f"Log Process with ID {log_id} not found.")
                return False
            log_dup.status = status
            fields_to_update = {"status": status}
            if comment:
                fields_to_update["comment"] = comment
            return self.dao.update(log_dup, fields_to_update)
        except Exception as e:
            logger.error(f"Error occurred while updating log process status: {e}")
            return False

    def update_common(self, log_process, fields_to_update: dict) -> bool:
            return self.dao.update(log_process, fields_to_update)

    def get_log_process_by_id(self, log_id: int) -> Optional[LogProcess]:
        try:
            return self.dao.get_by_id(log_id)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_by_trace_hash(self, trace_hash: str) -> Optional[LogProcess]:
        try:
            return self.dao.get_by_trace_hash(trace_hash)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_log_process_by_trace_id(self, trace_id: str) -> Optional[LogProcess]:
        try:
            return self.dao.get_by_trace_id(trace_id)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_log_process_by_sys_code_and_request(self, sys_code: str, request: str) -> Optional[LogProcess]:
        try:
            return self.dao.get_by_sys_code_and_request(sys_code, request)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_all_log_processs(self) -> list[LogProcess]:
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all log processs: {e}")
            return []

    def _dict_to_log_process(self, data: dict) -> LogProcess:
        valid_fields = {f: data[f] for f in data if f in asdict(LogProcess()).keys()}
        return LogProcess(**valid_fields)

    def delete_log_process(self, id: int):
        try:
            return self.dao.delete(id)
        except Exception as e:
            logger.error(f"Error delete log process by ID: {e}")
            return None

    def get_log_process_list(self, req: LogProcessDashboardParams) -> List[LogProcessSolution]:
        custom_fields = {}
        if req.trace_id:
            custom_fields[f"""lp.trace_id=%s"""] = req.trace_id
        if req.log_type:
            custom_fields[f"""lp.log_type=%s"""] = req.log_type
        if req.solution_status:
            custom_fields[f"""s.status=%s"""] = req.solution_status
        if req.solution_confirm_status:
            custom_fields[f"""s.confirm_status=%s"""] = req.solution_confirm_status
        
        if req.start_time:
            start_key = f"""s.generate_time >= %s"""
            custom_fields[start_key] = req.start_time
        if req.end_time:
            end_key = f"""s.generate_time <= %s"""
            custom_fields[end_key] = req.end_time

        data = self.dao.query_log_process(req.page_num, req.page_size, custom_fields=custom_fields)
        result = []
        for row in data:
            log_process_solution = LogProcessSolution(
                trace_id=row['trace_id'],
                request_time=row['request_time'].strftime('%Y-%m-%d %H:%M:%S') if row['request_time'] else None,
                error_info=row['error_info'],
                log_type=row['log_type'],
                current_stage=row['current_stage'],
                start_time=row['start_time'].strftime('%Y-%m-%d %H:%M:%S') if row['start_time'] else None,
                end_time=row['end_time'].strftime('%Y-%m-%d %H:%M:%S') if row['end_time'] else None,
                solution_status=row['solution_status'],
                user_name=row['user_name'],
                solution_confirm_status=row['solution_confirm_status'],
                solution_issued_status=row['solution_issued_status'],
                version=row['version'],
                status=row['status'],
                generate_time=row['generate_time'].strftime('%Y-%m-%d %H:%M:%S') if row['generate_time'] else None,
            )
            result.append(log_process_solution)
        return result
