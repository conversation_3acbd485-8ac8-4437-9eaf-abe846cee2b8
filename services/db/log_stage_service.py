
from datetime import datetime
from dao.log_stage_dao import LogStageDAO, LogStage
from utils.logger import logger

class LogStageService:
    def __init__(self):
        self.dao = LogStageDAO()

    def create_log_stage(self, stage_data) -> int:
        """
        创建一个新的 LogStage 记录。
        :param stage_data: 包含日志阶段字段的字典或 LogStage 对象
        :return: 返回生成的 ID
        """
        try:
            logger.debug("Creating new log stage...")
            if isinstance(stage_data, dict):
                stage = self._dict_to_log_stage(stage_data)
            else:
                stage = stage_data

            stage.create_time = datetime.now().replace(microsecond=0)
            stage.last_modify_time = datetime.now().replace(microsecond=0)
            stage.version = 1
            return self.dao.create(stage)
        except Exception as e:
            logger.error(f"Error occurred while creating log stage: {e}")
            raise

    def update_log_stage_fields(self, stage, fields: dict) -> bool:
        """
        更新指定 log stage 的字段，并自动更新 last_modify_time 和 version（乐观锁）
        :param stage: LogStage 对象
        :param fields: 要更新的字段字典
        :return: 是否更新成功
        """
        try:
            for k, v in fields.items():
                if hasattr(stage, k):
                    setattr(stage, k, v)

            stage.last_modify_time = datetime.now()
            return self.dao.update(stage, fields)
        except Exception as e:
            logger.error(f"Error occurred while updating log stage: {e}")
            return False

    def get_log_stage_by_id(self, stage_id: int):
        """
        根据 ID 获取 log stage 数据
        :param stage_id: 日志阶段 ID
        :return: LogStage 对象或 None
        """
        try:
            return self.dao.get_by_id(stage_id)
        except Exception as e:
            logger.error(f"Error fetching log stage by ID: {e}")
            return None

    def get_log_stages_by_process_id_version(self, log_process_id, version):
        """
        根据 log process ID 获取所有相关的 log stages
        :param process_id: 日志处理 ID
        :return: LogStage 列表
        """
        try:
            return self.dao.get_log_stages_by_process_id_version(log_process_id, version)
        except Exception as e:
            logger.error(f"Error fetching log stages by process ID: {e}")
            return []

    def get_all_log_stages(self):
        """
        获取所有 log stage 记录
        :return: LogStage 列表
        """
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all log stages: {e}")
            return []

    def _dict_to_log_stage(self, data: dict) -> "LogStage":
        """
        将字典转换为 LogStage 对象
        """
        from dataclasses import asdict

        # 过滤掉不在 LogStage 中的字段
        valid_fields = {f: data[f] for f in data if f in asdict(LogStage()).keys()}
        return LogStage(**valid_fields)


