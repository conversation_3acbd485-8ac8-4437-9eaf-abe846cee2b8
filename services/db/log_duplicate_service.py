from datetime import datetime
from typing import Optional

from dao.log_duplicate_dao import LogDuplicateDAO, LogDuplicate
from dataclasses import asdict
from utils.logger import logger


class LogDuplicateService:
    def __init__(self):
        self.dao = LogDuplicateDAO()

    def create_log_duplicate(self, data) -> int:
        try:
            logger.debug(f"Creating new log duplicate...{data} 重复")
            if isinstance(data, dict):
                log_duplicate = self._dict_to_log_duplicate(data)
            else:
                log_duplicate = data
            log_duplicate.create_time = datetime.now().replace(microsecond=0)
            log_duplicate.last_modify_time = datetime.now().replace(microsecond=0)
            log_duplicate.version = 1
            return self.dao.create(log_duplicate)
        except Exception as e:
            logger.error(f"Error occurred while creating log duplicate: {e}")
            raise

    def update_status(self, log_id: int, new_comment: str) -> bool:
        try:
            log_dup = self.dao.get_by_id(log_id)
            if not log_dup:
                logger.warning(f"Log Duplicate with ID {log_id} not found.")
                return False
            log_dup.comment = new_comment
            fields_to_update = {"comment": log_dup.comment}
            return self.dao.update(log_dup, fields_to_update)
        except Exception as e:
            logger.error(f"Error occurred while updating log duplicate status: {e}")
            return False

    def get_log_duplicate_by_id(self, log_id: int) -> Optional[LogDuplicate]:
        try:
            return self.dao.get_by_id(log_id)
        except Exception as e:
            logger.error(f"Error fetching log duplicate by ID: {e}")
            return None

    def get_all_log_duplicates(self) -> list[LogDuplicate]:
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all log duplicates: {e}")
            return []

    def _dict_to_log_duplicate(self, data: dict) -> LogDuplicate:
        valid_fields = {f: data[f] for f in data if f in asdict(LogDuplicate()).keys()}
        return LogDuplicate(**valid_fields)
