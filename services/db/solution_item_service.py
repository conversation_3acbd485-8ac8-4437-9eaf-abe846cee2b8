from datetime import datetime
from dao.solution_item_dao import SolutionItemDAO, SolutionItem
from utils.logger import logger

class SolutionItemService:
    def __init__(self):
        self.dao = SolutionItemDAO()

    def create_solution_item(self, item_data) -> int:
        """
        创建一个新的 SolutionItem 记录。
        :param item_data: 包含解决方案明细字段的字典或 SolutionItem 对象
        :return: 返回生成的 ID
        """
        try:
            logger.debug("Creating new solution item...")
            if isinstance(item_data, dict):
                item = self._dict_to_solution_item(item_data)
            else:
                item = item_data

            item.create_time = datetime.now().replace(microsecond=0)
            item.last_modify_time = datetime.now().replace(microsecond=0)
            item.version = 1
            return self.dao.create(item)
        except Exception as e:
            logger.error(f"Error occurred while creating solution item: {e}")
            raise

    def update_solution_item_fields(self, item, fields: dict) -> bool:
        """
        更新指定 solution item 的字段，并自动更新 last_modify_time 和 version（乐观锁）
        :param item: SolutionItem 对象
        :param fields: 要更新的字段字典
        :return: 是否更新成功
        """
        try:
            for k, v in fields.items():
                if hasattr(item, k):
                    setattr(item, k, v)

            item.last_modify_time = datetime.now()
            return self.dao.update(item, fields)
        except Exception as e:
            logger.error(f"Error occurred while updating solution item: {e}")
            return False

    def get_solution_item_by_id(self, item_id: int):
        """
        根据 ID 获取 solution item 数据
        :param item_id: 解决方案明细 ID
        :return: SolutionItem 对象或 None
        """
        try:
            return self.dao.get_by_id(item_id)
        except Exception as e:
            logger.error(f"Error fetching solution item by ID: {e}")
            return None

    def get_solution_items_by_solution_id(self, solution_id: int):
        """
        根据 solution ID 获取所有相关的 solution items
        :param solution_id: 解决方案 ID
        :return: SolutionItem 列表
        """
        try:
            return self.dao.get_by_solution_id(solution_id)
        except Exception as e:
            logger.error(f"Error fetching solution items by solution ID: {e}")
            return []

    def get_all_solution_items(self):
        """
        获取所有 solution item 记录
        :return: SolutionItem 列表
        """
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all solution items: {e}")
            return []

    def _dict_to_solution_item(self, data: dict) -> "SolutionItem":
        """
        将字典转换为 SolutionItem 对象
        """
        from dataclasses import asdict

        # 过滤掉不在 SolutionItem 中的字段
        valid_fields = {f: data[f] for f in data if f in asdict(SolutionItem()).keys()}
        return SolutionItem(**valid_fields)
