from datetime import datetime
from typing import Optional

from dao.log_fetch_param_dao import LogFetchParamDAO, LogFetchParam
from dataclasses import asdict
from utils.logger import logger


class LogFetchParamService:
    def __init__(self):
        self.dao = LogFetchParamDAO()

    def create_log_fetch_param(self, data) -> int:
        try:
            logger.debug("Creating new log fetch_param...")
            if isinstance(data, dict):
                log_fetch_param = self._dict_to_log_fetch_param(data)
            else:
                log_fetch_param = data
            log_fetch_param.create_time = datetime.now().replace(microsecond=0)
            log_fetch_param.last_modify_time = datetime.now().replace(microsecond=0)
            log_fetch_param.version = 1
            return self.dao.create(log_fetch_param)
        except Exception as e:
            logger.error(f"Error occurred while creating log fetch_param: {e}")
            raise

    def update_status(self, log_id: int, new_comment: str) -> bool:
        try:
            log_dup = self.dao.get_by_id(log_id)
            if not log_dup:
                logger.warning(f"Log FetchParam with ID {log_id} not found.")
                return False
            log_dup.comment = new_comment
            fields_to_update = {"comment": log_dup.comment}
            return self.dao.update(log_dup, fields_to_update)
        except Exception as e:
            logger.error(f"Error occurred while updating log fetch_param status: {e}")
            return False

    def get_log_fetch_param_by_id(self, log_id: int) -> Optional[LogFetchParam]:
        try:
            return self.dao.get_by_id(log_id)
        except Exception as e:
            logger.error(f"Error fetching log fetch_param by ID: {e}")
            return None

    def get_all_log_fetch_params(self) -> list[LogFetchParam]:
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all log fetch_params: {e}")
            return []

    def _dict_to_log_fetch_param(self, data: dict) -> LogFetchParam:
        valid_fields = {f: data[f] for f in data if f in asdict(LogFetchParam()).keys()}
        return LogFetchParam(**valid_fields)
