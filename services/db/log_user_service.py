import time
from datetime import datetime
from typing import Optional

from common.common_enum import LogProcessStatus
from dao.log_user_dao import <PERSON>g<PERSON>serDA<PERSON>, LogUser
from dataclasses import asdict

from utils.common_util import get_with_retry
from utils.logger import logger


class LogUserService:
    def __init__(self):
        self.dao = LogUserDAO()

    def create(self, data) -> int:
        try:
            logger.debug("Creating new log process...")
            if isinstance(data, dict):
                entity = self._dict_to_log_process(data)
            else:
                entity = data
            entity.create_time = datetime.now().replace(microsecond=0)
            entity.last_modify_time = datetime.now().replace(microsecond=0)
            entity.version = 1
            return self.dao.create(entity)
        except Exception as e:
            logger.error(f"Error occurred while creating log process: {e}")
            raise



    def update_status(self, log_id: int, status: str, comment: str) -> bool:
        try:
            log_dup = get_with_retry(lambda: self.dao.get_by_id(log_id), max_retries=1, delay=1)
            if not log_dup:
                logger.warning(f"Log Process with ID {log_id} not found.")
                return False
            log_dup.status = status
            fields_to_update = {"status": status}
            if comment:
                fields_to_update["comment"] = comment
            return self.dao.update(log_dup, fields_to_update)
        except Exception as e:
            logger.error(f"Error occurred while updating log process status: {e}")
            return False

    def update_common(self, log_process, fields_to_update: dict) -> bool:
            return self.dao.update(log_process, fields_to_update)

    def get_by_id(self, log_id: int) -> Optional[LogUser]:
        try:
            return self.dao.get_by_id(log_id)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_by_trace_id(self, trace_id: str) -> Optional[LogUser]:
        try:
            return self.dao.get_by_trace_id(trace_id)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_by_trace_id_and_user_id(self, trace_id: str, user_id: int) -> Optional[LogUser]:
        try:
            return self.dao.get_by_trace_id_and_user_id(trace_id, user_id)
        except Exception as e:
            logger.error(f"Error fetching log process by ID: {e}")
            return None

    def get_all(self) -> list[LogUser]:
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all log processs: {e}")
            return []

    def _dict_to_log_process(self, data: dict) -> LogUser:
        valid_fields = {f: data[f] for f in data if f in asdict(LogUser()).keys()}
        return LogUser(**valid_fields)

    def get_solution_by_trace_id(self, trace_id):

        pass
