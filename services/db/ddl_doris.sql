DROP TABLE IF EXISTS issue;
DROP TABLE IF EXISTS issue_context;
DROP TABLE IF EXISTS job_trigger;
DROP TABLE IF EXISTS log_duplicate;
DROP TABLE IF EXISTS log_fetch_param;
DROP TABLE IF EXISTS log_process;
DROP TABLE IF EXISTS log_user;
DROP TABLE IF EXISTS service;
DROP TABLE IF EXISTS service_static_data;
DROP TABLE IF EXISTS solution;
DROP TABLE IF EXISTS sys;


CREATE TABLE `issue` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- issue 层级字段
  `issue_type` STRING COMMENT '问题类型（如：缓慢）',
  
  -- root_cause.origin_point 字段
  `rc_file_name` STRING COMMENT '根因原始点文件名',
  `rc_class_name` STRING COMMENT '根因原始点类名',
  `rc_method_name` STRING COMMENT '根因原始点方法名',
  `rc_line_number` STRING COMMENT '根因原始点行号',
  `rc_service_url` STRING COMMENT '根因原始点服务URL',
  `rc_sql` STRING COMMENT '根因SQL语句',
  `rc_table_names` STRING COMMENT '根因涉及的表名列表',
  `rc_app_code` STRING COMMENT '根因应用编码',
  `rc_middleware` STRING COMMENT '根因中间件',
  -- root_cause.origin_point.timestamp 字段
  `rc_start_time` DATETIME COMMENT '根因开始时间',
  `rc_end_time` DATETIME COMMENT '根因结束时间',
  `rc_duration_ms` INT COMMENT '根因耗时（毫秒）',
  `rc_occurrence_time` DATETIME COMMENT '根因发生时间',
  -- root_cause.error_description
  `rc_error_description` STRING COMMENT '根因错误描述',
  
  -- final_manifestation.location 字段
  `fm_file_name` STRING COMMENT '最终表现位置文件名',
  `fm_class_name` STRING COMMENT '最终表现位置类名',
  `fm_method_name` STRING COMMENT '最终表现位置方法名',
  `fm_line_number` STRING COMMENT '最终表现位置行号',
  `fm_service_url` STRING COMMENT '最终表现位置服务URL',
  `fm_app_code` STRING COMMENT '最终表现位置应用编码',
  
  -- final_manifestation.location.timestamp 字段
  `fm_start_time` DATETIME COMMENT '最终表现开始时间',
  `fm_end_time` DATETIME COMMENT '最终表现结束时间',
  `fm_duration_ms` INT COMMENT '最终表现耗时（毫秒）',
  `fm_occurrence_time` DATETIME COMMENT '最终表现发生时间',
  
  -- final_manifestation 其他字段
  `fm_error_type` STRING COMMENT '最终表现错误类型',
  `fm_error_info` STRING COMMENT '最终表现错误信息',
  `fm_duration` INT COMMENT '最终表现总耗时（毫秒）',
  `fm_impact_scope` STRING COMMENT '影响范围',
  `raw_json` TEXT COMMENT '原始json数据',
  
  -- 基础字段
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁版本号',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '最后修改人',
  `last_modify_time` DATETIME COMMENT '最后修改时间',
  `trace_hash` TEXT COMMENT 'trace_hash',
  `trace_id` STRING COMMENT 'trace_id'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '问题表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;



CREATE TABLE `issue_context` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `issue_id` STRING COMMENT '问题id',
  `type` STRING COMMENT '类型',
  `meta_data` STRING COMMENT '上下文元数据',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '问题上下文表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `job_trigger` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'job id',
  `job_name` STRING NOT NULL COMMENT '任务名',
  `trigger_time` DATETIME NOT NULL COMMENT '触发时间',
  `status` STRING NOT NULL COMMENT '状态:进行中，完成',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT 'job表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `log_duplicate` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `job_trigger_id` STRING COMMENT 'job触发id',
  `duplicate_log_id` INT NOT NULL COMMENT '重复的log id',
  `trace_id` STRING COMMENT 'traceId',
  `request` STRING COMMENT '请求入口',
  `request_time` DATETIME COMMENT '请求时间',
  `log_type` STRING COMMENT '日志类型',
  `sys_code` STRING COMMENT '系统编码;项目id',
  `count` INT COMMENT '发生次数',
  `comment` STRING COMMENT '注释：重复原因',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '重复日志表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `log_fetch_param` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `job_trigger_id` INT NOT NULL COMMENT 'job id',
  `log_type` STRING COMMENT '日志类型',
  `system_code` STRING COMMENT '系统编码',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NOT NULL COMMENT '结束时间',
  `start_index` INT DEFAULT '0' COMMENT '开始条数',
  `page_num` INT DEFAULT '10' COMMENT '每页数',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '取log参数表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `log_process` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `trace_id` STRING COMMENT 'traceId',
  `request` STRING COMMENT '请求入口',
  `request_time` DATETIME COMMENT '请求时间',
  `sys_code` STRING COMMENT '系统编码;项目id',
  `count` INT COMMENT '发生次数',
  `log_type` STRING COMMENT '日志类型',
  `job_trigger_id` STRING COMMENT 'job id',
  `status` STRING COMMENT '状态：进行中/已完成',
  `user_id` STRING COMMENT '用户id',
  `trace_data` TEXT COMMENT 'trace数据',
  `log_data` TEXT COMMENT '日志数据',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间',
  `comment` STRING COMMENT '备注',
  `solution_id` BIGINT COMMENT '解决方案id',
  `trace_hash` TEXT COMMENT 'trac每个入口的hash',
  `source_type` STRING COMMENT '来源类型',
  `start_time` DATETIME COMMENT '开始时间',
  `end_time` DATETIME COMMENT '结束时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '日志处理表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;


CREATE TABLE `log_user` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `trace_id` STRING COMMENT 'traceId',
  `request` STRING COMMENT '请求入口',
  `request_time` datetime COMMENT '请求时间',
  `status` STRING COMMENT '状态：进行中/已完成',
  `user_id` BIGINT COMMENT '用户id',
  `trace_data` TEXT COMMENT 'trace数据',
  `log_process_id` BIGINT COMMENT 'log_process_id',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间',
  `comment` STRING COMMENT '备注',
  `solution_id` BIGINT COMMENT '解决方案id',
  `trace_hash` text COMMENT 'trac每个入口的hash'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '用户发布日志表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;


CREATE TABLE `service` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `service_id` STRING COMMENT '服务id',
  `service_name` STRING COMMENT '服务名称',
  `sys_code` STRING COMMENT '系统编码;项目id',
  `meta_data` STRING COMMENT '服务元数据',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '服务表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `service_static_data` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `service_id` STRING COMMENT '服务id',
  `type` STRING COMMENT '类型',
  `meta_data` STRING COMMENT '静态元数据',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '服务的静态数据表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `solution` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `trace_id` STRING COMMENT 'traceId',
  `raw_json` TEXT COMMENT '解决方案',
  `trace_hash` TEXT COMMENT 'trace_hash',
  `status` STRING COMMENT '方案状态',
  `confirm_status` STRING COMMENT '方案确认状态',
  `issued_status` STRING COMMENT '方案下发状态',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间',
  `generate_time` DATETIME COMMENT '方案生成时间',
  `user_name` STRING COMMENT '提交用户名',
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '解决方案表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `sys` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sys_code` STRING COMMENT '系统编码;项目id',
  `meta_data` STRING COMMENT '系统元数据',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '系统表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;

CREATE TABLE `solution_item` (
  `id` BIGINT NOT NULL COMMENT '主键ID',
  `solution_id` BIGINT COMMENT '关联解决方案表id',
  `trace_id` STRING COMMENT 'trace_id',
  `trace_hash` TEXT COMMENT 'trace_hash',
  `system_code` STRING COMMENT '系统编号',
  `service_code` STRING COMMENT '服务编号',
  `problem_desc` STRING COMMENT '问题说明',
  `ai_suggestion` STRING COMMENT 'AI建议方案',
  `confirm_status` STRING COMMENT '确认状态',
  `confirm_by` STRING COMMENT '确认人',
  `confirm_time` DATETIME COMMENT '确认时间',
  `system_owner` STRING COMMENT '系统负责人',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '更新人',
  `last_modify_time` DATETIME COMMENT '修改时间'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '解决方案明细表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;


CREATE TABLE `log_stage` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `log_process_id` BIGINT COMMENT '关联log_process表id',
  `trace_id` STRING COMMENT 'trace_id',
  `trace_hash` TEXT COMMENT 'trace_hash',
  `solution_id` BIGINT COMMENT '关联解决方案表id',
  `stage_name` STRING COMMENT '阶段名称（如：读取日志、错误定位等）',
  `stage_status` STRING COMMENT '阶段状态（如：成功完成、失败完成、未完成等）',
  `start_time` DATETIME COMMENT '阶段开始时间',
  `end_time` DATETIME COMMENT '阶段结束时间',
  `duration_ms` INT COMMENT '阶段耗时（毫秒）',
  `comment` STRING COMMENT '注释',
  `details` TEXT COMMENT '阶段详细信息或日志',
  `messages` TEXT COMMENT '预留字段',
  `tenant_id` STRING COMMENT '租户号',
  `version` INT COMMENT '乐观锁版本号',
  `create_user` STRING COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` STRING COMMENT '最后修改人',
  `last_modify_time` DATETIME COMMENT '最后修改时间',
  `catalog_name` STRING COMMENT '所属阶段分类'
) ENGINE=OLAP
UNIQUE KEY(`id`)
COMMENT '处理阶段表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10;
