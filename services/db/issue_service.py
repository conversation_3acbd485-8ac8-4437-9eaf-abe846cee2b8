from datetime import datetime
from dao.issue_dao import IssueDAO, Issue
from utils.logger import logger


class IssueService:
    def __init__(self):
        self.dao = IssueDAO()

    def create_issue(self, issue_data) -> int:
        """
        创建一个新的 Issue 记录。
        :param issue_data: 包含问题字段的字典或 Issue 对象
        :return: 返回生成的 ID
        """
        try:
            logger.debug("Creating new issue...")
            if isinstance(issue_data, dict):
                issue = self._dict_to_issue(issue_data)
            else:
                issue = issue_data

            issue.create_time = datetime.now().replace(microsecond=0)
            issue.last_modify_time = datetime.now().replace(microsecond=0)
            issue.version = 1
            return self.dao.create(issue)
        except Exception as e:
            logger.error(f"Error occurred while creating issue: {e}")
            raise

    def update_issue_fields(self, issue_id: int, fields: dict) -> bool:
        """
        更新指定 issue 的字段，并自动更新 last_modify_time 和 version（乐观锁）
        :param issue_id: 问题 ID
        :param fields: 要更新的字段字典
        :return: 是否更新成功
        """
        try:
            issue = self.dao.get_by_id(issue_id)
            if not issue:
                logger.warning(f"Issue with ID {issue_id} not found.")
                return False

            for k, v in fields.items():
                if hasattr(issue, k):
                    setattr(issue, k, v)

            issue.last_modify_time = datetime.now()
            return self.dao.update(issue, fields)
        except Exception as e:
            logger.error(f"Error occurred while updating issue: {e}")
            return False

    def get_issue_by_id(self, issue_id: int):
        """
        根据 ID 获取 issue 数据
        :param issue_id: 问题 ID
        :return: Issue 对象或 None
        """
        try:
            return self.dao.get_by_id(issue_id)
        except Exception as e:
            logger.error(f"Error fetching issue by ID: {e}")
            return None

    def get_all_issues(self):
        """
        获取所有 issue 记录
        :return: Issue 列表
        """
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all issues: {e}")
            return []

    def _dict_to_issue(self, data: dict) -> "Issue":
        """
        将字典转换为 Issue 对象
        """
        from dataclasses import asdict

        # 过滤掉不在 Issue 中的字段
        valid_fields = {f: data[f] for f in data if f in asdict(Issue()).keys()}
        return Issue(**valid_fields)
