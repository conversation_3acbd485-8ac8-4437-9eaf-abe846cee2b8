DROP TABLE IF EXISTS issue;
DROP TABLE IF EXISTS issue_context;
DROP TABLE IF EXISTS job_trigger;
DROP TABLE IF EXISTS log_duplicate;
DROP TABLE IF EXISTS log_fetch_param;
DROP TABLE IF EXISTS log_process;
DROP TABLE IF EXISTS log_user;
DROP TABLE IF EXISTS service;
DROP TABLE IF EXISTS service_static_data;
DROP TABLE IF EXISTS solution;
DROP TABLE IF EXISTS sys;


CREATE TABLE `issue` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `issue_type` varchar(255) DEFAULT NULL COMMENT '问题类型（如：缓慢）',
  `rc_file_name` varchar(255) DEFAULT NULL COMMENT '根因原始点文件名',
  `rc_class_name` varchar(255) DEFAULT NULL COMMENT '根因原始点类名',
  `rc_method_name` varchar(255) DEFAULT NULL COMMENT '根因原始点方法名',
  `rc_line_number` varchar(255) DEFAULT NULL COMMENT '根因原始点行号',
  `rc_service_url` varchar(1024) DEFAULT NULL COMMENT '根因原始点服务URL',
  `rc_sql` text COMMENT '根因SQL语句',
  `rc_table_names` text COMMENT '根因涉及的表名列表',
  `rc_app_code` varchar(255) DEFAULT NULL COMMENT '根因应用编码',
  `rc_middleware` varchar(255) DEFAULT NULL COMMENT '根因中间件',
  `rc_start_time` datetime DEFAULT NULL COMMENT '根因开始时间',
  `rc_end_time` datetime DEFAULT NULL COMMENT '根因结束时间',
  `rc_duration_ms` int DEFAULT NULL COMMENT '根因耗时（毫秒）',
  `rc_occurrence_time` datetime DEFAULT NULL COMMENT '根因发生时间',
  `rc_error_description` text COMMENT '根因错误描述',
  `fm_file_name` varchar(255) DEFAULT NULL COMMENT '最终表现位置文件名',
  `fm_class_name` varchar(255) DEFAULT NULL COMMENT '最终表现位置类名',
  `fm_method_name` varchar(255) DEFAULT NULL COMMENT '最终表现位置方法名',
  `fm_line_number` varchar(255) DEFAULT NULL COMMENT '最终表现位置行号',
  `fm_service_url` varchar(1024) DEFAULT NULL COMMENT '最终表现位置服务URL',
  `fm_app_code` varchar(255) DEFAULT NULL COMMENT '最终表现位置应用编码',
  `fm_start_time` datetime DEFAULT NULL COMMENT '最终表现开始时间',
  `fm_end_time` datetime DEFAULT NULL COMMENT '最终表现结束时间',
  `fm_duration_ms` int DEFAULT NULL COMMENT '最终表现耗时（毫秒）',
  `fm_occurrence_time` datetime DEFAULT NULL COMMENT '最终表现发生时间',
  `fm_error_type` varchar(255) DEFAULT NULL COMMENT '最终表现错误类型',
  `fm_error_info` text COMMENT '最终表现错误信息',
  `fm_duration` int DEFAULT NULL COMMENT '最终表现总耗时（毫秒）',
  `fm_impact_scope` varchar(255) DEFAULT NULL COMMENT '影响范围',
  `raw_json` longtext COMMENT '原始json数据',
  `tenant_id` varchar(255) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT NULL COMMENT '乐观锁版本号',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `last_modify_user` varchar(255) DEFAULT NULL COMMENT '最后修改人',
  `last_modify_time` datetime DEFAULT NULL COMMENT '最后修改时间',
  `trace_hash` text COMMENT 'tarce hash',
  `trace_id` varchar(100) DEFAULT NULL COMMENT 'trace_id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题表';


-- aianadb.issue_context definition

CREATE TABLE `issue_context` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `issue_id` varchar(255) DEFAULT NULL COMMENT '问题id',
  `type` varchar(255) DEFAULT NULL COMMENT '类型',
  `meta_data` varchar(900) DEFAULT NULL COMMENT '上下文元数据',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题上下文表';


-- aianadb.job_trigger definition

CREATE TABLE `job_trigger` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'job id',
  `job_name` varchar(255) NOT NULL COMMENT '任务名',
  `trigger_time` datetime NOT NULL COMMENT '触发时间',
  `status` varchar(50) NOT NULL COMMENT '状态:进行中，完成',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='job表';


-- aianadb.log_duplicate definition

CREATE TABLE `log_duplicate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_trigger_id` varchar(100) DEFAULT NULL COMMENT 'job触发id',
  `duplicate_log_id` int DEFAULT NULL COMMENT '重复的log id',
  `trace_id` varchar(255) DEFAULT NULL COMMENT 'traceId',
  `request` varchar(255) DEFAULT NULL COMMENT '请求入口',
  `request_time` datetime DEFAULT NULL COMMENT '请求时间',
  `log_type` varchar(100) DEFAULT NULL COMMENT '日志类型',
  `sys_code` varchar(255) DEFAULT NULL COMMENT '系统编码;项目id',
  `count` int DEFAULT NULL COMMENT '发生次数',
  `comment` varchar(255) DEFAULT NULL COMMENT '注释：重复原因',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4159 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='重复日志表';


-- aianadb.log_fetch_param definition

CREATE TABLE `log_fetch_param` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_trigger_id` int NOT NULL COMMENT 'job id',
  `log_type` varchar(255) DEFAULT NULL COMMENT '日志类型',
  `system_code` varchar(255) DEFAULT NULL COMMENT '系统编码',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `start_index` int DEFAULT '0' COMMENT '开始条数',
  `page_num` int DEFAULT '10' COMMENT '每页数',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='取log参数表';


-- aianadb.log_process definition

CREATE TABLE `log_process` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `trace_id` varchar(255) DEFAULT NULL COMMENT 'traceId',
  `request` varchar(255) DEFAULT NULL COMMENT '请求入口',
  `request_time` datetime DEFAULT NULL COMMENT '请求时间',
  `sys_code` varchar(255) DEFAULT NULL COMMENT '系统编码;项目id',
  `count` int DEFAULT NULL COMMENT '发生次数',
  `log_type` varchar(255) DEFAULT NULL COMMENT '日志类型',
  `job_trigger_id` varchar(255) DEFAULT NULL COMMENT 'job id',
  `status` varchar(255) DEFAULT NULL COMMENT '状态：进行中/已完成',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `trace_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'trace数据',
  `log_data` longtext COMMENT '日志数据',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `comment` varchar(100) DEFAULT NULL COMMENT '备注',
  `solution_id` bigint DEFAULT NULL COMMENT '解决方案id',
  `trace_hash` text COMMENT 'trac每个入口的hash',
  `source_type` varchar(100) DEFAULT NULL COMMENT '来源类型 JOB USER',
  `start_time` DATETIME COMMENT '开始时间',
  `end_time` DATETIME COMMENT '结束时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=834 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日志处理表';


-- aianadb.log_user definition

CREATE TABLE `log_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `trace_id` varchar(255) DEFAULT NULL COMMENT 'traceId',
  `request` varchar(255) DEFAULT NULL COMMENT '请求入口',
  `request_time` datetime DEFAULT NULL COMMENT '请求时间',
  `status` varchar(255) DEFAULT NULL COMMENT '状态：进行中/已完成',
  `user_id` bigint DEFAULT NULL COMMENT '用户id',
  `trace_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'trace数据',
  `log_process_id` bigint DEFAULT NULL COMMENT 'log_process_id',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `comment` varchar(100) DEFAULT NULL COMMENT '备注',
  `solution_id` bigint DEFAULT NULL COMMENT '解决方案id',
  `trace_hash` text COMMENT 'trac每个入口的hash',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=831 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户发布日志表';


-- aianadb.service definition

CREATE TABLE `service` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `service_id` varchar(255) DEFAULT NULL COMMENT '服务id',
  `service_name` varchar(255) DEFAULT NULL COMMENT '服务名称',
  `sys_code` varchar(255) DEFAULT NULL COMMENT '系统编码;项目id',
  `meta_data` varchar(900) DEFAULT NULL COMMENT '服务元数据',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务表';


-- aianadb.service_static_data definition

CREATE TABLE `service_static_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `service_id` varchar(255) DEFAULT NULL COMMENT '服务id',
  `type` varchar(255) DEFAULT NULL COMMENT '类型',
  `meta_data` varchar(255) DEFAULT NULL COMMENT '静态元数据',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务的静态数据表';


-- aianadb.solution definition

CREATE TABLE `solution` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `trace_id` varchar(255) DEFAULT NULL COMMENT 'traceId',
  `raw_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '解决方案',
  `trace_hash` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'trace_hash',
  `status` varchar(100) DEFAULT NULL COMMENT '方案状态',
  `confirm_status` varchar(100) DEFAULT NULL COMMENT '方案确认状态',
  `issued_status` varchar(100) DEFAULT NULL COMMENT '方案下发状态',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `generate_time` datetime COMMENT '方案生成时间',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '提交用户名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='解决方案表';


-- aianadb.sys definition

CREATE TABLE `sys` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sys_code` varchar(255) DEFAULT NULL COMMENT '系统编码;项目id',
  `meta_data` varchar(900) DEFAULT NULL COMMENT '系统元数据',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统表';

CREATE TABLE `solution_item` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `solution_id` BIGINT COMMENT '关联解决方案表id',
  `trace_id` VARCHAR(255) COMMENT 'trace_id',
  `trace_hash` TEXT COMMENT 'trace_hash',
  `system_code` VARCHAR(255) COMMENT '系统编号',
  `service_code` VARCHAR(255) COMMENT '服务编号',
  `problem_desc` TEXT COMMENT '问题说明',
  `ai_suggestion` TEXT COMMENT 'AI建议方案',
  `confirm_status` VARCHAR(255) COMMENT '确认状态',
  `confirm_by` VARCHAR(255) COMMENT '确认人',
  `confirm_time` DATETIME COMMENT '确认时间',
  `system_owner` VARCHAR(255) COMMENT '系统负责人',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户号',
  `version` int DEFAULT '0' COMMENT '乐观锁',
  `create_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modify_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '-1' COMMENT '更新人',
  `last_modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='解决方案明细表';


CREATE TABLE `log_stage` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `log_process_id` BIGINT COMMENT '关联log_process表id',
  `trace_id` VARCHAR(255) COMMENT 'trace_id',
  `trace_hash` TEXT COMMENT 'trace_hash',
  `solution_id` BIGINT COMMENT '关联解决方案表id',
  `stage_name` VARCHAR(255) COMMENT '阶段名称（如：读取日志、错误定位等）',
  `stage_status` VARCHAR(255) COMMENT '阶段状态（如：成功完成、失败完成、未完成等）',
  `start_time` DATETIME COMMENT '阶段开始时间',
  `end_time` DATETIME COMMENT '阶段结束时间',
  `duration_ms` INT COMMENT '阶段耗时（毫秒）',
  `comment` VARCHAR(1024) COMMENT '注释',
  `details` TEXT COMMENT '阶段详细信息或日志',
  `messages` TEXT COMMENT '预留字段',
  `tenant_id` VARCHAR(255) COMMENT '租户号',
  `version` INT COMMENT '乐观锁版本号',
  `create_user` VARCHAR(255) COMMENT '创建人',
  `create_time` DATETIME COMMENT '创建时间',
  `last_modify_user` VARCHAR(255) COMMENT '最后修改人',
  `last_modify_time` DATETIME COMMENT '最后修改时间',
  `catalog_name` varchar(100) COMMENT '所属阶段分类',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处理阶段表';

