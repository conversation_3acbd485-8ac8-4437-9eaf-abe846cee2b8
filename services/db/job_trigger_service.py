from datetime import datetime
from dao.job_trigger_dao import JobTriggerDAO, JobTrigger
from utils.logger import logger


class JobTriggerService:
    def __init__(self):
        self.dao = JobTriggerDAO()

    def create_job(self, job_data) -> int:
        """
        创建一个新的 JobTrigger 记录。
        :param job_data: 包含 job_name, trigger_time, status 等字段的字典
        :return: 返回生成的 job ID
        """
        try:
            logger.debug("Creating new job trigger...")
            if isinstance(job_data, dict):
                job = self._dict_to_job(job_data)
            else:
                job = job_data
            job.create_time = datetime.now().replace(microsecond=0)
            job.last_modify_time = datetime.now().replace(microsecond=0)
            job.version = 1
            return self.dao.create(job)
        except Exception as e:
            logger.error(f"Error occurred while creating job trigger: {e}")
            raise

    def update_job_status(self, job_id: int, new_status: str) -> bool:
        """
        更新指定 job 的状态，并自动更新 last_modify_time 和 version（乐观锁）
        :param job_id: job ID
        :param new_status: 新的状态值
        :return: 是否更新成功
        """
        try:
            job = self.dao.get_by_id(job_id)
            if not job:
                logger.warning(f"Job with ID {job_id} not found.")
                return False

            job.status = new_status
            fields_to_update = {"status": job.status}
            return self.dao.update(job, fields_to_update)
        except Exception as e:
            logger.error(f"Error occurred while updating job status: {e}")
            return False

    def cancel_job(self, job_id: int) -> bool:
        """
        取消指定 job（设置 status 为 '完成'）
        :param job_id: job ID
        :return: 是否取消成功
        """
        return self.update_job_status(job_id, "完成")

    def get_job_by_id(self, job_id: int):
        """
        根据 job ID 获取 job 数据
        :param job_id: job ID
        :return: JobTrigger 对象或 None
        """
        try:
            return self.dao.get_by_id(job_id)
        except Exception as e:
            logger.error(f"Error fetching job by ID: {e}")
            return None

    def get_all_jobs(self):
        """
        获取所有 job_trigger 记录
        :return: JobTrigger 列表
        """
        try:
            return self.dao.get_all()
        except Exception as e:
            logger.error(f"Error fetching all jobs: {e}")
            return []

    def _dict_to_job(self, data: dict) -> "JobTrigger":
        """
        将字典转换为 JobTrigger 对象
        """
        from dataclasses import asdict

        # 过滤掉不在 JobTrigger 中的字段
        valid_fields = {f: data[f] for f in data if f in asdict(JobTrigger()).keys()}
        return JobTrigger(**valid_fields)