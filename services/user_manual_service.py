import json
import os
from dataclasses import asdict
from datetime import datetime

from dotenv import load_dotenv

from common.common_enum import LogProcessStatus, SourceType, TracePriority, SolutionStatus
from common.constant import LOG_PROCESSING_QUEUE, VERSION_START
from common.response_model import Response
from services.db.log_duplicate_service import LogDuplicateService
from services.db.log_process_service import LogProcessService
from services.db.log_user_service import LogUserService
from services.db.solution_service import SolutionService
from utils.common_util import get_with_retry
from utils.external_api import fetch_trace_by_id
from utils.logger import logger
from utils.mq_client import RabbitMQClient
from utils.trace_util import process_and_validate_trace, get_trace_data_hash

load_dotenv()

class UserManualService:
    def __init__(self):
        self.log_process_service = LogProcessService()
        self.get_trace_data_url = os.getenv("GET_TRACE_DATA")
        self.solution_service = SolutionService()
        self.log_user_service = LogUserService()
        self.log_duplicate_service = LogDuplicateService()

    def send_mq_callback(self, success, mq_data):
        log_process = get_with_retry(lambda: self.log_process_service.get_log_process_by_id(mq_data["id"]),
                                     max_retries=1, delay=1)
        if not log_process:
            logger.error(f"日志处理结果不存在或丢失: {mq_data}")
            return

        if success:
            # 先插入一条solution占位
            solution = {"trace_id": log_process.trace_id,
                        "trace_hash": log_process.trace_hash,
                        "status": SolutionStatus.TO_BE_GENERATED.value,
                        "version": VERSION_START}
            solution_id = self.solution_service.create_solution(solution)
            # 更改状态为 processing
            self.log_process_service.update_common(log_process, {"solution_id": solution_id, "status": LogProcessStatus.PROCESSING.value})
            log_user = get_with_retry(lambda: self.log_user_service.get_by_id(mq_data["log_user_id"]),
                                         max_retries=1, delay=1)
            self.log_user_service.update_common(log_user, {"status": LogProcessStatus.PROCESSING.value, "solution_id": solution_id})
        else:
            # 失败则丢弃
            log_dup_data = asdict(log_process)
            log_dup_data.pop('id', None)
            log_dup_data["duplicate_log_id"] = log_process.id
            log_dup_data["status"] = LogProcessStatus.EXCEPTION.value
            log_dup_data["comment"] = "尝试发送至MQ重试超过最大限度，丢弃该记录"
            self.log_duplicate_service.create_log_duplicate(log_dup_data)
            # 删除该log_process
            self.log_process_service.delete_log_process(log_process.id)

    def submit_trace(self, user_id: int, trace_id: str, request_time: datetime) -> Response:
        """"用户提交一个特定的trace_id"""
        # 构造需要存储的数据
        request_time_str = request_time.strftime("%Y-%m-%d %H:%M:%S")
        log_user_data = {
            "trace_id": trace_id,
            "request_time": request_time,
            "user_id": user_id
        }
        # 0 先查log user表，确定自己提交过没有相同的traceId
        exist_log_user = self.log_user_service.get_by_trace_id_and_user_id(trace_id, user_id)
        if exist_log_user:
            return Response.fail(message="用户已经提交过相同trace id, 请勿重复提交", code=200)
        # 1 去重 相同traceId
        lp_same_trace_id = self.log_process_service.get_log_process_by_trace_id(trace_id)
        if lp_same_trace_id:
            log_user_data['log_process_id'] = lp_same_trace_id.id
            log_user_data['status'] = LogProcessStatus.DUPLICATED.value
            log_user_data['comment'] = "相同trace_id, 已有其他人提交"
            log_user_data['trace_data'] = lp_same_trace_id.trace_data
            log_user_data['solution_id'] = lp_same_trace_id.solution_id
            log_user_data['trace_hash'] = lp_same_trace_id.trace_hash
            log_user_data['request_time'] = lp_same_trace_id.request_time
            log_user_new_id = self.log_user_service.create(log_user_data)
            log_user_data['id'] = log_user_new_id
            log_user_data['log_process'] = lp_same_trace_id.to_dict()
            log_user_data['request_time'] = request_time_str
            return Response.succeed(data=log_user_data, message="用户提交成功: 但相同trace_id, 已有其他人提交")
        # 2 获取trace_data，异常
        trace_data = fetch_trace_by_id(trace_id, request_time_str)
        if not trace_data:
            log_user_data['status'] = LogProcessStatus.EXCEPTION.value
            log_user_data['comment'] = "未取得trace data, 请检查trace time是否同trace id匹配"
            logger.warning(f"Failed to fetch trace data for {trace_id}")
            log_user_new_id = self.log_user_service.create(log_user_data)
            log_user_data['id'] = log_user_new_id
            log_user_data['request_time'] = request_time_str
            return Response.fail(data=log_user_data, message="未取得trace data, 请检查trace time是否同trace id匹配", code=200)
        # 3 校验trace_data，异常
        trace_processed_data, has_non_empty_level = process_and_validate_trace(trace_data)
        if not has_non_empty_level:
            log_user_data["status"] = LogProcessStatus.EXCEPTION.value
            log_user_data["comment"] = "trace data中所有level都为空"
            log_user_new_id = self.log_user_service.create(log_user_data)
            log_user_data['id'] = log_user_new_id
            log_user_data['request_time'] = request_time_str
            return Response.fail(data=log_user_data, message="trace data中所有level都为空", code=200)

        # 4 trace hash去重
        trace_hash = get_trace_data_hash(trace_processed_data)
        trace_by_same_hash = self.log_process_service.get_by_trace_hash(trace_hash)
        if trace_by_same_hash:
            log_user_data['log_process_id'] = trace_by_same_hash.id
            log_user_data['status'] = LogProcessStatus.DUPLICATED.value
            log_user_data['comment'] = "相同trace_hash, 已有其他人提交"
            log_user_data['trace_data'] = trace_by_same_hash.trace_data
            log_user_data['solution_id'] = trace_by_same_hash.solution_id
            log_user_data['trace_hash'] = trace_by_same_hash.trace_hash
            log_user_data['request_time'] = trace_by_same_hash.request_time
            log_user_new_id = self.log_user_service.create(log_user_data)
            log_user_data['id'] = log_user_new_id
            log_user_data['log_process'] = trace_by_same_hash.to_dict()
            log_user_data['request_time'] = request_time_str
            return Response.succeed(data=log_user_data, message="用户提交成功: 但相同trace_hash, 已有其他人提交")

        # 校验通过 开始发送消息
        # 2 再插入一条log process，真正发送的记录
        trace_data_str = json.dumps(trace_processed_data, ensure_ascii=False, indent=2)
        log_data = {
            "trace_id": trace_id,
            "request_time": request_time,
            "trace_data": trace_data_str,
            "user_id": user_id,
            "trace_hash": trace_hash,
            "source_type": SourceType.USER.value,
            "status": LogProcessStatus.CREATED.value
        }
        logger.info(f"Inserting log_process data: {log_data}")

        log_process_id = self.log_process_service.create_log_process(log_data)
        log_data['id'] = log_process_id
        logger.info(f"[USER:{user_id}]Successfully saved log with trace_id: {log_data['trace_id']} and id: {log_process_id}")
        # 3 插入用户记录
        log_user_data['log_process_id'] = log_process_id
        log_user_data['status'] = LogProcessStatus.CREATED.value
        log_user_data['comment'] = f"由用户第一次提交,用户ID:{user_id}"
        log_user_data['trace_data'] = trace_data_str
        log_user_data['trace_hash'] = trace_hash
        log_user_new_id = self.log_user_service.create(log_user_data)
        log_user_data['id'] = log_user_new_id
        log_user_data['log_process'] = log_data
        log_user_data['request_time'] = request_time_str
        # 向mq推送消息
        mq_data = {
            "id": log_data["id"],
            "trace_id": log_data["trace_id"],
            "request_time": request_time_str,
            "send_type": SourceType.USER.value,
            "log_user_id": log_user_new_id
        }
        rabbitmq_client = RabbitMQClient("producer")
        mq_result = rabbitmq_client.publish_message(LOG_PROCESSING_QUEUE, mq_data, priority=TracePriority.MANUAL.value, callback=self.send_mq_callback)
        if mq_result:
            logger.info(f"[USER:{user_id}] Published message to RabbitMQ: {mq_data}")
            return Response.succeed(data=log_user_data, message="用户提交成功")
        else:
            logger.warning(
                f"[USER:{user_id}] Failed to publish message to RabbitMQ after waiting: {log_data}")
            return Response.fail(data=log_user_data, message="用户提交失败")