# log_handle_service.py
import asyncio
import json
import os
import random
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Any

import pika
import requests
from dotenv import load_dotenv

from common.common_enum import LogProcessStatus, SolutionStatus
from services.db.log_duplicate_service import LogDuplicateService
from services.db.log_process_service import LogProcessService
from services.db.solution_service import SolutionService
from services.logs_analysis_service import process_log_analysis_from_external_data
from utils.common_util import get_with_retry
from utils.get_token import get_api_gateway_token
from utils.logger import logger
from utils.mq_client import RabbitMQClient

load_dotenv()


class LogHandleService:
    def __init__(self, queue_name: str = "log_processing_queue"):
        self.queue_name = queue_name
        self.rabbitmq_client = RabbitMQClient("consumer")
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.rabbitmq_clients = []
        self.get_log_data_url = os.getenv("GET_LOG_DATA")
        self.logProcessService = LogProcessService()
        self.logDuplicateService = LogDuplicateService()
        self.solutionService = SolutionService()

    def _fetch_trace_log_send_to_ai(self, log_data: dict) -> dict:
        log_process = get_with_retry(lambda: self.logProcessService.get_log_process_by_id(log_data["id"]),
                                     max_retries=1, delay=1)
        if not log_process:
            logger.error(f"日志处理结果不存在: {log_process}")
            return {
                "success": False,
                "message": "日志处理结果不存在",
                "traceId": log_data["trace_id"]
            }

        if log_process.status != LogProcessStatus.CREATED.value:
            logger.info(f"日志处理结果已处理完成: {log_process}")
            return {
                "success": False,
                "message": "重复消息，日志已经在处理中",
                "traceId": log_data["trace_id"]
            }


        """消费mq消息查询trace和log"""
        logger.info(f"""消费者开始日志处理: {log_data['trace_id']} 请求时间: {log_data['request_time']}""")
        log_info_data = self.fetch_log_by_trace_id(log_data["trace_id"], log_data["request_time"])

        # 将 JSON 数据转换为字符串
        log_info_data_str = json.dumps(log_info_data, ensure_ascii=False, indent=2) if log_info_data else None

        self.logProcessService.update_common(log_process, {"log_data": log_info_data_str,
                                                           "status": LogProcessStatus.AI_PROCESSING.value})

        # 调用LLM走后续流程
        log_process_to_dict = log_process.to_dict()
        log_process_to_dict["trace_data"] = json.loads(log_process.trace_data)
        log_process_to_dict["log_data"] = log_info_data
        # logger.info(f"发送给llm的日志处理结果: {log_process_to_dict}")
        logger.info(f"发送给llm的日志处理结果:{log_process_to_dict['trace_id']}")
        result = process_log_analysis_from_external_data(request_data=log_process_to_dict, show_debug=True)
        # result = self.mock_process_log_analysis(request_data=log_process_to_dict, show_debug=True)
        logger.info(f"LLM处理结果: {result}")
        if result.get("success"):
            self.logProcessService.update_common(log_process, {"status": LogProcessStatus.DONE.value})
        else:
            self.logProcessService.update_common(log_process, {"status": LogProcessStatus.EXCEPTION.value})

        logger.info(f"结束发送")

        return {
            "traceId": log_data["trace_id"]
        }

    def mock_process_log_analysis(self, request_data, show_debug=False):
        time.sleep(3000000)
        """
        Mock 方法，随机返回 success: True 或 False
        """
        return {
            "success": random.choice([True, False]),
            "data": None,
            "error": None if random.choice([True, False]) else "模拟错误信息",
            "request_id": random.randint(1000, 9999),
            "trace_id": "mock_trace_id"
        }

    def process_and_validate_trace(self, data):
        """
        递归处理 trace 数据：
        1. 加工：清理 servername 字段
        2. 校验：判断是否所有 level 都为空字符串
        :param data: trace_data 的 data 字段（list 或 dict）
        :return: (processed_data, has_non_empty_level)
        """

        def process_node(node):
            # 处理单个节点
            if isinstance(node, dict):
                # 加工 servername
                if "servername" in node and "[" in node["servername"]:
                    node["servername"] = node["servername"].split("[")[0]
                # 检查 level 是否非空
                nonlocal has_non_empty_level
                if node.get("level") != "":
                    has_non_empty_level = True
                # 递归处理 children
                if "children" in node and isinstance(node["children"], list):
                    for child in node["children"]:
                        process_node(child)

        has_non_empty_level = False

        if isinstance(data, list):
            for item in data:
                process_node(item)
        else:
            process_node(data)

        return data, has_non_empty_level

    def safe_basic_ack(self, ch, delivery_tag):
        if not ch.connection or ch.connection.is_closed or not ch.is_open:
            print("Channel is closed, cannot send ack.")
            return
        ch.basic_ack(delivery_tag=delivery_tag)

    def safe_basic_nack(self, ch, delivery_tag, requeue=True):
        if not ch.connection or ch.connection.is_closed or not ch.is_open:
            print("Channel is closed, cannot send nack.")
            return
        ch.basic_nack(delivery_tag=delivery_tag, requeue=requeue)

    def process_log_message(self, ch: Any, method: Any, properties: Any, body: bytes):
        log_data = json.loads(body.decode())
        print(f"Received log data from RabbitMQ: {log_data}")
        try:
            # 提交任务到线程池
            # future = self.executor.submit(self._fetch_trace_and_log, log_data)
            # result = future.result()  # 阻塞等待结果
            result = self._fetch_trace_log_send_to_ai(log_data)

            logger.info(f"LLM Analysis Result: {result}")
            self.safe_basic_ack(ch, method.delivery_tag)
        except (pika.exceptions.StreamLostError, pika.exceptions.ChannelWrongStateError) as e:
            logger.error(f"Channel or connection is broken: {e}. Reconnecting...")
            # 不再尝试 ack/nack，直接退出当前循环让 consumer 自动重连
            raise
        # 确认消息已处理
        except Exception as e:
            logger.exception(f"Error processing log message: {e}")
            self.safe_basic_nack(ch, method.delivery_tag) # 消息处理失败，重新入队

    def start_consuming(self):
        self.rabbitmq_client.consume_messages(self.queue_name, self.process_log_message)


    def start_multiple_consumers(self, num_workers=5):
        """启动多个消费者线程，每个线程使用不同的 consumer 角色"""

        def run_consumer(index):
            print(f"Starting consumer {index}")
            # 每个消费者使用独立的 client 实例
            rabbitmq_client = RabbitMQClient(f"consumer{index}")
            self.rabbitmq_clients.append(rabbitmq_client)  # 保存 client
            rabbitmq_client.consume_messages(self.queue_name, self.process_log_message)

        for i in range(num_workers):
            thread = threading.Thread(target=run_consumer, args=(i + 1,))
            thread.daemon = True  # 守护线程，主程序退出时自动关闭
            thread.start()
            print(f"Consumer thread {i + 1} started.")

    async def run_async(self):
        """异步启动日志处理服务"""
        print(f"Starting to consume messages from queue: {self.queue_name}")
        while True:
            try:
                await asyncio.to_thread(self.start_consuming)
            except Exception as e:
                logger.error(f"Consumer crashed: {e}, restarting in 5s...")
                await asyncio.sleep(5)

    # def stop(self):
    #     """停止消费"""
    #     if self.rabbitmq_client.connection and self.rabbitmq_client.connection.is_open:
    #         self.rabbitmq_client.connection.close()
    #     self.executor.shutdown(wait=False)  # 关闭线程池

    def stop(self):
        """停止消费，关闭所有消费者连接"""
        print("Stopping all consumers...")
        for client in self.rabbitmq_clients:
            if client.connection and client.connection.is_open:
                print(f"Closing connection for {client.role}")
                client.connection.close()
        self.executor.shutdown(wait=False)  # 关闭线程池

    # log_handle_service.py

    def fetch_log_by_trace_id(self, trace_id: str, time: str):
        """
        调用外部接口根据 trace_id 查询日志信息
        :param trace_id: 链路ID
        :param time: 请求时间 (格式如 "2025-06-13 13:54:16")
        :return: 接口返回的日志数据列表（data字段），失败返回 None
        """
        token = get_api_gateway_token()
        params = {
            "traceId": trace_id,
            "time": time,
            "access_token": token
        }

        try:
            response = requests.get(self.get_log_data_url, params=params, timeout=100)
            response.raise_for_status()
            result = response.json()
            # logger.info(f"获取日志数据结果: {result}")
            if result.get("success"):
                return result.get("data")
            else:
                logger.error(f"Failed to fetch log data for {trace_id}: {result.get('message')}")
                return None
        except requests.RequestException as e:
            logger.exception(f"Error querying log data for {trace_id}: {e}")
            return None


        # if __name__ == "__main__":

    def regen_solution(self, log_process):
        # 先插入一条solution占位
        solution = {"trace_id": log_process.trace_id, "trace_hash": log_process.trace_hash,
                    "status": SolutionStatus.TO_BE_GENERATED.value, "version": log_process.version + 1}
        solution_id = self.solutionService.create_solution(solution)
        # 更改状态为 processing
        self.logProcessService.update_common(log_process, {"solution_id": solution_id,
                                                           "status": LogProcessStatus.AI_PROCESSING.value,
                                                           "version": log_process.version + 1})

        # 调用LLM走后续流程
        log_process_to_dict = log_process.to_dict()
        if log_process.trace_data:
            log_process_to_dict["trace_data"] = json.loads(log_process.trace_data)
        if log_process.log_data:
            log_process_to_dict["log_data"] = json.loads(log_process.log_data)

        # logger.info(f"发送给llm的日志处理结果: {log_process_to_dict}")
        logger.info(f"再次诊断发送给llm的日志处理结果:{log_process_to_dict['trace_id']}")
        result = process_log_analysis_from_external_data(request_data=log_process_to_dict, show_debug=True)
        # result = self.mock_process_log_analysis(request_data=log_process_to_dict, show_debug=True)
        logger.info(f"再次诊断LLM处理结果: {result}")
        if result.get("success"):
            self.logProcessService.update_common(log_process, {"status": LogProcessStatus.DONE.value})
        else:
            self.logProcessService.update_common(log_process, {"status": LogProcessStatus.EXCEPTION.value})

        logger.info(f"再次诊断结束发送")
        return True

#     log_processor = LogProcessorService()
#     log_processor.run()
