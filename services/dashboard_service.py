from typing import List

from common.common_enum import StageCatalog, StageName, StageStatus
from dao.log_process_dao import LogProcess
from services.db.log_process_service import LogProcessService
from services.db.log_stage_service import LogStageService
from services.vo.log_dashboard_req_vo import LogProcessDashboardParams
from services.vo.log_process_solution_vo import LogProcessSolution


class DashboardService:
    def __init__(self):
        self.log_process_service = LogProcessService()
        self.log_stage_service = LogStageService()

    def get_log_process_list(self, req: LogProcessDashboardParams) -> List[LogProcessSolution]:
        return self.log_process_service.get_log_process_list(req)

    def get_log_process_stage(self, log_process: LogProcess) -> list[dict]:
        # 查询实际存在的阶段
        existing_stages = self.log_stage_service.get_log_stages_by_process_id_version(log_process.id, log_process.version)

        # 定义所有阶段及其子阶段
        all_stages = [
            {"name": StageName.READ_LOG.value, "catalog": StageCatalog.READ_LOG.value, "sub_stages": []},
            {"name": StageName.ERROR_LOCATION.value, "catalog": StageCatalog.ERROR_LOCATION.value, "sub_stages": []},
            {
                "name": StageCatalog.ROOT_CAUSE_DIAGNOSIS.value,
                "catalog": StageCatalog.ROOT_CAUSE_DIAGNOSIS.value,
                "sub_stages": [
                    StageName.CODE_DIAGNOSIS.value,
                    StageName.DDL_DIAGNOSIS.value,
                    StageName.MIDDLEWARE_RESOURCE_DIAGNOSIS.value,
                    StageName.K8S_RESOURCE_DIAGNOSIS.value
                ]
            },
            {"name": StageName.SOLUTION_GENERATION.value, "catalog": StageCatalog.SOLUTION_GENERATION.value,
             "sub_stages": []},
            {"name": StageName.AI_JUDGEMENT.value, "catalog": StageCatalog.AI_JUDGEMENT.value, "sub_stages": []},
            {"name": StageName.HUMAN_CONFIRMATION.value, "catalog": StageCatalog.HUMAN_CONFIRMATION.value,
             "sub_stages": []},
            {"name": StageName.ISSUE_DISPATCH.value, "catalog": StageCatalog.ISSUE_DISPATCH.value, "sub_stages": []}
        ]

        # 初始化展示数据
        display_stages = []
        for stage in all_stages:
            stage_info = {
                "stageName": stage["name"],
                "status": StageStatus.UN_STARTED.value,  # 默认状态为未完成
                "startTime": None,
                "endTime": None,
                "stageId": None,
                "invisible": False,
                "parallel": None,
                "parentName": None,
                "subStages": []
            }

            # 检查是否存在该阶段的信息
            existing_stage = next((s for s in existing_stages if s.stage_name == stage["name"]), None)
            if existing_stage:
                stage_info.update({
                    "status": existing_stage.stage_status,
                    "startTime": existing_stage.start_time,
                    "endTime": existing_stage.end_time,
                    "stageId": existing_stage.id,
                    "invisible": False  # 根据需求调整
                })

            # 处理子阶段
            if stage["sub_stages"]:
                sub_stages_info = []
                for sub_stage_name in stage["sub_stages"]:
                    sub_stage_info = {
                        "stageName": sub_stage_name,
                        "status": StageStatus.UN_STARTED.value,  # 默认状态为未完成
                        "startTime": None,
                        "endTime": None,
                        "stageId": None,
                        "invisible": False,
                        "parallel": False,
                        "parentName": stage["name"],
                        "subStages": None
                    }

                    existing_sub_stage = next((s for s in existing_stages if s.stage_name == sub_stage_name), None)
                    if existing_sub_stage:
                        sub_stage_info.update({
                            "status": existing_sub_stage.stage_status,
                            "startTime": existing_sub_stage.start_time,
                            "endTime": existing_sub_stage.end_time,
                            "stageId": existing_sub_stage.id
                        })

                    sub_stages_info.append(sub_stage_info)

                stage_info["subStages"] = sub_stages_info

            display_stages.append(stage_info)

        return display_stages