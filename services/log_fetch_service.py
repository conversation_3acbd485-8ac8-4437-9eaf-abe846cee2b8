import json
import os
from dataclasses import asdict
from datetime import datetime, timedelta

import requests
from dotenv import load_dotenv

from common.common_enum import JobStatus, LogType, LogProcessStatus, SourceType, TracePriority, SolutionStatus
from common.constant import LOG_PROCESSING_QUEUE, JOB_MAX_MINUS_DAY, JOB_PAGE_SIZE, JOB_MIN_NUM_PER_TYPE, VERSION_START
from dao.job_trigger_dao import JobTrigger
from services.db.job_trigger_service import JobTriggerService
from services.db.log_duplicate_service import LogDuplicateService
from services.db.log_process_service import LogProcessService
from services.db.solution_service import SolutionService
from utils.common_util import get_with_retry
from utils.external_api import fetch_trace_by_id
from utils.get_token import get_api_gateway_token
from utils.logger import logger
from utils.mq_client import RabbitMQClient
from utils.trace_util import get_trace_data_hash, process_and_validate_trace

load_dotenv()
log_urls_map = {
    LogType.HTTP_REQ_SLOW.value: os.getenv("GET_HTTP_REQ_SLOW_INFO", "https://apps-fc-monitor.faw.cn/out-inter/rootCauseAnaController/getHttpReqSlowInfo"),
    LogType.RESOURCE_LOAD_SLOW.value: os.getenv("GET_RESOURCE_LOAD_SLOW_INFO", "https://apps-fc-monitor.faw.cn/out-inter/rootCauseAnaController/getResourceLoadSlowInfo"),
    LogType.WHITE_SCREEN_TIME_SLOW.value: os.getenv("GET_WHITE_SCREEN_TIME_SLOW_INFO", "https://apps-fc-monitor.faw.cn/out-inter/rootCauseAnaController/getWhiteScreenTimeSlowInfo"),
    LogType.ACCESS_USE_CASE_SLOW.value: os.getenv("GET_ACCESS_USE_CASE_SLOW_INFO", "https://apps-fc-monitor.faw.cn/out-inter/rootCauseAnaController/getAccessUseCaseSlowInfo"),
    LogType.HTTP_REQ_40_50.value: os.getenv("GET_HTTP_REQ_40_50_INFO", "https://apps-fc-monitor.faw.cn/out-inter/rootCauseAnaController/getHttpReq4050Info")
}


class LogFetchService:
    def __init__(self):
        self.job_trigger_service = JobTriggerService()
        self.log_process_service = LogProcessService()
        self.log_duplicate_service = LogDuplicateService()
        self.solution_service = SolutionService()

    def send_mq_callback(self, success, mq_data):
        log_process = get_with_retry(lambda: self.log_process_service.get_log_process_by_id(mq_data["id"]),
                                     max_retries=1, delay=1)
        if not log_process:
            logger.error(f"日志处理结果不存在或丢失: {mq_data}")
            return

        if success:
            # 先插入一条solution占位
            solution = {"trace_id": log_process.trace_id,
                        "trace_hash": log_process.trace_hash,
                        "status": SolutionStatus.TO_BE_GENERATED.value,
                        "version": VERSION_START}
            solution_id = self.solution_service.create_solution(solution)
            # 更改状态为 processing
            self.log_process_service.update_common(log_process, {"solution_id": solution_id, "status": LogProcessStatus.PROCESSING.value})
        else:
            # 失败则丢弃
            log_dup_data = asdict(log_process)
            log_dup_data.pop('id', None)
            log_dup_data["duplicate_log_id"] = log_process.id
            log_dup_data["status"] = LogProcessStatus.EXCEPTION.value
            log_dup_data["comment"] = "尝试发送至MQ重试超过最大限度，丢弃该记录"
            self.log_duplicate_service.create_log_duplicate(log_dup_data)
            # 删除该log_process
            self.log_process_service.delete_log_process(log_process.id)

    def fetch_log_by_type(self, end_time, job_type, system_code):
        """
        调用接口获取日志，并增加详细日志输出
        """
        start_time = end_time - timedelta(days=JOB_MAX_MINUS_DAY)
        job = JobTrigger(job_name=f"{job_type}", trigger_time=start_time, status=JobStatus.PROCESSING.value)
        job_id = self.job_trigger_service.create_job(job)

        url = log_urls_map.get(job_type)
        if not url:
            logger.error(f"[JobID: {job_id}] Invalid job type: {job_type}")
            return []

        # 格式化时间参数
        time_fmt = "%Y-%m-%d %H:%M:%S"
        start, end = 1, JOB_PAGE_SIZE  # 初始分页参数

        sent_msgs = []
        fetched_count = 0

        while fetched_count < JOB_MIN_NUM_PER_TYPE:
            token = get_api_gateway_token()
            params = [
                f"startTime={start_time.strftime(time_fmt)}",
                f"endTime={end_time.strftime(time_fmt)}",
                f"start={start}",
                f"end={end}",
                f"access_token={token}"
            ]
            if system_code:
                params.append(f"systemCode={system_code}")

            request_url = f"{url}?{'&'.join(params)}"

            logger.info(f"[JobID: {job_id}] Fetching logs with start={start}, end={end}, URL: {request_url}")

            try:
                # 调用外部API
                response = requests.get(request_url)
                response.raise_for_status()
                logger.info(f"[JobID: {job_id}] {job_type} log fetch success")

                # 解析响应数据
                response_data = response.json()
                if not response_data.get("success"):
                    logger.error(f"[JobID: {job_id}] Failed to fetch logs. Response: {response_data}")
                    continue

                data_list = response_data.get("data", [])
                logger.info(f"[JobID: {job_id}] Received {len(data_list)} items from API")

                count_duplicate_or_error = 0
                count_loop_new_logs = 0

                for item in data_list:
                    trace_id = item.get("traceId")
                    sys_code = item.get("systemCode")
                    request_url_item = item.get("requestUrl")
                    timestamp = item.get("timestamp")

                    if not all([trace_id, sys_code, request_url_item, timestamp]):
                        logger.warning(f"[JobID: {job_id}] Missing required fields in item: {item}")
                        continue

                    try:
                        request_time = datetime.strptime(timestamp, time_fmt)
                    except ValueError as ve:
                        logger.error(f"[JobID: {job_id}] Invalid timestamp format: {timestamp}, error: {ve}")
                        continue

                    # 构造需要存储的数据
                    log_data = {
                        "trace_id": trace_id,
                        "sys_code": sys_code,
                        "count": item.get("count"),
                        "request": request_url_item,
                        "request_time": request_time,
                        "log_type": job_type,
                        "job_trigger_id": job_id,
                    }

                    # 1 去重 相同traceId
                    lp_same_trace_id = self.log_process_service.get_log_process_by_trace_id(trace_id)
                    if lp_same_trace_id:
                        log_data["duplicate_log_id"] = lp_same_trace_id.id
                        log_data["status"] = LogProcessStatus.DUPLICATED.value
                        log_data["comment"] = "相同trace_id"
                        self.log_duplicate_service.create_log_duplicate(log_data)
                        count_duplicate_or_error += 1
                        continue
                    # 2 获取trace_data，异常
                    trace_data = fetch_trace_by_id(trace_id, timestamp)
                    if not trace_data:
                        logger.warning(f"[JobID: {job_id}] Failed to fetch trace data for {trace_id}")
                        log_data["status"] = LogProcessStatus.EXCEPTION.value
                        log_data["comment"] = "未取得trace data"
                        self.log_duplicate_service.create_log_duplicate(log_data)
                        count_duplicate_or_error += 1
                        continue
                    # 3 校验trace_data，异常
                    trace_processed_data, has_non_empty_level = process_and_validate_trace(trace_data)
                    if not has_non_empty_level:
                        log_data["status"] = LogProcessStatus.EXCEPTION.value
                        log_data["comment"] = "trace data中所有level都为空"
                        self.log_duplicate_service.create_log_duplicate(log_data)
                        count_duplicate_or_error += 1
                        continue

                    # 4 trace hash去重
                    trace_hash = get_trace_data_hash(trace_processed_data)
                    trace_by_same_hash = self.log_process_service.get_by_trace_hash(trace_hash)
                    if trace_by_same_hash:
                        log_data["status"] = LogProcessStatus.DUPLICATED.value
                        log_data["duplicate_log_id"] = trace_by_same_hash.id
                        log_data["comment"] = "trace hash重复"
                        self.log_duplicate_service.create_log_duplicate(log_data)
                        count_duplicate_or_error += 1
                        continue


                    if fetched_count >= JOB_MIN_NUM_PER_TYPE:
                        break

                    # 校验通过 发送消息
                    trace_data_str = json.dumps(trace_processed_data, ensure_ascii=False, indent=2)
                    log_data["trace_data"] = trace_data_str
                    log_data["status"] = LogProcessStatus.CREATED.value
                    log_data["trace_hash"] = trace_hash
                    log_data["source_type"] = SourceType.JOB.value
                    logger.info(f"[JobID: {job_id}] Saving log with trace_id: {log_data['trace_id']}")
                    logger.info(f"[JobID: {job_id}] log_data: {log_data}")
                    log_process_id = self.log_process_service.create_log_process(log_data)
                    log_data["id"] = log_process_id
                    count_loop_new_logs += 1
                    fetched_count += 1
                    logger.info(f"[JobID: {job_id}] Successfully saved log with trace_id: {log_data['trace_id']}")

                    # 向mq推送消息
                    # 将request_time由datetime改回str
                    log_data["request_time"] = timestamp
                    mq_data = {
                        "id": log_data["id"],
                        "trace_id": log_data["trace_id"],
                        "request_time": timestamp,
                        "send_type": SourceType.JOB.value
                    }
                    rabbitmq_client = RabbitMQClient("producer")
                    mq_result = rabbitmq_client.publish_message(LOG_PROCESSING_QUEUE, mq_data, priority=TracePriority.JOB.value, callback=self.send_mq_callback)
                    if mq_result:
                        mq_data = {
                            "id": log_data["id"],
                            "trace_id": log_data["trace_id"],
                            "request_time": log_data["request_time"],
                            "request": log_data["request"],
                            "sys_code": log_data["sys_code"],
                            "log_type": log_data["log_type"]
                        }
                        logger.info(f"[JobID: {job_id}] Published message to RabbitMQ: {mq_data}")
                        sent_msgs.append(mq_data)
                    else:
                        logger.warning(
                            f"[JobID: {job_id}] Failed to publish message to RabbitMQ after waiting: {log_data}")


                # 输出本次loop处理统计
                logger.info(f"[JobID: {job_id}] Processed {len(data_list)} items: "
                            f"{count_duplicate_or_error} duplicated or error, "
                            f"{count_loop_new_logs} loop new logs, "
                            f"{fetched_count} total new logs, ")

                # 检查是否有更多页面
                if len(data_list) < (end - start + 1):
                    logger.info(f"[JobID: {job_id}] No more pages, exiting loop.")
                    break  # 当前页数据少于请求量，表示没有更多数据

                start += JOB_PAGE_SIZE
                end += JOB_PAGE_SIZE

            except requests.RequestException as re:
                logger.error(f"[JobID: {job_id}] Request failed for {job_type}: {re}")

        return sent_msgs





