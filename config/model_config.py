"""
模型配置管理模块

管理不同处理阶段使用的LLM模型配置，支持RESTful API和传统OpenAI兼容接口
"""

import os
import time
import requests

from typing import List, Dict, Any, Optional, Union, Iterator, ClassVar
from langchain_openai import ChatOpenAI
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, SystemMessage
from langchain_core.outputs import ChatR<PERSON>ult, ChatGeneration
import json

from dotenv import load_dotenv
from utils.logger import logger

# 导入token获取工具
from utils.get_token import get_api_prod_gateway_token

load_dotenv()

# 集中管理所有模型参数
LLM_CONFIG = {
    "DeepSeek-R1": {
        "API_BASE": os.getenv("DEEPSEEK_R1_ENDPOINT", "https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/DEEPSEEK-R1-671B/completions"),
        "MODEL": "DeepSeek-R1",
        "TEMPERATURE": 0.0,
        "TIMEOUT": 1200
    },
    "qwen3-32B": {
        "API_BASE": os.getenv("QWEN3_32B_ENDPOINT", "https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/QWEN3-32B/completions"),
        "MODEL": "qwen3-32B",
        "TEMPERATURE": 0.0,
        "TIMEOUT": 1200
    }
}


class CustomRestfulLLM(BaseChatModel):
    """自定义 LLM 包装器，用于调用 RESTful 接口"""
    model: str = "DeepSeek-R1"
    api_base: str = ""
    temperature: float = 0.0
    timeout: int = 1200
    streaming: bool = False
    max_retries: int = 1

    def __init__(self, model: str = "DeepSeek-R1", **kwargs):
        config = LLM_CONFIG.get(model, LLM_CONFIG["DeepSeek-R1"])
        super().__init__(
            model=config["MODEL"],
            api_base=config["API_BASE"],
            temperature=config.get("TEMPERATURE", 0.0),
            timeout=config.get("TIMEOUT", 1200),
            streaming=kwargs.get("streaming", False),
            max_retries=kwargs.get("max_retries", 1)
        )
    
    @property
    def _llm_type(self) -> str:
        """返回 LLM 类型标识符"""
        return "custom_restful"
    
    def _get_api_url_for_model(self, model_name: str) -> str:
        """为指定模型获取API URL"""
        config = LLM_CONFIG.get(model_name, LLM_CONFIG["DeepSeek-R1"])
        base_url = config["API_BASE"]
        
        access_token = get_api_prod_gateway_token()
        if not access_token:
            raise Exception("无法获取access_token")
        return f"{base_url}?access_token={access_token}"
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """生成响应 - 简化版本"""
        logger.info(f"🚀 开始生成响应，使用模型: {self.model}")
        
        # 转换消息格式
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, SystemMessage):
                formatted_messages.append({"role": "system", "content": msg.content})
            elif isinstance(msg, HumanMessage):
                formatted_messages.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                formatted_messages.append({"role": "assistant", "content": msg.content})
        
        # 构建请求参数
        request_data = {
            "model": self.model,
            "messages": formatted_messages,
            "stream": self.streaming,
            "temperature": self.temperature
        }
        
        try:
            # 直接调用API，不使用重试机制
            api_url = self._get_api_url_for_model(self.model)
            
            logger.info(f"🎯 直接调用 {self.model} API...")
            
            response = requests.post(
                api_url,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # 解析响应
            response_data = response.json()
            
            # 提取响应内容
            content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            if not content:
                # 如果内容为空，尝试其他可能的响应格式
                content = response_data.get("content", "")
                if not content:
                    content = str(response_data)
            
            logger.info(f"✅ {self.model} API 调用成功")
            
            # 创建 AIMessage
            ai_message = AIMessage(content=content)
            
            # 返回 ChatResult
            return ChatResult(
                generations=[ChatGeneration(message=ai_message)]
            )
            
        except Exception as e:
            logger.error(f"❌ {self.model} API 调用失败: {str(e)}")
            # 返回降级响应
            return self._get_fallback_response()
    
    def _get_fallback_response(self) -> ChatResult:
        """获取降级响应"""
        # logger.info("API 调用完全失败，使用降级方案")
        fallback_content = f"""API调用失败，无法获取智能分析结果。"""
        
        ai_message = AIMessage(content=fallback_content)
        return ChatResult(
            generations=[ChatGeneration(message=ai_message)]
        )
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """异步生成响应（暂时使用同步实现）"""
        return self._generate(messages, stop, run_manager, **kwargs)


def clean_analysis_content(content: str) -> str:
    """清理分析内容，移除 <think></think> 标签及其内容，以及标签之前的所有内容
    
    Args:
        content: 原始内容
        
    Returns:
        清理后的内容
    """
    import re
    
    # 移除从文本开始到 </think> 标签结束的所有内容（支持多行）
    cleaned_content = re.sub(r'^.*?</think>', '', content, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除剩余的 <think>...</think> 标签及其内容（支持多行）
    cleaned_content = re.sub(r'<think>.*?</think>', '', cleaned_content, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除可能的孤立标签
    cleaned_content = re.sub(r'</?think>', '', cleaned_content, flags=re.IGNORECASE)
    
    # 清理多余的空行
    cleaned_content = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_content)
    
    # 移除开头和结尾的空白字符
    cleaned_content = cleaned_content.strip()
    
    return cleaned_content


class ModelConfig:
    """管理LLM模型配置（精简+LLM_CONFIG驱动）"""
    def __init__(self,):
        # 只保留阶段到模型名的映射
        self.configs = {
            "error_location": {"model": "DeepSeek-R1"},
            "code_context": {"model": "DeepSeek-R1"},
            "problem_analysis": {"model": "DeepSeek-R1"}
        }

    def get_model(self, stage: str) -> CustomRestfulLLM:
        if stage not in self.configs:
            raise ValueError(f"未知的处理阶段: {stage}，可用阶段: {list(self.configs.keys())}")
        model_name = self.configs[stage]["model"]
        if model_name not in LLM_CONFIG:
            logger.info(f"警告: 模型 {model_name} 不在支持列表中，支持的模型: {list(LLM_CONFIG.keys())}")
            logger.info("回退到默认模型: DeepSeek-R1")
            model_name = "DeepSeek-R1"
        return CustomRestfulLLM(model=model_name)

    def switch_all_to_model(self, model_name: str):
        if model_name not in LLM_CONFIG:
            raise ValueError(f"不支持的模型: {model_name}，支持的模型: {list(LLM_CONFIG.keys())}")
        for stage_config in self.configs.values():
            stage_config["model"] = model_name
        logger.info(f"已将所有处理阶段切换到模型: {model_name}")

    def get_supported_models(self) -> List[str]:
        return list(LLM_CONFIG.keys())

    def update_config(self, stage: str, **kwargs):
        if stage not in self.configs:
            self.configs[stage] = {}
        self.configs[stage].update(kwargs) 