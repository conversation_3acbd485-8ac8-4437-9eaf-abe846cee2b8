#!/usr/bin/env python3
"""
启动Streamlit测试界面
"""

import subprocess
import sys
import os

def main():
    """启动Streamlit测试界面"""
    print("🚀 启动Streamlit日志分析测试界面")
    print("=" * 60)
    
    # 检查streamlit是否安装
    try:
        import streamlit
        print("✅ Streamlit已安装")
    except ImportError:
        print("❌ Streamlit未安装，请先安装: pip install streamlit")
        return
    
    # 检查主文件是否存在
    main_file = "streamlit_main.py"
    if not os.path.exists(main_file):
        print(f"❌ 找不到主文件: {main_file}")
        return
    
    print(f"✅ 找到主文件: {main_file}")
    print("\n📋 新功能说明:")
    print("🎨 前端问题分析流程:")
    print("   - 支持 resource_load_slow, access_use_slow, white_screen_time_slow")
    print("   - 自动识别前端资源文件")
    print("   - 获取文件大小信息")
    print("   - 生成前端优化建议")
    
    print("\n🔧 后端问题分析流程:")
    print("   - 支持所有其他日志类型")
    print("   - 错误位置分析")
    print("   - 代码上下文获取")
    print("   - 综合问题分析")
    
    print("\n🌐 启动Web界面...")
    print("访问地址: http://localhost:8501")
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    
    # 启动streamlit
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", main_file,
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 Streamlit服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
