import os
from dotenv import load_dotenv
from dbutils.pooled_db import PooledDB
import pymysql
from threading import Lock

load_dotenv()

class ConnectionPool:
    _instance = None
    _lock = Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    try:
                        cls._instance = super().__new__(cls)
                        cls._instance.init_pool()
                    except Exception as e:
                        cls._instance = None
                        raise e
        return cls._instance

    def init_pool(self):
        self.pool = PooledDB(
            creator=pymysql,  # 使用 pymysql 作为数据库驱动
            host=os.getenv("DB_HOST"),
            port=int(os.getenv("DB_PORT")),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PSWD"),
            database=os.getenv("DB_NAME"),
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=False,
            maxconnections=20,       # 最大连接数
            mincached=5,             # 初始化时创建的连接数（空闲）
            maxcached=10,            # 最大空闲连接数
            blocking=True,           # 连接池满时是否阻塞等待
            ping=1,                  # 每次获取连接前执行 ping 检查连接有效性
            connect_timeout=10,
            read_timeout=30,
            write_timeout=30
        )

    def get_conn(self):
        return self.pool.connection()

    def put_conn(self, conn):
        # connection 在调用 close() 后会自动放回连接池中，无需额外操作
        conn.close()
