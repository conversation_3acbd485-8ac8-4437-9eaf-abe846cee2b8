import json
import os

import requests
from dotenv import load_dotenv

from utils.get_token import get_api_gateway_token
from utils.logger import logger

load_dotenv()


def fetch_trace_by_id(trace_id: str, time: str, is_query_ingress: bool = False):
    """
    调用外部接口查询链路数据
    :param trace_id: 链路ID
    :param time: 请求时间
    :param is_query_ingress: 是否查询 Ingress 日志
    :return: 接口返回的数据（dict），失败返回 None
    """
    payload = {
        "time": time,
        "traceid": trace_id,
        "isQueryIngress": is_query_ingress
    }
    token = get_api_gateway_token()
    params = {
        "access_token": token
    }

    get_trace_data_url = os.getenv("GET_TRACE_DATA")

    try:
        response = requests.post(get_trace_data_url, json=payload, params=params, timeout=100)
        response.raise_for_status()
        result = response.json()
        logger.info(f"请求trace接口地址: {get_trace_data_url,}")
        logger.info(f"请求trace接口body: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        # logger.info(f"获取trace数据结果: {result}")
        if result.get("success"):
            first_data = result.get("data")
            first_item = first_data[0]
            if isinstance(first_item, dict):
                code = first_item.get("code")
                if code == 500:
                    return None

            return first_data[0]["data"]
        else:
            logger.error(f"Failed to fetch trace data for {trace_id}: {result.get('message')}")
            return None
    except requests.RequestException as e:
        logger.exception(f"Error querying trace data for {trace_id}: {e}")
        return None