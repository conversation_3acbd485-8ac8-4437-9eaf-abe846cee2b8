# logger.py
import logging
from logging.handlers import TimedRotatingFileHandler
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()


# 自定义主机名过滤器
class HostnameFilter(logging.Filter):
    def filter(self, record):
        record.hostname = os.environ.get('HOSTNAME', 'unknown_host')
        return True


# 自定义文件处理器，确保使用 utf-8 编码
class UTF8TimedRotatingFileHandler(TimedRotatingFileHandler):
    def __init__(self, filename, when='h', interval=1, backupCount=0, encoding='utf-8', delay=False, utc=False, atTime=None):
        super().__init__(filename, when, interval, backupCount, encoding, delay, utc, atTime)


def get_logger(logger_name=None):
    current_date_time = datetime.now().strftime("%Y-%m-%d")
    logger_dir = os.getenv('LOG_FOLDER', 'logs')
    if not logger_name:
        logger_name = os.getenv('LOG_NAME', 'rcp')
    if not os.path.exists(logger_dir):
        os.makedirs(logger_dir)
    log_file_name = f"{logger_name}.log"
    log_file_path = os.path.join(logger_dir, log_file_name)
    logger_level = os.getenv('LOG_LEVEL', 'DEBUG')

    # Get or create the logger
    logger = logging.getLogger(logger_name)

    # Avoid adding duplicate handlers
    if not logger.hasHandlers():
        # 创建主机名过滤器
        # hostname_filter = HostnameFilter()

        # Create a UTF8TimedRotatingFileHandler
        handler = UTF8TimedRotatingFileHandler(
            log_file_path,  # Log file path
            when='midnight',  # Rotate at midnight
            interval=1,  # Interval for rotation (1 day in this case)
            backupCount=7,  # Number of backup files to keep (7 days in this case)
            encoding='utf-8'  # Ensure utf-8 encoding
        )
        # %(hostname)s
        # 定义带主机名的日志格式
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] [%(threadName)s] %(message)s'
        )

        handler.setFormatter(formatter)
        # handler.addFilter(hostname_filter)  # 添加过滤器

        # Stream Handler (可选)
        stream_handler = logging.StreamHandler()
        stream_handler.setFormatter(formatter)
        stream_handler.setLevel(logger_level)
        # stream_handler.addFilter(hostname_filter)

        # Add handlers to the logger
        logger.addHandler(handler)
        logger.addHandler(stream_handler)
        logger.setLevel(logger_level)

    return logger


logger = get_logger()