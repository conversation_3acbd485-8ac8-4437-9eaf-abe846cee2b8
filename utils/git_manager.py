"""
Git管理器模块

提供同步和异步的Git操作功能，包括代码拉取、分支管理等
"""

import os
import shutil
import subprocess
import asyncio
import requests
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import concurrent.futures
from urllib.parse import urlparse
from dotenv import load_dotenv
from utils.logger import logger
load_dotenv()

@dataclass
class GitRepoInfo:
    """Git仓库信息"""
    id: int
    application_name: str
    repo_url: str
    last_build_branch: Optional[str]
    last_success_build_branch: Optional[str]
    last_build_date: Optional[str]
    last_success_build_date: Optional[str]
    local_path: Optional[str] = None
    clone_status: str = "pending"  # pending, cloning, success, failed


class GitAPIClient:
    """Git API客户端，用于查询Git信息"""
    
    def __init__(self, base_url: str = os.getenv("GIT_INFO_BASE_URL", None)):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Git-Manager/1.0',
            'Accept': 'application/json'
        })
    
    def query_app_git_info(self, app_code: str, env: str, access_token: str) -> List[GitRepoInfo]:
        """
        查询应用的Git信息
        
        Args:
            app_code: 服务编号
            env: 环境信息 (daily/pre/prod)
            access_token: 访问令牌
            
        Returns:
            Git仓库信息列表
        """
        url = f"{self.base_url}/queryAppLastBuild"
        params = {
            "appCode": app_code,
            "env": env,
            "access_token": access_token
        }
        
        try:
            logger.info(f"🔍 查询Git信息: appCode={app_code}, env={env}")
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if not data.get("success", False):
                raise Exception(f"API调用失败: {data.get('message', 'Unknown error')}")
            
            repo_list = data.get("data", {}).get("list", [])
            repos = []
            
            for repo_data in repo_list:
                repo = GitRepoInfo(
                    id=repo_data.get("id"),
                    application_name=repo_data.get("applicationName"),
                    repo_url=repo_data.get("repoUrl"),
                    last_build_branch=repo_data.get("lastBuildBranch"),
                    last_success_build_branch=repo_data.get("lastSuccessBuildBranch"),
                    last_build_date=repo_data.get("lastBuildDate"),
                    last_success_build_date=repo_data.get("lastSuccessBuildDate")
                )
                repos.append(repo)
            
            logger.info(f"✅ 查询到 {len(repos)} 个仓库")
            return repos
            
        except Exception as e:
            logger.info(f"❌ 查询Git信息失败: {str(e)}")
            raise


class GitManager:
    """同步Git管理器"""
    
    def __init__(self, 
                 workspace_dir: str = "./git_workspace",
                 ):
        """
        初始化Git管理器
        
        Args:
            workspace_dir: 工作空间目录
        """
        self.workspace_dir = Path(workspace_dir)
        self.git_username = os.getenv("GIT_USERNAME", "monitor")
        self.git_token = os.getenv("GIT_TOKEN", None)
        self.git_host = os.getenv("GIT_HOST", "devops-gitlab.faw.cn")
        self.api_client = GitAPIClient()
        
        # 创建工作空间目录
        self.workspace_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Git工作空间: {self.workspace_dir.absolute()}")
    
    def _build_clone_url(self, repo_url: str, branch: str = None) -> str:
        """
        构建带认证的克隆URL
        
        Args:
            repo_url: 原始仓库URL
            branch: 分支名称
            
        Returns:
            带认证的克隆URL
        """
        # 解析URL
        parsed = urlparse(repo_url)
        
        # 构建带认证的URL
        if self.git_host in parsed.netloc:
            # 替换为devops-gitlab.faw.cn的认证方式
            auth_url = f"https://{self.git_username}:{self.git_token}@{self.git_host}{parsed.path}"
        else:
            # 对于其他Git服务器，尝试添加认证
            auth_url = f"https://{self.git_username}:{self.git_token}@{parsed.netloc}{parsed.path}"
        
        return auth_url
    
    def _execute_git_command(self, command: List[str], cwd: str = None, timeout: int = 300) -> Tuple[bool, str, str]:
        """
        执行Git命令
        
        Args:
            command: Git命令列表
            cwd: 工作目录
            timeout: 超时时间（秒）
            
        Returns:
            (成功标志, 标准输出, 错误输出)
        """
        try:
            logger.info(f"🔧 执行Git命令: {' '.join(command)}")
            
            result = subprocess.run(
                command,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout,
                check=False
            )
            
            success = result.returncode == 0
            stdout = result.stdout.strip()
            stderr = result.stderr.strip()
            
            if success:
                logger.info(f"✅ 命令执行成功")
                if stdout:
                    logger.info(f"📤 输出: {stdout[:200]}...")
            else:
                logger.info(f"❌ 命令执行失败 (返回码: {result.returncode})")
                if stderr:
                    logger.info(f"🚨 错误: {stderr[:200]}...")
            
            return success, stdout, stderr
            
        except subprocess.TimeoutExpired:
            logger.info(f"⏰ 命令执行超时 ({timeout}秒)")
            return False, "", f"命令执行超时 ({timeout}秒)"
        except Exception as e:
            logger.info(f"💥 命令执行异常: {str(e)}")
            return False, "", str(e)
    
    def clone_repository(self, repo_info: GitRepoInfo, branch: str = None, force: bool = False, app_code: str = None) -> bool:
        """
        克隆代码仓库
        
        Args:
            repo_info: 仓库信息
            branch: 指定分支，默认使用lastSuccessBuildBranch或lastBuildBranch
            force: 是否强制重新克隆
            app_code: 服务编号，用于创建服务编号命名的文件夹
            
        Returns:
            克隆是否成功
        """
        # 确定分支
        if not branch:
            branch = (repo_info.last_success_build_branch or 
                     repo_info.last_build_branch or 
                     "master")
        
        # 确定本地路径 - 使用服务编号作为父目录
        repo_name = Path(repo_info.repo_url).name
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        if app_code:
            # 创建以服务编号命名的文件夹
            app_dir = self.workspace_dir / app_code
            app_dir.mkdir(parents=True, exist_ok=True)
            local_path = app_dir / f"{repo_info.application_name}_{repo_name}"
        else:
            # 兼容原有逻辑
            local_path = self.workspace_dir / f"{repo_info.application_name}_{repo_name}"
        
        repo_info.local_path = str(local_path)
        
        logger.info(f"\n📦 开始克隆仓库:")
        logger.info(f"   应用名: {repo_info.application_name}")
        logger.info(f"   仓库URL: {repo_info.repo_url}")
        logger.info(f"   分支: {branch}")
        logger.info(f"   本地路径: {local_path}")
        
        # 检查本地是否已存在
        if local_path.exists():
            if force:
                logger.info(f"🗑️ 强制模式，删除现有目录: {local_path}")
                shutil.rmtree(local_path)
            else:
                logger.info(f"📁 目录已存在，跳过克隆: {local_path}")
                repo_info.clone_status = "success"
                return True
        
        # 构建克隆命令
        clone_url = self._build_clone_url(repo_info.repo_url, branch)
        
        # 执行克隆
        repo_info.clone_status = "cloning"
        
        if branch:
            command = ["git", "clone", "-b", branch, clone_url, str(local_path)]
        else:
            command = ["git", "clone", clone_url, str(local_path)]
        
        success, stdout, stderr = self._execute_git_command(command, timeout=600)
        
        if success:
            repo_info.clone_status = "success"
            logger.info(f"✅ 仓库克隆成功: {repo_info.application_name}")
            return True
        else:
            repo_info.clone_status = "failed"
            logger.info(f"❌ 仓库克隆失败: {repo_info.application_name}")
            logger.info(f"   错误信息: {stderr}")
            return False
    
    def clone_all_repositories(self, repos: List[GitRepoInfo], force: bool = False, app_code: str = None) -> Dict[str, Any]:
        """
        批量克隆所有仓库
        
        Args:
            repos: 仓库信息列表
            force: 是否强制重新克隆
            app_code: 服务编号，用于创建服务编号命名的文件夹
            
        Returns:
            克隆结果统计
        """
        logger.info(f"\n🚀 开始批量克隆 {len(repos)} 个仓库...")
        if app_code:
            logger.info(f"📁 目标目录: {self.workspace_dir / app_code}")
        
        success_count = 0
        failed_count = 0
        skipped_count = 0
        failed_repos = []
        
        for i, repo in enumerate(repos, 1):
            logger.info(f"\n[{i}/{len(repos)}] 处理仓库: {repo.application_name}")
            
            # 跳过没有有效分支的仓库
            if not repo.last_build_branch and not repo.last_success_build_branch:
                logger.info(f"⚠️ 跳过无有效分支的仓库: {repo.application_name}")
                skipped_count += 1
                continue
            
            try:
                if self.clone_repository(repo, force=force, app_code=app_code):
                    success_count += 1
                else:
                    failed_count += 1
                    failed_repos.append(repo.application_name)
            except Exception as e:
                logger.info(f"💥 克隆仓库异常: {repo.application_name} - {str(e)}")
                failed_count += 1
                failed_repos.append(repo.application_name)
        
        # 统计结果
        result = {
            "total": len(repos),
            "success": success_count,
            "failed": failed_count,
            "skipped": skipped_count,
            "failed_repos": failed_repos,
            "workspace": str(self.workspace_dir.absolute()),
            "app_code": app_code,
            "app_directory": str(self.workspace_dir / app_code) if app_code else None
        }
        
        logger.info(f"\n📊 批量克隆完成:")
        logger.info(f"   总数: {result['total']}")
        logger.info(f"   成功: {result['success']}")
        logger.info(f"   失败: {result['failed']}")
        logger.info(f"   跳过: {result['skipped']}")
        if failed_repos:
            logger.info(f"   失败仓库: {', '.join(failed_repos)}")
        
        return result
    
    def pull_repository(self, repo_path: str, branch: str = None) -> bool:
        """
        拉取仓库最新代码
        
        Args:
            repo_path: 仓库本地路径
            branch: 分支名称
            
        Returns:
            拉取是否成功
        """
        if not Path(repo_path).exists():
            logger.info(f"❌ 仓库路径不存在: {repo_path}")
            return False
        
        logger.info(f"🔄 拉取最新代码: {repo_path}")
        
        # 切换分支（如果指定）
        if branch:
            success, _, _ = self._execute_git_command(
                ["git", "checkout", branch], 
                cwd=repo_path
            )
            if not success:
                logger.info(f"⚠️ 切换分支失败，继续使用当前分支")
        
        # 拉取最新代码
        success, stdout, stderr = self._execute_git_command(
            ["git", "pull"], 
            cwd=repo_path
        )
        
        if success:
            logger.info(f"✅ 代码拉取成功")
            return True
        else:
            logger.info(f"❌ 代码拉取失败: {stderr}")
            # 如果pull失败，使用fetch + reset策略强制更新到远程分支
            logger.info(f"🔄 尝试强制重置到远程分支...")
            
            # 先获取远程更新
            fetch_success, _, fetch_stderr = self._execute_git_command(
                ["git", "fetch", "origin"], 
                cwd=repo_path
            )
            
            if fetch_success:
                # 强制重置到远程分支
                remote_branch = f"origin/{branch}" if branch else "origin/master"
                reset_success, _, reset_stderr = self._execute_git_command(
                    ["git", "reset", "--hard", remote_branch], 
                    cwd=repo_path
                )
                
                if reset_success:
                    logger.info(f"✅ 强制重置到远程分支成功")
                    return True
                else:
                    logger.info(f"❌ 强制重置失败: {reset_stderr}")
            else:
                logger.info(f"❌ 获取远程更新失败: {fetch_stderr}")
            
            return False
    
    def get_repository_info(self, repo_path: str) -> Dict[str, Any]:
        """
        获取仓库信息
        
        Args:
            repo_path: 仓库本地路径
            
        Returns:
            仓库信息
        """
        if not Path(repo_path).exists():
            return {"error": "仓库路径不存在"}
        
        info = {"path": repo_path}
        
        # 获取当前分支
        success, branch, _ = self._execute_git_command(
            ["git", "branch", "--show-current"], 
            cwd=repo_path
        )
        if success:
            info["current_branch"] = branch
        
        # 获取远程URL
        success, remote_url, _ = self._execute_git_command(
            ["git", "remote", "get-url", "origin"], 
            cwd=repo_path
        )
        if success:
            info["remote_url"] = remote_url
        
        # 获取最新提交
        success, commit_info, _ = self._execute_git_command(
            ["git", "log", "-1", "--pretty=format:%H|%an|%ad|%s"], 
            cwd=repo_path
        )
        if success and commit_info:
            commit_parts = commit_info.split("|")
            if len(commit_parts) >= 4:
                info["last_commit"] = {
                    "hash": commit_parts[0],
                    "author": commit_parts[1],
                    "date": commit_parts[2],
                    "message": commit_parts[3]
                }
        
        return info
    
    def cleanup_workspace(self, confirm: bool = False) -> bool:
        """
        清理工作空间
        
        Args:
            confirm: 是否确认清理
            
        Returns:
            清理是否成功
        """
        if not confirm:
            logger.info("⚠️ 需要确认才能清理工作空间")
            return False
        
        try:
            if self.workspace_dir.exists():
                shutil.rmtree(self.workspace_dir)
                logger.info(f"🗑️ 工作空间已清理: {self.workspace_dir}")
                return True
            else:
                logger.info(f"📁 工作空间不存在: {self.workspace_dir}")
                return True
        except Exception as e:
            logger.info(f"❌ 清理工作空间失败: {str(e)}")
            return False


class AsyncGitManager:
    """异步Git管理器"""
    
    def __init__(self, 
                 workspace_dir: str = "./git_workspace",
                 max_concurrent_clones: int = 3):
        """
        初始化异步Git管理器
        
        Args:
            workspace_dir: 工作空间目录
            max_concurrent_clones: 最大并发克隆数
        """
        self.git_username = os.getenv("GIT_USERNAME", "monitor")
        self.git_token = os.getenv("GIT_TOKEN", None)
        self.git_host = os.getenv("GIT_HOST", "devops-gitlab.faw.cn")
        self.sync_manager = GitManager(workspace_dir)
        self.max_concurrent_clones = max_concurrent_clones
        self.semaphore = asyncio.Semaphore(max_concurrent_clones)
        
        logger.info(f"🚀 异步Git管理器初始化完成 (最大并发: {max_concurrent_clones})")
    
    async def _execute_git_command_async(self, command: List[str], cwd: str = None, timeout: int = 300) -> Tuple[bool, str, str]:
        """
        异步执行Git命令
        
        Args:
            command: Git命令列表
            cwd: 工作目录
            timeout: 超时时间（秒）
            
        Returns:
            (成功标志, 标准输出, 错误输出)
        """
        try:
            logger.info(f"🔧 异步执行Git命令: {' '.join(command)}")
            
            process = await asyncio.create_subprocess_exec(
                *command,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=timeout
            )
            
            success = process.returncode == 0
            stdout_str = stdout.decode('utf-8').strip()
            stderr_str = stderr.decode('utf-8').strip()
            
            if success:
                logger.info(f"✅ 异步命令执行成功")
            else:
                logger.info(f"❌ 异步命令执行失败 (返回码: {process.returncode})")
            
            return success, stdout_str, stderr_str
            
        except asyncio.TimeoutError:
            logger.info(f"⏰ 异步命令执行超时 ({timeout}秒)")
            return False, "", f"命令执行超时 ({timeout}秒)"
        except Exception as e:
            logger.info(f"💥 异步命令执行异常: {str(e)}")
            return False, "", str(e)
    
    async def clone_repository_async(self, repo_info: GitRepoInfo, branch: str = None, force: bool = False, app_code: str = None) -> bool:
        """
        异步克隆代码仓库
        
        Args:
            repo_info: 仓库信息
            branch: 指定分支
            force: 是否强制重新克隆
            app_code: 服务编号，用于创建服务编号命名的文件夹
            
        Returns:
            克隆是否成功
        """
        async with self.semaphore:
            logger.info(f"🔄 开始异步克隆: {repo_info.application_name}")
            
            # 使用线程池执行同步克隆操作
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                result = await loop.run_in_executor(
                    executor,
                    self.sync_manager.clone_repository,
                    repo_info,
                    branch,
                    force,
                    app_code
                )
            
            logger.info(f"{'✅' if result else '❌'} 异步克隆完成: {repo_info.application_name}")
            return result
    
    async def clone_all_repositories_async(self, repos: List[GitRepoInfo], force: bool = False, app_code: str = None) -> Dict[str, Any]:
        """
        异步批量克隆所有仓库
        
        Args:
            repos: 仓库信息列表
            force: 是否强制重新克隆
            app_code: 服务编号，用于创建服务编号命名的文件夹
            
        Returns:
            克隆结果统计
        """
        logger.info(f"\n🚀 开始异步批量克隆 {len(repos)} 个仓库 (并发数: {self.max_concurrent_clones})...")
        if app_code:
            logger.info(f"📁 目标目录: {self.sync_manager.workspace_dir / app_code}")
        
        # 过滤有效仓库
        valid_repos = [
            repo for repo in repos 
            if repo.last_build_branch or repo.last_success_build_branch
        ]
        
        if len(valid_repos) < len(repos):
            logger.info(f"⚠️ 过滤掉 {len(repos) - len(valid_repos)} 个无有效分支的仓库")
        
        # 创建异步任务
        tasks = []
        for repo in valid_repos:
            task = self.clone_repository_async(repo, force=force, app_code=app_code)
            tasks.append(task)
        
        # 执行所有任务
        start_time = datetime.now()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = datetime.now()
        
        # 统计结果
        success_count = 0
        failed_count = 0
        failed_repos = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.info(f"💥 仓库 {valid_repos[i].application_name} 异步克隆异常: {str(result)}")
                failed_count += 1
                failed_repos.append(valid_repos[i].application_name)
            elif result:
                success_count += 1
            else:
                failed_count += 1
                failed_repos.append(valid_repos[i].application_name)
        
        duration = (end_time - start_time).total_seconds()
        
        result_summary = {
            "total": len(repos),
            "valid": len(valid_repos),
            "success": success_count,
            "failed": failed_count,
            "skipped": len(repos) - len(valid_repos),
            "failed_repos": failed_repos,
            "workspace": str(self.sync_manager.workspace_dir.absolute()),
            "app_code": app_code,
            "app_directory": str(self.sync_manager.workspace_dir / app_code) if app_code else None,
            "duration_seconds": duration,
            "concurrent_limit": self.max_concurrent_clones
        }
        
        logger.info(f"\n📊 异步批量克隆完成:")
        logger.info(f"   总数: {result_summary['total']}")
        logger.info(f"   有效: {result_summary['valid']}")
        logger.info(f"   成功: {result_summary['success']}")
        logger.info(f"   失败: {result_summary['failed']}")
        logger.info(f"   跳过: {result_summary['skipped']}")
        logger.info(f"   耗时: {duration:.2f}秒")
        if failed_repos:
            logger.info(f"   失败仓库: {', '.join(failed_repos)}")
        
        return result_summary
    
    async def query_and_clone_by_app_code(self, 
                                        app_code: str, 
                                        env: str, 
                                        access_token: str,
                                        force: bool = False) -> Dict[str, Any]:
        """
        根据应用编号查询并克隆所有相关仓库
        
        Args:
            app_code: 服务编号
            env: 环境信息
            access_token: 访问令牌
            force: 是否强制重新克隆
            
        Returns:
            完整的执行结果
        """
        try:
            logger.info(f"🔍 开始查询并克隆应用代码: {app_code} ({env})")
            
            # 查询Git信息
            repos = self.sync_manager.api_client.query_app_git_info(app_code, env, access_token)
            
            if not repos:
                return {
                    "success": False,
                    "error": "未查询到任何仓库信息",
                    "app_code": app_code,
                    "env": env
                }
            
            # 异步克隆所有仓库 - 使用服务编号作为文件夹名
            clone_result = await self.clone_all_repositories_async(repos, force=force, app_code=app_code)
            
            return {
                "success": True,
                "app_code": app_code,
                "env": env,
                "repos_info": [
                    {
                        "id": repo.id,
                        "name": repo.application_name,
                        "url": repo.repo_url,
                        "branch": repo.last_success_build_branch or repo.last_build_branch,
                        "local_path": repo.local_path,
                        "status": repo.clone_status
                    }
                    for repo in repos
                ],
                "clone_result": clone_result
            }
            
        except Exception as e:
            error_msg = f"查询并克隆失败: {str(e)}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "env": env
            }
    
    def clone_repositories_batch(self, clone_tasks: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        批量克隆仓库（同步接口，内部使用异步实现）
        
        Args:
            clone_tasks: 克隆任务列表，每个任务包含：
                - repo_name: 仓库名称
                - repo_url: 仓库URL
                - target_dir: 目标目录
                - branch: 分支名称（可选，默认为master）
                
        Returns:
            克隆结果列表
        """
        try:
            # 检查是否已经在事件循环中
            try:
                loop = asyncio.get_running_loop()
                logger.info("🔄 检测到正在运行的事件循环，使用同步实现")
                # 如果已经在事件循环中，使用同步实现
                return self._clone_repositories_batch_sync(clone_tasks)
            except RuntimeError:
                # 没有运行的事件循环，创建新的
                logger.info("🆕 创建新的事件循环进行异步克隆")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    result = loop.run_until_complete(self._clone_repositories_batch_async(clone_tasks))
                    return result
                finally:
                    loop.close()
                    
        except Exception as e:
            logger.info(f"❌ 批量克隆异常: {str(e)}")
            # 返回失败结果
            return [
                {
                    "success": False,
                    "repo_name": task.get("repo_name", "unknown"),
                    "error": str(e)
                }
                for task in clone_tasks
            ]
    
    async def _clone_repositories_batch_async(self, clone_tasks: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        异步批量克隆仓库
        
        Args:
            clone_tasks: 克隆任务列表，每个任务包含：
                - repo_name: 仓库名称
                - repo_url: 仓库URL
                - target_dir: 目标目录
                - branch: 分支名称（可选）
            
        Returns:
            克隆结果列表
        """
        logger.info(f"🚀 开始异步批量克隆 {len(clone_tasks)} 个仓库...")
        
        # 创建异步任务
        tasks = []
        for task in clone_tasks:
            repo_name = task.get("repo_name", "")
            repo_url = task.get("repo_url", "")
            target_dir = task.get("target_dir", "")
            branch = task.get("branch", "master")  # 默认使用master分支
            
            if not all([repo_name, repo_url, target_dir]):
                logger.info(f"⚠️ 跳过无效任务: {task}")
                continue
            
            # 创建异步克隆任务
            clone_task = self._clone_single_repository_async(
                repo_name=repo_name,
                repo_url=repo_url,
                target_dir=target_dir,
                branch=branch
            )
            tasks.append(clone_task)
        
        # 并发执行所有克隆任务
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            final_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    final_results.append({
                        "success": False,
                        "repo_name": clone_tasks[i].get("repo_name", "unknown"),
                        "error": str(result)
                    })
                else:
                    final_results.append(result)
            
            return final_results
        else:
            logger.info("⚠️ 没有有效的克隆任务")
            return []
    
    def _clone_repositories_batch_sync(self, clone_tasks: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        同步批量克隆仓库（当已经在事件循环中时使用）
        
        Args:
            clone_tasks: 克隆任务列表
            
        Returns:
            克隆结果列表
        """
        logger.info(f"🔧 使用同步方式批量克隆 {len(clone_tasks)} 个仓库...")
        
        results = []
        for task in clone_tasks:
            repo_name = task.get("repo_name", "")
            repo_url = task.get("repo_url", "")
            target_dir = task.get("target_dir", "")
            branch = task.get("branch", "master")
            
            if not all([repo_name, repo_url, target_dir]):
                logger.info(f"⚠️ 跳过无效任务: {task}")
                results.append({
                    "success": False,
                    "repo_name": repo_name,
                    "error": "缺少必要参数"
                })
                continue
            
            # 使用同步方式克隆单个仓库
            result = self._clone_single_repository_sync(repo_name, repo_url, target_dir, branch)
            results.append(result)
        
        return results
    
    def _clone_single_repository_sync(self, repo_name: str, repo_url: str, target_dir: str, branch: str = "master") -> Dict[str, Any]:
        """
        同步克隆单个仓库
        
        Args:
            repo_name: 仓库名称
            repo_url: 仓库URL
            target_dir: 目标目录
            branch: 分支名称，默认为master
            
        Returns:
            克隆结果
        """
        try:
            logger.info(f"📦 开始同步克隆仓库: {repo_name}")
            logger.info(f"   URL: {repo_url}")
            logger.info(f"   分支: {branch}")
            logger.info(f"   目标: {target_dir}")
            
            # 检查目标目录是否已存在
            target_path = Path(target_dir)
            if target_path.exists():
                logger.info(f"📁 目录已存在，检查是否为Git仓库: {target_dir}")
                
                # 检查是否为Git仓库
                git_dir = target_path / ".git"
                if git_dir.exists():
                    logger.info(f"🔍 发现Git仓库，执行更新操作...")
                    
                    # 获取当前分支
                    success, current_branch, _ = self.sync_manager._execute_git_command(
                        ["git", "branch", "--show-current"], 
                        cwd=str(target_path)
                    )
                    
                    if success and current_branch:
                        logger.info(f"   当前分支: {current_branch}")
                        
                        # 如果目标分支与当前分支不同，尝试切换分支
                        if branch and branch != current_branch:
                            logger.info(f"🔄 切换到目标分支: {branch}")
                            success, _, stderr = self.sync_manager._execute_git_command(
                                ["git", "checkout", branch], 
                                cwd=str(target_path)
                            )
                            
                            if not success:
                                logger.info(f"⚠️ 分支切换失败，尝试从远程创建分支: {stderr}")
                                # 尝试从远程创建并切换分支
                                success, _, stderr = self.sync_manager._execute_git_command(
                                    ["git", "checkout", "-b", branch, f"origin/{branch}"], 
                                    cwd=str(target_path)
                                )
                                
                                if not success:
                                    logger.info(f"⚠️ 从远程创建分支也失败，继续使用当前分支: {stderr}")
                        
                        # 执行git pull更新代码
                        logger.info(f"📥 拉取最新代码...")
                        success, stdout, stderr = self.sync_manager._execute_git_command(
                            ["git", "pull"], 
                            cwd=str(target_path)
                        )
                        
                        if success:
                            logger.info(f"✅ 代码更新成功: {repo_name}")
                            return {
                                "success": True,
                                "repo_name": repo_name,
                                "target_dir": target_dir,
                                "branch": branch,
                                "message": f"代码更新成功 (分支: {branch})",
                                "operation": "pull"
                            }
                        else:
                            logger.info(f"❌ 代码更新失败: {stderr}")
                            # 如果pull失败，使用fetch + reset策略强制更新到远程分支
                            logger.info(f"🔄 尝试强制重置到远程分支...")
                            
                            # 先获取远程更新
                            fetch_success, _, fetch_stderr = self.sync_manager._execute_git_command(
                                ["git", "fetch", "origin"], 
                                cwd=str(target_path)
                            )
                            
                            if fetch_success:
                                # 强制重置到远程分支
                                remote_branch = f"origin/{branch}" if branch else "origin/master"
                                reset_success, _, reset_stderr = self.sync_manager._execute_git_command(
                                    ["git", "reset", "--hard", remote_branch], 
                                    cwd=str(target_path)
                                )
                                
                                if reset_success:
                                    logger.info(f"✅ 强制重置到远程分支成功: {repo_name}")
                                    return {
                                        "success": True,
                                        "repo_name": repo_name,
                                        "target_dir": target_dir,
                                        "branch": branch,
                                        "message": f"强制重置到远程分支成功 (分支: {branch})",
                                        "operation": "force_reset"
                                    }
                                else:
                                    logger.info(f"❌ 强制重置失败: {reset_stderr}")
                            else:
                                logger.info(f"❌ 获取远程更新失败: {fetch_stderr}")
                            
                            return {
                                "success": False,
                                "repo_name": repo_name,
                                "branch": branch,
                                "error": f"代码更新失败: {stderr}，强制重置也失败",
                                "operation": "all_failed"
                            }
                    else:
                        logger.info(f"⚠️ 无法获取当前分支信息，直接尝试拉取")
                        success, stdout, stderr = self.sync_manager._execute_git_command(
                            ["git", "pull"], 
                            cwd=str(target_path)
                        )
                        
                        if success:
                            return {
                                "success": True,
                                "repo_name": repo_name,
                                "message": "代码更新成功（未知分支）",
                                "operation": "pull"
                            }
                        else:
                            logger.info(f"❌ 代码更新失败: {stderr}")
                            # 如果pull失败，使用fetch + reset策略强制更新
                            logger.info(f"🔄 尝试强制重置到远程分支...")
                            
                            # 先获取远程更新
                            fetch_success, _, fetch_stderr = self.sync_manager._execute_git_command(
                                ["git", "fetch", "origin"], 
                                cwd=str(target_path)
                            )
                            
                            if fetch_success:
                                # 强制重置到远程分支
                                remote_branch = f"origin/{branch}" if branch else "origin/master"
                                reset_success, _, reset_stderr = self.sync_manager._execute_git_command(
                                    ["git", "reset", "--hard", remote_branch], 
                                    cwd=str(target_path)
                                )
                                
                                if reset_success:
                                    logger.info(f"✅ 强制重置到远程分支成功: {repo_name}")
                                    return {
                                        "success": True,
                                        "repo_name": repo_name,
                                        "target_dir": target_dir,
                                        "branch": branch,
                                        "message": f"强制重置到远程分支成功 (分支: {branch})",
                                        "operation": "force_reset"
                                    }
                                else:
                                    logger.info(f"❌ 强制重置失败: {reset_stderr}")
                            else:
                                logger.info(f"❌ 获取远程更新失败: {fetch_stderr}")
                            
                            return {
                                "success": False,
                                "repo_name": repo_name,
                                "error": f"代码更新失败: {stderr}，强制重置也失败",
                                "operation": "all_failed"
                            }
                else:
                    logger.info(f"📁 目录存在但不是Git仓库，删除后重新克隆")
                    # 删除非Git目录，重新克隆
                    import shutil
                    shutil.rmtree(target_path)
                    logger.info(f"🗑️ 已删除非Git目录: {target_dir}")
            
            # 如果目录不存在或已被删除，执行克隆操作
            
            # 确保父目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 构建带认证的克隆URL
            clone_url = self.sync_manager._build_clone_url(repo_url)
            
            # 执行带分支的克隆命令
            if branch and branch != "master":
                # 指定分支克隆
                command = ["git", "clone", "-b", branch, clone_url, str(target_path)]
                logger.info(f"🌿 使用指定分支克隆: {branch}")
            else:
                # 默认分支克隆
                command = ["git", "clone", clone_url, str(target_path)]
                logger.info(f"🌿 使用默认分支克隆")
            
            success, stdout, stderr = self.sync_manager._execute_git_command(
                command, 
                timeout=600  # 10分钟超时
            )
            
            if success:
                logger.info(f"✅ 仓库同步克隆成功: {repo_name} (分支: {branch})")
                return {
                    "success": True,
                    "repo_name": repo_name,
                    "target_dir": target_dir,
                    "branch": branch,
                    "message": f"克隆成功 (分支: {branch})"
                }
            else:
                logger.info(f"❌ 仓库同步克隆失败: {repo_name} (分支: {branch})")
                logger.info(f"   错误: {stderr}")
                return {
                    "success": False,
                    "repo_name": repo_name,
                    "branch": branch,
                    "error": stderr or "克隆命令执行失败"
                }
                
        except Exception as e:
            logger.info(f"💥 同步克隆仓库异常: {repo_name} (分支: {branch}) - {str(e)}")
            return {
                "success": False,
                "repo_name": repo_name,
                "branch": branch,
                "error": str(e)
            }
    
    async def _clone_single_repository_async(self, repo_name: str, repo_url: str, target_dir: str, branch: str = "master") -> Dict[str, Any]:
        """
        异步克隆单个仓库
        
        Args:
            repo_name: 仓库名称
            repo_url: 仓库URL
            target_dir: 目标目录
            branch: 分支名称，默认为master
            
        Returns:
            克隆结果
        """
        async with self.semaphore:  # 限制并发数
            try:
                logger.info(f"📦 开始克隆仓库: {repo_name}")
                logger.info(f"   URL: {repo_url}")
                logger.info(f"   分支: {branch}")
                logger.info(f"   目标: {target_dir}")
                
                # 检查目标目录是否已存在
                target_path = Path(target_dir)
                if target_path.exists():
                    logger.info(f"📁 目录已存在，检查是否为Git仓库: {target_dir}")
                    
                    # 检查是否为Git仓库
                    git_dir = target_path / ".git"
                    if git_dir.exists():
                        logger.info(f"🔍 发现Git仓库，执行异步更新操作...")
                        
                        # 获取当前分支
                        success, current_branch, _ = await self._execute_git_command_async(
                            ["git", "branch", "--show-current"], 
                            cwd=str(target_path)
                        )
                        
                        if success and current_branch:
                            logger.info(f"   当前分支: {current_branch}")
                            
                            # 如果目标分支与当前分支不同，尝试切换分支
                            if branch and branch != current_branch:
                                logger.info(f"🔄 切换到目标分支: {branch}")
                                success, _, stderr = await self._execute_git_command_async(
                                    ["git", "checkout", branch], 
                                    cwd=str(target_path)
                                )
                                
                                if not success:
                                    logger.info(f"⚠️ 分支切换失败，尝试从远程创建分支: {stderr}")
                                    # 尝试从远程创建并切换分支
                                    success, _, stderr = await self._execute_git_command_async(
                                        ["git", "checkout", "-b", branch, f"origin/{branch}"], 
                                        cwd=str(target_path)
                                    )
                                    
                                    if not success:
                                        logger.info(f"⚠️ 从远程创建分支也失败，继续使用当前分支: {stderr}")
                        
                            # 执行git pull更新代码
                            logger.info(f"📥 异步拉取最新代码...")
                            success, stdout, stderr = await self._execute_git_command_async(
                                ["git", "pull"], 
                                cwd=str(target_path)
                            )
                            
                            if success:
                                logger.info(f"✅ 异步代码更新成功: {repo_name}")
                                return {
                                    "success": True,
                                    "repo_name": repo_name,
                                    "target_dir": target_dir,
                                    "branch": branch,
                                    "message": f"代码更新成功 (分支: {branch})",
                                    "operation": "pull"
                                }
                            else:
                                logger.info(f"❌ 异步代码更新失败: {stderr}")
                                # 如果pull失败，使用fetch + reset策略强制更新到远程分支
                                logger.info(f"🔄 尝试强制重置到远程分支...")
                                
                                # 先获取远程更新
                                fetch_success, _, fetch_stderr = await self._execute_git_command_async(
                                    ["git", "fetch", "origin"], 
                                    cwd=str(target_path)
                                )
                                
                                if fetch_success:
                                    # 强制重置到远程分支
                                    remote_branch = f"origin/{branch}" if branch else "origin/master"
                                    reset_success, _, reset_stderr = await self._execute_git_command_async(
                                        ["git", "reset", "--hard", remote_branch], 
                                        cwd=str(target_path)
                                    )
                                    
                                    if reset_success:
                                        logger.info(f"✅ 强制重置到远程分支成功: {repo_name}")
                                        return {
                                            "success": True,
                                            "repo_name": repo_name,
                                            "target_dir": target_dir,
                                            "branch": branch,
                                            "message": f"强制重置到远程分支成功 (分支: {branch})",
                                            "operation": "force_reset"
                                        }
                                    else:
                                        logger.info(f"❌ 强制重置失败: {reset_stderr}")
                                else:
                                    logger.info(f"❌ 获取远程更新失败: {fetch_stderr}")
                                
                                return {
                                    "success": False,
                                    "repo_name": repo_name,
                                    "branch": branch,
                                    "error": f"代码更新失败: {stderr}，强制重置也失败",
                                    "operation": "all_failed"
                                }
                        else:
                            logger.info(f"⚠️ 无法获取当前分支信息，直接尝试异步拉取")
                            success, stdout, stderr = await self._execute_git_command_async(
                                ["git", "pull"], 
                                cwd=str(target_path)
                            )
                            
                            if success:
                                return {
                                    "success": True,
                                    "repo_name": repo_name,
                                    "message": "代码更新成功（未知分支）",
                                    "operation": "pull"
                                }
                            else:
                                logger.info(f"❌ 代码更新失败: {stderr}")
                                # 如果pull失败，使用fetch + reset策略强制更新
                                logger.info(f"🔄 尝试强制重置到远程分支...")
                                
                                # 先获取远程更新
                                fetch_success, _, fetch_stderr = await self._execute_git_command_async(
                                    ["git", "fetch", "origin"], 
                                    cwd=str(target_path)
                                )
                                
                                if fetch_success:
                                    # 强制重置到远程分支
                                    remote_branch = f"origin/{branch}" if branch else "origin/master"
                                    reset_success, _, reset_stderr = await self._execute_git_command_async(
                                        ["git", "reset", "--hard", remote_branch], 
                                        cwd=str(target_path)
                                    )
                                    
                                    if reset_success:
                                        logger.info(f"✅ 强制重置到远程分支成功: {repo_name}")
                                        return {
                                            "success": True,
                                            "repo_name": repo_name,
                                            "target_dir": target_dir,
                                            "branch": branch,
                                            "message": f"强制重置到远程分支成功 (分支: {branch})",
                                            "operation": "force_reset"
                                        }
                                    else:
                                        logger.info(f"❌ 强制重置失败: {reset_stderr}")
                                else:
                                    logger.info(f"❌ 获取远程更新失败: {fetch_stderr}")
                                
                                return {
                                    "success": False,
                                    "repo_name": repo_name,
                                    "error": f"代码更新失败: {stderr}，强制重置也失败",
                                    "operation": "all_failed"
                                }
                    else:
                        logger.info(f"📁 目录存在但不是Git仓库，删除后重新克隆")
                        # 删除非Git目录，重新克隆
                        import shutil
                        shutil.rmtree(target_path)
                        logger.info(f"🗑️ 已删除非Git目录: {target_dir}")
                
                # 如果目录不存在或已被删除，执行克隆操作
                
                # 确保父目录存在
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 构建带认证的克隆URL
                clone_url = self.sync_manager._build_clone_url(repo_url)
                
                # 执行带分支的克隆命令
                if branch and branch != "master":
                    # 指定分支克隆
                    command = ["git", "clone", "-b", branch, clone_url, str(target_path)]
                    logger.info(f"🌿 使用指定分支克隆: {branch}")
                else:
                    # 默认分支克隆
                    command = ["git", "clone", clone_url, str(target_path)]
                    logger.info(f"🌿 使用默认分支克隆")
                
                success, stdout, stderr = await self._execute_git_command_async(
                    command, 
                    timeout=600  # 10分钟超时
                )
                
                if success:
                    logger.info(f"✅ 仓库克隆成功: {repo_name} (分支: {branch})")
                    return {
                        "success": True,
                        "repo_name": repo_name,
                        "target_dir": target_dir,
                        "branch": branch,
                        "message": f"克隆成功 (分支: {branch})"
                    }
                else:
                    logger.info(f"❌ 仓库克隆失败: {repo_name} (分支: {branch})")
                    logger.info(f"   错误: {stderr}")
                    return {
                        "success": False,
                        "repo_name": repo_name,
                        "branch": branch,
                        "error": stderr or "克隆命令执行失败"
                    }
                    
            except Exception as e:
                logger.info(f"💥 克隆仓库异常: {repo_name} (分支: {branch}) - {str(e)}")
                return {
                    "success": False,
                    "repo_name": repo_name,
                    "branch": branch,
                    "error": str(e)
                }