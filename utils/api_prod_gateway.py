import requests
import json
import base64
from hashlib import sha256
import hmac
import urllib
import os
from collections import OrderedDict
from utils.logger import logger
from dotenv import load_dotenv
import os
import time

load_dotenv()

# 生产环境的网关配置
ucg_config = {
    "app_key": os.getenv(f"GATEWAY_APP_KEY_PROD", None),
    "app_secret": os.getenv(f"GATEWAY_APP_SECRET_PROD", None),
    "host": os.getenv(f"GATEWAY_HOST_PROD", "https://prod-api.faw.cn")
}

class ApiProdGatewayService:

    def get_token(self):
        params = self.build_params_map(ucg_config['app_key'], str(int(time.time() * 1000)))
        signature = self.sign(params, ucg_config['app_secret'])
        # print(ucg_config['app_key'])
        # print(ucg_config['app_secret'])

        # logger.debug(f"getToken.signature: {signature}")

        response = requests.get(self.get_get_url(params['appKey'], params['timestamp'], signature)).text
        # logger.debug(f"getToken.response: {response}")

        if not response:
            raise Exception("RPC_FAILED")

        result = json.loads(response)
        if not result or not result.get("data"):
            raise Exception("RPC_FAILED")

        expire = result["data"]["expire"]
        token = result["data"]["access_token"]

        return token, expire

    def build_params_map(self, app_key, timestamp):
        params = OrderedDict()
        params['appKey'] = app_key
        params['timestamp'] = timestamp
        return params

    def sign(self, params, secret_key):
        # logger.info(f"sign.params: {json.dumps(params)}")

        sorted_params = OrderedDict(sorted(params.items()))
        string_to_sign = ''.join(f"{k}{v}" for k, v in sorted_params.items())
        # logger.debug(f"string_to_sign: {string_to_sign}")

        try:
            mac = hmac.new(secret_key.encode(), string_to_sign.encode(), sha256)
            signature = base64.b64encode(mac.digest()).decode()
            # logger.info(f"sign.original: {signature}")
            # 手动替换指定字符
            encoded_signature = urllib.parse.quote(signature)
            encoded_signature = (encoded_signature.replace('/', '%2F'))

            return encoded_signature
        except Exception as e:
            logger.error(f"sign.failed: {e}")
            raise Exception("INTERNAL_FAILED")

    def get_get_url(self, app_key, timestamp, signature):
        return f"{ucg_config['host']}{os.getenv('GATEWAY_TOKEN_URL', None)}?appKey={app_key}&timestamp={timestamp}&signature={signature}"

    def build_api_request_url(self, url, token):
        return f"{ucg_config['host']}{url}?access_token={token}"
    
