import hashlib
import json


def process_and_validate_trace(data):
    """
    递归处理 trace 数据：
    1. 加工：清理 servername 字段
    2. 校验：判断是否所有 level 都为空字符串
    :param data: trace_data 的 data 字段（list 或 dict）
    :return: (processed_data, has_non_empty_level)
    """

    def process_node(node):
        # 处理单个节点
        if isinstance(node, dict):
            # 加工 servername
            if "servername" in node and "[" in node["servername"]:
                node["servername"] = node["servername"].split("[")[0]
            # 检查 level 是否非空
            nonlocal has_non_empty_level
            if node.get("level") != "":
                has_non_empty_level = True
            # 递归处理 children
            if "children" in node and isinstance(node["children"], list):
                for child in node["children"]:
                    process_node(child)

    has_non_empty_level = False

    if isinstance(data, list):
        for item in data:
            process_node(item)
    else:
        process_node(data)

    return data, has_non_empty_level


def clean_trace_data(data):
    """
    递归清洗数据，只保留 request, servername, children 字段
    """
    if isinstance(data, dict):
        cleaned = {}
        # 保留当前层级的字段
        for key in ['request', 'servername']:
            if key in data:
                cleaned[key] = data[key]

        # 递归处理 children
        if 'children' in data and isinstance(data['children'], list):
            cleaned['children'] = [clean_trace_data(child) for child in data['children']]
        return cleaned

    elif isinstance(data, list):
        return [clean_trace_data(item) for item in data]

    else:
        return data


def compute_sha256(data_cleaned):
    """
    将清洗后的数据转换为标准化 JSON 字符串并计算 SHA256
    """
    json_str = json.dumps(data_cleaned, sort_keys=True, ensure_ascii=False, separators=(',', ':'))
    sha256_hash = hashlib.sha256(json_str.encode('utf-8')).hexdigest()
    return sha256_hash


def get_trace_data_hash(trace_data: dict) -> str:
    cleaned_trace_data = clean_trace_data(trace_data)
    return compute_sha256(cleaned_trace_data)