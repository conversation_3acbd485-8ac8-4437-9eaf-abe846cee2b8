import json
import time
from datetime import datetime

import pika
import os
from dotenv import load_dotenv
from fastapi import HTTPException

from common.constant import MQ_MAX_WAIT_SECONDS, MQ_SEND_MSG_ACCEPT_COUNT, MQ_RETRY_INTERVAL
from utils.redis_util import RedisClient

load_dotenv()

def multi_singleton(key_arg_index=0):
    def decorator(cls):
        _instance = {}

        def inner(*args, **kwargs):
            # 根据指定位置的参数作为 key
            key = args[key_arg_index] if args else kwargs.get(f"arg{key_arg_index}")
            identifier = (cls, key)
            if identifier not in _instance:
                _instance[identifier] = cls(*args, **kwargs)
            return _instance[identifier]

        return inner
    return decorator


@multi_singleton(key_arg_index=0)
class RabbitMQClient:
    def __init__(self, role):
        self.rabbitmq_url = os.getenv("RABBITMQ_URL")
        self.connection = None
        self.channel = None
        self.redis_client = RedisClient()
        self.role = role

    def connect(self):
        if not self.connection or self.connection.is_closed:
            print("Connecting to RabbitMQ...")
            parameters = pika.URLParameters(self.rabbitmq_url)
            parameters.heartbeat = 60  # 心跳间隔（秒）
            parameters.blocked_connection_timeout = 3600  # 阻塞超时（秒
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
        return self.channel

    def reconnect(self):
        """强制重新连接"""
        if self.connection and self.connection.is_open:
            self.connection.close()
        self.connection = pika.BlockingConnection(pika.URLParameters(self.rabbitmq_url))
        self.channel = self.connection.channel()

    def disconnect(self):
        if self.connection and self.connection.is_open:
            self.connection.close()

    def publish_message(self, queue_name: str, message: dict, priority: int = 0, callback=None):
        try:
            self.connect()  # 确保连接有效
            max_wait_seconds = MQ_MAX_WAIT_SECONDS
            start_time = time.time()
            while True:
                message_count = self.get_queue_message_count(queue_name)
                if message_count < MQ_SEND_MSG_ACCEPT_COUNT:
                    break
                if time.time() - start_time > max_wait_seconds:
                    print(f"Waiting timeout after {max_wait_seconds} seconds, abandoning message: {message}")
                    # 调用 fallback 回调
                    if callback:
                        callback(False, message)
                    return False
                print(f"Queue {queue_name} is full ({message_count} messages), waiting for {MQ_RETRY_INTERVAL} seconds...")
                time.sleep(MQ_RETRY_INTERVAL)

            # 声明队列时设置优先级支持
            self.channel.queue_declare(
                queue=queue_name,
                durable=True,
                passive=False,  # 设置为 False 表示如果不存在则创建
                arguments={'x-max-priority': 10}  # 设置最大优先级为10，优先级越大越优先
            )
            message_body = json.dumps(message).encode('utf-8')
            self.channel.basic_publish(
                exchange='',
                routing_key=queue_name,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=pika.spec.PERSISTENT_DELIVERY_MODE,
                    priority=priority  # 设置消息优先级
                )
            )
            print(f"Published log data to RabbitMQ with priority {priority}: {message}")
            if callback:
                callback(True, message)
            return True
        except (pika.exceptions.AMQPError, pika.exceptions.StreamLostError, pika.exceptions.ConnectionClosed) as e:
            print(f"Connection error when publishing: {e}, attempting reconnect...")
            self.reconnect()
            self.publish_message(queue_name, message, priority)
            return True

    def consume_messages(self, queue_name: str, on_message_callback):
        while True:
            try:
                self.connect()
                self.channel.queue_declare(queue=queue_name, durable=True, arguments={'x-max-priority': 10})
                self.channel.basic_qos(prefetch_count=5)
                self.channel.basic_consume(queue=queue_name, on_message_callback=on_message_callback)

                print("Waiting for messages. To exit press CTRL+C")
                self.channel.start_consuming()
            except (pika.exceptions.StreamLostError, pika.exceptions.ConnectionClosed) as e:
                print(f"Connection lost: {e}, reconnecting in 5 seconds...")
                time.sleep(5)
                self.reconnect()  # 重新建立连接和通道
            except KeyboardInterrupt:
                print("User interrupted.")
                break
            except Exception as e:
                print(f"Unexpected error during consuming: {e}")
                time.sleep(5)
                self.reconnect()

    def get_queue_message_count(self, queue_name: str) -> int:
        # 先从 Redis 中获取缓存值
        cached_count = self.redis_client.get(f'queue:{queue_name}:count')
        if cached_count is not None:
            return int(cached_count)

        # 如果 Redis 中没有缓存值，则从 RabbitMQ 获取实际值
        try:
            self.connect()
            queue = self.channel.queue_declare(queue=queue_name, passive=True, arguments={'x-max-priority': 10})
            count = queue.method.message_count
            # 将值缓存到 Redis 中（例如缓存 5 秒）
            self.redis_client.set(f'queue:{queue_name}:count', str(count), expire=MQ_RETRY_INTERVAL)
            return count
        except pika.exceptions.ChannelClosedByBroker as e:
            print(f"Queue not found or access denied: {e}")
            return -1


    def purge_queue(self, queue_name: str):
        """
        清空指定队列的消息。
        原理：删除队列再重新声明
        """
        try:
            self.connect()
            # 先删除队列
            self.channel.queue_delete(queue=queue_name)
            # 再重新声明队列（保持持久化等属性一致）
            self.channel.queue_declare(queue=queue_name, durable=True, arguments={'x-max-priority': 10})
            return True
        except pika.exceptions.AMQPError as e:
            print(f"Failed to purge queue {queue_name}: {e}")
            return False

    def queue_delete(self, queue_name: str):
        try:
            self.connect()
            self.channel.queue_delete(queue=queue_name)
            return True
        except pika.exceptions.ChannelClosedByBroker as e:
            if e.reply_code == 404:
                return False
            else:
                raise HTTPException(
                    status_code=500,
                    detail=f"删除队列 '{queue_name}' 时发生错误：{e}"
                )

    # 使用
    # rabbitmq_client = RabbitMQClient()
    # rabbitmq_client.publish_message("log_processing_queue", {"traceId": "123", "logContent": "Error occurred..."})
