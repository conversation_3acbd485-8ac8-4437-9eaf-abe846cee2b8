import os
from threading import Lock

import redis
from dotenv import load_dotenv

from utils.logger import logger

load_dotenv()

class RedisClient:
    _instance = None
    _lock = Lock()

    def __new__(self):
        with self._lock:
            if self._instance is None:
                self._instance = super().__new__(self)
                self._instance._initialize()
        return self._instance
    def _initialize(self):
        """
        初始化 Redis 连接
        :param env_file: 环境变量文件路径
        """

        # 获取 Redis 配置
        self.host = os.getenv('REDIS_HOST', 'localhost')
        self.port = int(os.getenv('REDIS_PORT', 6379))
        self.db = int(os.getenv('REDIS_DB', 0))
        self.password = os.getenv('REDIS_PASSWORD', None)  # 读取密码

        # 创建 Redis 连接
        self.client = redis.Redis(host=self.host, port=self.port, db=self.db, password=self.password,
                                 decode_responses=True)

    def set(self, key, value, expire=None):
        """设置键值对"""
        try:
            if expire:
                self.client.set(key, value, ex=expire)
            else:
                self.client.set(key, value)
            return True
        except Exception as e:
            logger.error(f"Error setting key {key}: {e}")
            return False

    def get(self, key):
        """获取键对应的值"""
        try:
            return self.client.get(key)
        except Exception as e:
            logger.error(f"Error getting key {key}: {e}")
            return None

    def delete(self, key):
        """删除键"""
        try:
            return self.client.delete(key)
        except Exception as e:
            logger.error(f"Error deleting key {key}: {e}")
            return 0

    def exists(self, key):
        """检查键是否存在"""
        try:
            return self.client.exists(key)
        except Exception as e:
            logger.error(f"Error checking existence of key {key}: {e}")
            return False

    def expire(self, key, timeout):
        """设置键的过期时间"""
        try:
            return self.client.expire(key, timeout)
        except Exception as e:
            logger.error(f"Error setting expiration for key {key}: {e}")
            return False

# 使用示例
if __name__ == "__main__":
    def test_redis():
        redis_client = RedisClient()
        redis_client.set('test_key', 'test_value', expire=60)
        print(redis_client.get('test_key'))  # 输出: test_value
        print(redis_client.exists('test_key'))  # 输出: 1 (存在)
        redis_client.delete('test_key')
        print(redis_client.get('test_key'))  # 输出: None (不存在)

    test_redis()