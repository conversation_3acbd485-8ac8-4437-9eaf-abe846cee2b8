import os
import sys
import os.path

from common.constant import GATEWAY_ACCESS_TOKEN_CURR_ENV_KEY, GATEWAY_ACCESS_TOKEN_PROD_KEY
# 添加项目根目录到Python路径
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api_gateway import ApiGatewayService
from utils.api_prod_gateway import ApiProdGatewayService
from utils.logger import logger
from utils.redis_util import RedisClient

api_gateway_service = ApiGatewayService()
api_prod_gateway_service = ApiProdGatewayService()

def get_api_gateway_token():
    token = None
    redis_client = None
    if os.getenv('USE_REDIS', 'false').lower()=='true':
        redis_client = RedisClient()
        token = redis_client.get(GATEWAY_ACCESS_TOKEN_CURR_ENV_KEY)
    if token is None:
        token, expire = api_gateway_service.get_token()
        if token is not None and redis_client:
            redis_client.set(GATEWAY_ACCESS_TOKEN_CURR_ENV_KEY, token, expire=int(expire))
        logger.debug(f"Token: {token}")
    return token

def get_api_prod_gateway_token():
    token = None
    redis_client = None
    if os.getenv('USE_REDIS', 'false').lower()=='true':
        redis_client = RedisClient()
        token = redis_client.get(GATEWAY_ACCESS_TOKEN_PROD_KEY)
    if token is None:
        token, expire = api_prod_gateway_service.get_token()
        if token is not None and redis_client:
            redis_client.set(GATEWAY_ACCESS_TOKEN_PROD_KEY, token, expire=int(expire))
        logger.debug(f"Token: {token}")
    return token

# if __name__ == "__main__":
#     print(get_api_gateway_token())