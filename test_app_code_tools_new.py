#!/usr/bin/env python3
"""
测试修改后的 app_code_tools_new.py 功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.app_code_tools_new import search_code_by_app_code

def test_file_search_with_error_line():
    """测试文件名搜索 + 报错行数功能"""
    print("=== 测试文件名搜索 + 报错行数功能 ===")
    
    result = search_code_by_app_code(
        app_code="test_app",
        file_name="TestController.java",
        error_line_number=25
    )
    
    print(f"搜索结果: {result.get('success', False)}")
    if result.get('search_results'):
        print("搜索类型:", list(result['search_results'].keys()))
    
    return result

def test_file_search_without_error_line():
    """测试文件名搜索（无报错行数）功能"""
    print("\n=== 测试文件名搜索（无报错行数）功能 ===")
    
    result = search_code_by_app_code(
        app_code="test_app",
        file_name="TestController.java"
    )
    
    print(f"搜索结果: {result.get('success', False)}")
    if result.get('search_results'):
        print("搜索类型:", list(result['search_results'].keys()))
    
    return result

def test_sql_search():
    """测试SQL搜索功能"""
    print("\n=== 测试SQL搜索功能 ===")
    
    result = search_code_by_app_code(
        app_code="test_app",
        sql_fragment="SELECT * FROM user_info WHERE user_id = ?"
    )
    
    print(f"搜索结果: {result.get('success', False)}")
    if result.get('search_results'):
        print("搜索类型:", list(result['search_results'].keys()))
    
    return result

def test_url_search():
    """测试URL搜索功能"""
    print("\n=== 测试URL搜索功能 ===")
    
    result = search_code_by_app_code(
        app_code="test_app",
        server_url="/api/v1/user/list"
    )
    
    print(f"搜索结果: {result.get('success', False)}")
    if result.get('search_results'):
        print("搜索类型:", list(result['search_results'].keys()))
    
    return result

def main():
    """主测试函数"""
    print("开始测试修改后的 app_code_tools_new.py 功能...")
    
    # 注意：这些测试可能会失败，因为需要实际的Git仓库和代码
    # 但可以验证函数调用和参数传递是否正确
    
    try:
        test_file_search_with_error_line()
    except Exception as e:
        print(f"文件搜索+报错行数测试异常: {e}")
    
    try:
        test_file_search_without_error_line()
    except Exception as e:
        print(f"文件搜索测试异常: {e}")
    
    try:
        test_sql_search()
    except Exception as e:
        print(f"SQL搜索测试异常: {e}")
    
    try:
        test_url_search()
    except Exception as e:
        print(f"URL搜索测试异常: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
