from pydantic import BaseModel

class Response(BaseModel):
    success: bool
    code: int
    message: str
    data: object

    @classmethod
    def succeed(cls, data: object = None, message: str = "操作成功"):
        return cls(
            success=True,
            code=200,
            message=message,
            data=data
        )

    @classmethod
    def fail(cls, message: str = "服务器错误", code: int = 500, data: object = None):
        return cls(
            success=False,
            code=code,
            message=message,
            data=data
        )
