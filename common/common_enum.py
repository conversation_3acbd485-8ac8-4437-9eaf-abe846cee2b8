from enum import Enum

# job触发表的状态
class JobStatus(Enum):
    DONE = "done"
    PROCESSING = "processing"

# 日志类型 对应五个接口
class LogType(Enum):
    HTTP_REQ_SLOW = "http_req_slow"
    RESOURCE_LOAD_SLOW = "resource_load_slow"
    ACCESS_USE_CASE_SLOW = "access_use_case_slow"
    WHITE_SCREEN_TIME_SLOW = "white_screen_time_slow"
    HTTP_REQ_40_50 = "http_req_40_50"

# 日志处理表的状态
# 一轮去重后created，二轮去重后取完log和trace为processing
# 二轮去重被过滤掉则duplicated
class LogProcessStatus(Enum):
    # 已创建
    CREATED = "created"
    # 正在处理，已发送至mq
    PROCESSING = "processing"
    # AI处理中，已发送至AI
    AI_PROCESSING = "ai_processing"
    # AI处理中已分析完毕根因
    AI_PROCESSING_DONE_RCA = "ai_processing_done_rca"
    # AI处理中已生成完报告
    AI_PROCESSING_DONE_REPORT = "ai_processing_done_report"
    # 已处理完毕
    DONE = "done"
    # 重复
    DUPLICATED = "duplicated"
    #  异常
    EXCEPTION = "exception"

class SourceType(Enum):
    JOB = "job"
    USER = "user"

# 设置消息优先级
class TracePriority(Enum):
    VIP = 9
    MANUAL = 8
    JOB = 7


class SolutionStatus(Enum):
    TO_BE_GENERATED = "待生成"
    GENERATING = "生成中"
    GENERATED = "已生成"


class SolutionConfirmStatus(Enum):
    UNCONFIRMED = "未确认"
    PARTIALLY_CONFIRMED = "部分确认"
    FULLY_CONFIRMED = "全部确认"

class SolutionIssueStatus(Enum):
    UNISSUED = "未下发"
    PARTIALLY_ISSUED = "部分下发"
    FULLY_ISSUED = "全部下发"

class StageStatus(Enum):
    UN_STARTED = "未启动"
    PROCESSING = "处理中"
    SUCCESS = "成功完成"
    FAILED = "失败"

class StageCatalog(Enum):
    READ_LOG = "读取日志"
    ERROR_LOCATION = "错误定位"
    ROOT_CAUSE_DIAGNOSIS = "根因诊断"
    SOLUTION_GENERATION = "解决方案生成"
    AI_JUDGEMENT = "AI对方案可信度判断"
    HUMAN_CONFIRMATION = "人工确认方案"
    ISSUE_DISPATCH = "下发整改"

class StageName(Enum):
    READ_LOG = "读取日志"
    ERROR_LOCATION = "错误定位"
    CODE_DIAGNOSIS = "代码诊断"
    DDL_DIAGNOSIS = "DDL诊断"
    MIDDLEWARE_RESOURCE_DIAGNOSIS = "中间件资源诊断"
    K8S_RESOURCE_DIAGNOSIS = "K8S资源诊断"
    SOLUTION_GENERATION = "解决方案生成"
    AI_JUDGEMENT = "AI对方案可信度判断"
    HUMAN_CONFIRMATION = "人工确认方案"
    ISSUE_DISPATCH = "下发整改"
