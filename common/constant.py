
import os
from dotenv import load_dotenv
load_dotenv()

# RabbitMQ 相关常量
LOG_PROCESSING_QUEUE = os.getenv("MQ_LOG_PROCESSING_QUEUE_NAME", "log_processing_queue")
# RabbitMQ 限流相关常量
# 最大等待时间 秒 最多15分钟 每类5个，每个最多等3分钟
MQ_MAX_WAIT_SECONDS = int(os.getenv("MQ_MAX_WAIT_SECONDS", "180"))
# 队列最大消息数阈值 低于则继续发消息
MQ_SEND_MSG_ACCEPT_COUNT = int(os.getenv("MQ_SEND_MSG_ACCEPT_COUNT", "5"))
# 每次等待间隔（秒）
MQ_RETRY_INTERVAL = int(os.getenv("MQ_RETRY_INTERVAL", "5"))
# 网关token在redis中的key
env = os.getenv("ENV", "SIT")
GATEWAY_ACCESS_TOKEN_CURR_ENV_KEY = f"GATEWAY_ACCESS_TOKEN_{env.upper()}"
GATEWAY_ACCESS_TOKEN_PROD_KEY = "GATEWAY_ACCESS_TOKEN_PROD"

# Job相关配置
#日志扫描最大前移天数
JOB_MAX_MINUS_DAY=int(os.getenv("JOB_MAX_MINUS_DAY", "1"))
#日志触发每页数量
JOB_PAGE_SIZE=int(os.getenv("JOB_PAGE_SIZE", "10"))
#每种类型最小的触发数量
JOB_MIN_NUM_PER_TYPE=int(os.getenv("JOB_MIN_NUM_PER_TYPE", "5"))
# 消费端实例数量
CONSUMERS_COUNT = int(os.getenv("CONSUMERS_COUNT", "5"))

VERSION_START = 1
