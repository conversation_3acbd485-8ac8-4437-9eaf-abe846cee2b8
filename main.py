import asyncio
from contextlib import asynccontextmanager
from datetime import datetime

import uvicorn
from fastapi import FastAPI, Body
from fastapi import Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from common.common_enum import LogType
from common.constant import LOG_PROCESSING_QUEUE, CONSUMERS_COUNT
from common.response_model import Response
from services.db.job_trigger_service import JobTriggerService
from services.db.log_process_service import LogProcessService
from services.log_fetch_service import LogFetchService
from services.log_handle_service import LogHandleService
from services.user_manual_service import UserManualService
from utils.get_token import get_api_gateway_token
from utils.logger import logger
from utils.mq_client import RabbitMQClient
from utils.redis_util import RedisClient
from services.vo.log_dashboard_req_vo import LogProcessDashboardParams
from dataclasses import asdict
from services.dashboard_service import DashboardService


# ===== 生命周期管理器 =====
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动 RabbitMQ 消费者（在后台运行）
    await asyncio.to_thread(log_handle_service.start_multiple_consumers, num_workers=CONSUMERS_COUNT)

    yield

    # shutdown 阶段
    print("FastAPI is shutting down...")
    log_handle_service.stop()


app = FastAPI(lifespan=lifespan)
job_trigger_service = JobTriggerService()
log_fetch_service = LogFetchService()
log_handle_service = LogHandleService(queue_name=LOG_PROCESSING_QUEUE)
log_process_service = LogProcessService()
user_manual_service = UserManualService()
dashboard_service = DashboardService()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/token")
async def get_token():
    try:
        token = get_api_gateway_token()
        return Response.succeed(message="Token获取成功", data={"token": token,
            "expires_in": 3600 })
    except Exception as e:
        logger.error(f"Token获取失败: {str(e)}")
        return Response.fail(code=500, message="Token获取失败")

@app.get("/log-process/{trace_id}")
async def get_log_process_by_trace(trace_id: str):
    """
    根据 trace_id 查询日志处理记录
    """
    result = log_process_service.get_log_process_by_trace_id(trace_id)
    if not result:
        return Response.fail(message="Log process not found")
    return Response.succeed(data=result)

class TraceRequest(BaseModel):
    trace_id: str
    user_id: int
    trace_time: datetime

@app.post("/submit_trace")
async def submit_trace(req: TraceRequest = Body(...)):
    """
    根据 trace_id 查询日志处理记录
    """
    response = user_manual_service.submit_trace(req.user_id, req.trace_id, req.trace_time)
    return response

@app.get("/start_job_http_req_slow")
async def start_job_http_req_slow(
    execution_time: datetime = Query(None, description="执行时间(格式:YYYY-MM-DD HH:mm:ss)"),
    system_code: str = Query(None, description="系统编码（可选）")
):
    # 处理时间参数
    end_time = execution_time or datetime.now().replace(microsecond=0)
    res = log_fetch_service.fetch_log_by_type(end_time, LogType.HTTP_REQ_SLOW.value ,system_code)
    return Response.succeed(data=res)

@app.get("/start_job_resource_load_slow")
async def start_job_resource_load_slow(
    execution_time: datetime = Query(None, description="执行时间(格式:YYYY-MM-DD HH:mm:ss)"),
    system_code: str = Query(None, description="系统编码（可选）")
):
    # 处理时间参数
    end_time = execution_time or datetime.now().replace(microsecond=0)
    res = log_fetch_service.fetch_log_by_type(end_time, LogType.RESOURCE_LOAD_SLOW.value ,system_code)
    return Response.succeed(data=res)

@app.get("/start_job_access_use_case_slow")
async def start_job_access_use_case_slow(
    execution_time: datetime = Query(None, description="执行时间(格式:YYYY-MM-DD HH:mm:ss)"),
    system_code: str = Query(None, description="系统编码（可选）")
):
    # 处理时间参数
    end_time = execution_time or datetime.now().replace(microsecond=0)
    res = log_fetch_service.fetch_log_by_type(end_time, LogType.ACCESS_USE_CASE_SLOW.value ,system_code)
    return Response.succeed(data=res)

@app.get("/start_job_white_screen_time_slow")
async def start_job_white_screen_time_slow(
    execution_time: datetime = Query(None, description="执行时间(格式:YYYY-MM-DD HH:mm:ss)"),
    system_code: str = Query(None, description="系统编码（可选）")
):
    # 处理时间参数
    end_time = execution_time or datetime.now().replace(microsecond=0)
    res = log_fetch_service.fetch_log_by_type(end_time, LogType.WHITE_SCREEN_TIME_SLOW.value ,system_code)
    return Response.succeed(data=res)

@app.get("/start_job_http_req_40_50")
async def start_job_http_req_40_50(
    execution_time: datetime = Query(None, description="执行时间(格式:YYYY-MM-DD HH:mm:ss)"),
    system_code: str = Query(None, description="系统编码（可选）")
):
    # 处理时间参数
    end_time = execution_time or datetime.now().replace(microsecond=0)
    res = log_fetch_service.fetch_log_by_type(end_time, LogType.HTTP_REQ_40_50.value ,system_code)
    return Response.succeed(data=res)


@app.post('/log-process-dashboard')
def get_log_process_dashboard(req: LogProcessDashboardParams):
    # try:
    #     req.validate_time_range()
    # except ValueError as e:
    #     return Response.fail(code=400, message=str(e))

    log_process_list = dashboard_service.get_log_process_list(req)

    # 将 dataclass 对象转换为字典
    result = [asdict(lp) for lp in log_process_list]
    res = {
        "record": result,
        "page_num": req.page_num,
        "page_size": req.page_size
    }

    return Response.succeed(res)

@app.post('/log-process-stage')
async def log_stage(req: dict):
    id = req["log_process_id"]
    trace_id = req["trace_id"]
    log_process = None
    if not id and not trace_id:
        logger.warning("缺少必要参数", extra={"missing_fields": ["id", "trace_id"]})
        return Response.fail(message="必须提供id或trace_id")

    if id:
        log_process = log_process_service.get_log_process_by_id(id)
    if log_process is None and trace_id:
        log_process = log_process_service.get_log_process_by_trace_id(trace_id)
    if log_process is None:
        return Response.fail(message="Log process not found")

    result = dashboard_service.get_log_process_stage(log_process)
    return Response.succeed(result)

@app.post("/regen-solution")
async def regen_solution(req: dict):
    """再次诊断入口"""

    async def background_regen_task(handle_function, log_process):
        try:
            await asyncio.to_thread(handle_function, log_process)
        except Exception as e:
            logger.error(f"后台任务执行失败: {e}", exc_info=True)

    id = req["log_process_id"]
    trace_id = req["trace_id"]
    log_process = None
    if not id and not trace_id:
        logger.warning("缺少必要参数", extra={"missing_fields": ["id", "trace_id"]})
        return Response.fail(message="必须提供id或trace_id")

    if id:
        log_process = log_process_service.get_log_process_by_id(id)
    if log_process is None and trace_id:
        log_process = log_process_service.get_log_process_by_trace_id(trace_id)
    if log_process is None:
        return Response.fail(message="Log process not found")
    # 创建后台任务
    task = asyncio.create_task(background_regen_task(log_handle_service.regen_solution, log_process))

    # result = log_handle_service.regen_solution(log_process)
    return Response.succeed(data="提交再诊断任务成功")


@app.get("/clear-queue")
async def clear_queue(queue_name: str = Query(None, description="系统编码（可选）")):
    if not queue_name:
        queue_name = LOG_PROCESSING_QUEUE
    rabbitmq_client = RabbitMQClient("admin")  # 角色不影响，只是为了实例化
    success = rabbitmq_client.purge_queue(queue_name)
    if success:
        return Response.succeed(message=f"Queue '{queue_name}' cleared successfully.")
    else:
        return Response.fail(code=500, message=f"Failed to clear queue '{queue_name}'.")

@app.post("/delete-queue")
def delete_queue(queue_name: str = Query(None, description="队列名")):
    if not queue_name:
        queue_name = LOG_PROCESSING_QUEUE
    rabbitmq_client = RabbitMQClient("admin")
    success = rabbitmq_client.queue_delete(queue_name)
    if success:
        return Response.succeed(message=f"Queue '{queue_name}' deleted successfully.")
    else:
        return Response.fail(code=500, message=f"Failed to delete queue '{queue_name}'.")

@app.get("/get_queue_count")
def get_queue_count(queue_name: str = Query(None, description="队列名")):
    if not queue_name:
        queue_name = LOG_PROCESSING_QUEUE
    rabbitmq_client = RabbitMQClient("admin")
    count = rabbitmq_client.get_queue_message_count(queue_name)

    return Response.succeed(message=f"Queue '{queue_name}' count is {count}")


@app.post("/execute_redis_command")
async def execute_redis_command(
    req: dict
):
    try:
        command = req.get("command")
        args = req.get("args", [])
        redis_client = RedisClient()
        # 动态调用 Redis 命令
        method = getattr(redis_client.client, command)
        result = method(*args)
        return Response(success=True, code=200, message="Redis 命令执行成功", data=result)
    except AttributeError:
        return Response(success=False, code=400, message="无效的 Redis 命令", data=None)
    except Exception as e:
        return Response(success=False, code=500, message=f"执行 Redis 命令时出错: {str(e)}", data=None)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)