#!/usr/bin/env python3
"""
外部触发日志分析测试脚本

该脚本用于测试外部触发模式的日志分析流程
1. 从指定API获取外部数据结构体
2. 调用日志分析服务进行分析
3. 输出分析结果
"""

import asyncio
import json
import requests
from datetime import datetime
from typing import Dict, Any, Optional

# 导入日志分析服务
from services.logs_analysis_service import process_log_analysis_from_external_data


class ExternalTriggerTester:
    """外部触发测试器"""
    
    def __init__(self, api_url: str, timeout: int = 30):
        self.api_url = api_url
        self.timeout = timeout
    
    def fetch_external_data(self) -> Optional[Dict[str, Any]]:
        """从外部API获取数据结构体"""
        try:
            print(f"🌐 正在从外部API获取数据...")
            print(f"   URL: {self.api_url}")
            
            response = requests.get(self.api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            print(f"✅ 成功获取API响应数据")
            print(f"   状态码: {response.status_code}")
            print(f"   响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            
            # 提取data字段
            if isinstance(data, dict) and 'data' in data:
                external_data = data['data']
                print(f"✅ 成功提取data字段")
                print(f"   数据类型: {type(external_data)}")
                if isinstance(external_data, dict):
                    print(f"   数据字段: {list(external_data.keys())}")
                return external_data
            else:
                print(f"❌ 响应数据中没有找到'data'字段")
                print(f"   完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {str(e)}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            print(f"❌ 获取外部数据时出现未知错误: {str(e)}")
            return None
    
    def validate_external_data(self, data: Dict[str, Any]) -> bool:
        """验证外部数据结构的完整性"""
        print(f"🔍 验证外部数据结构...")
        
        # 必需字段
        required_fields = [
            'id', 'trace_id', 'request', 'request_time',
            'sys_code', 'count', 'log_type', 'job_trigger_id',
            'status', 'user_id', 'trace_data'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
            return False
        
        # 验证trace_data字段
        if not isinstance(data.get('trace_data'), dict):
            print(f"❌ trace_data字段必须是字典类型")
            try:
                data['trace_data'] = json.loads(data['trace_data'])[0]
                # 保存转化后的trace_data到文件,使用traceid做区分
                trace_data_file = f'trace_data_{data.get("trace_id", "unknown")}.json'
                with open(trace_data_file, 'w') as f:
                    json.dump(data['trace_data'], f, ensure_ascii=False, indent=2)
            except:
                print(f"❌ 转化trace_data为字典失败")
                return False

        # 验证log_data字段
        # if not isinstance(data.get('log_data'), list):
        #     print(f"❌ log_data字段必须是列表类型")
        #     try:
        #         data['log_data'] = json.loads(data['log_data'])
        #         # 保存转化后的log_data到文件
        #         log_data_file = f'log_data_{data.get("trace_id", "unknown")}.json'
        #         with open(log_data_file, 'w') as f:
        #             json.dump(data['log_data'], f, ensure_ascii=False, indent=2)
        #     except:
        #         print(f"❌ 转化log_data为列表失败")
        #         # return False
            
        #     return True
        
        print(f"✅ 数据结构验证通过")
        print(f"   Trace ID: {data.get('trace_id')}")
        print(f"   系统编号: {data.get('sys_code')}")
        print(f"   请求时间: {data.get('request_time')}")
        print(f"   日志类型: {data.get('log_type')}")
        
        return True
    
    def print_data_summary(self, data: Dict[str, Any]):
        """打印数据摘要信息"""
        print(f"\n📋 外部数据摘要:")
        print(f"   ID: {data.get('id', 'N/A')}")
        print(f"   Trace ID: {data.get('trace_id', 'N/A')}")
        print(f"   请求: {data.get('request', 'N/A')}")
        print(f"   请求时间: {data.get('request_time', 'N/A')}")
        print(f"   系统编号: {data.get('sys_code', 'N/A')}")
        print(f"   计数: {data.get('count', 'N/A')}")
        print(f"   日志类型: {data.get('log_type', 'N/A')}")
        print(f"   任务触发ID: {data.get('job_trigger_id', 'N/A')}")
        print(f"   状态: {data.get('status', 'N/A')}")
        print(f"   用户ID: {data.get('user_id', 'N/A')}")
        
        trace_data = data.get('trace_data', {})
        if isinstance(trace_data, dict):
            print(f"   Trace数据字段数: {len(trace_data)}")
            if trace_data:
                print(f"   Trace数据示例字段: {list(trace_data.keys())[:5]}")
        
    async def run_analysis(self, external_data: Dict[str, Any], 
                          api_timeout: int = 1200,
                          max_retries: int = 1,
                          api_temperature: float = 0.0,
                          show_debug: bool = True,
                          use_react_mode: bool = False,
                          max_react_iterations: int = 2) -> Dict[str, Any]:
        """运行日志分析"""
        print(f"\n🚀 开始执行外部触发模式日志分析...")
        print(f"   API超时: {api_timeout}秒")
        print(f"   最大重试: {max_retries}次")
        print(f"   模型温度: {api_temperature}")
        print(f"   调试模式: {show_debug}")
        print(f"   ReAct模式: {use_react_mode}")
        if use_react_mode:
            print(f"   ReAct最大迭代: {max_react_iterations}")
        
        start_time = datetime.now()
        
        try:
            result = process_log_analysis_from_external_data(
                request_data=external_data,
                api_timeout=api_timeout,
                max_retries=max_retries,
                api_temperature=api_temperature,
                show_debug=show_debug,
                use_react_mode=use_react_mode,
                max_react_iterations=max_react_iterations
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"\n⏱️ 分析耗时: {duration:.2f}秒")
            
            return result
            
        except Exception as e:
            print(f"❌ 分析过程中出现异常: {str(e)}")
            return {
                "success": False,
                "data": None,
                "error": str(e)
            }
    
    def print_analysis_result(self, result: Dict[str, Any]):
        """打印分析结果"""
        print(f"\n📊 分析结果:")
        print(f"   成功状态: {result.get('success', False)}")
        
        if result.get('success'):
            data = result.get('data', {})
            if data:
                print(f"   请求ID: {data.get('request_id', 'N/A')}")
                print(f"   TraceID: {data.get('traceid', 'N/A')}")
                print(f"   系统编号: {data.get('sys_code', 'N/A')}")
                
                # 分析报告
                analysis_report = data.get('analysis_report')
                # if analysis_report:
                #     print(f"   分析报告长度: {len(analysis_report)}字符")
                #     print(f"   分析报告预览: {analysis_report[:200]}...")
                
                # 错误位置
                error_locations = data.get('error_locations')
                if error_locations:
                    print(f"   错误位置信息: {type(error_locations)}")
                
                # 工具结果
                tool_results = data.get('tool_results')
                if tool_results:
                    print(f"   工具结果长度: {len(tool_results)}字符")
                
                # 生成的文件
                files_generated = data.get('files_generated', [])
                if files_generated:
                    print(f"   生成文件数: {len(files_generated)}")
                    for file_path in files_generated:
                        print(f"     - {file_path}")
        else:
            error = result.get('error', '未知错误')
            print(f"   错误信息: {error}")


async def main():
    """主函数"""
    print("=" * 60)
    print("外部触发日志分析测试脚本")
    print("=" * 60)
    
    # 配置参数
    API_URLS = [
        # "http://10.65.39.186:8000/log-process/aca66ba9dfd483d6",
        # "http://10.65.39.186:8000/log-process/4ebe9401ad8c8be2",
        # "http://10.65.39.186:8000/log-process/b263e4c0ccd2396f",
        # "http://10.65.39.186:8000/log-process/3d2fd632a48292f6",
        # "http://10.65.39.186:8000/log-process/49f2730ab1a1f2de",
        # "http://10.65.39.186:8000/log-process/5ef4e7b49041cf97",
        # "http://10.65.39.186:8000/log-process/69becc88ef5e2944",
        # "http://10.65.39.186:8000/log-process/24a405f5a50977b4",
        # "http://10.65.39.186:8000/log-process/9e84dc47e02adbda",
        # "http://10.65.39.186:8000/log-process/2ecd189c96982c56",
        # "http://10.65.39.2:8000/log-process/cc120ceadbe85d9b",
        # "https://apps-fc-monitor-rcp-uat.faw.cn/log-process/e41fc1cc3e8d86d4",
        # "https://apps-fc-monitor-rcp-uat.faw.cn/log-process/630fd04080a694bd",
        "http://10.65.39.23:8000/log-process/424eeda3b4acab5f"
    ]

    # 分析参数
    ANALYSIS_CONFIG = {
        "api_timeout": 2400,        # API超时时间
        "max_retries": 2,           # 最大重试次数
        "api_temperature": 0,     # 模型温度
        "show_debug": True,         # 显示调试信息
        "use_react_mode": False,    # 是否使用ReAct模式
        "max_react_iterations": 5   # ReAct最大迭代次数
    }

    for API_URL in API_URLS:
        print(f"\n正在处理 {API_URL}")
        # 创建测试器
        tester = ExternalTriggerTester(API_URL)
        
        try:
            # 步骤1: 获取外部数据
            print(f"\n📥 步骤1: 获取外部数据")
            external_data = tester.fetch_external_data()
            
            if external_data is None:
                print(f"❌ 无法获取外部数据，跳过当前URL")
                continue
            
            # 步骤2: 验证数据结构
            print(f"\n🔍 步骤2: 验证数据结构")
            if not tester.validate_external_data(external_data):
                print(f"❌ 数据结构验证失败，跳过当前URL")
                continue
            
            # 步骤3: 打印数据摘要
            tester.print_data_summary(external_data)
            
            # 步骤4: 执行日志分析
            print(f"\n🔬 步骤4: 执行日志分析")
            result = await tester.run_analysis(external_data, **ANALYSIS_CONFIG)
            
            # 步骤5: 打印分析结果
            print(f"\n📋 步骤5: 分析结果")
            tester.print_analysis_result(result)
            
            # 保存完整结果到文件
            result_file = f"external_trigger_test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 完整结果已保存到: {result_file}")
            
            print(f"\n✅ 当前URL处理完成！")
            
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断测试")
            break
        except Exception as e:
            print(f"\n❌ 处理当前URL时出现异常: {str(e)}")
            import traceback
            traceback.print_exc()
            continue

    print("\n所有URL处理完成!")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main()) 