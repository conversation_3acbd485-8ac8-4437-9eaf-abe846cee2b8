import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import openai
import os
from tqdm import tqdm
from .vector_store import VectorStore
from .config import EMBEDDING_CONFIG
from .llm_client import PrivateLLMClient
from .embedding_client import EmbeddingClient
from utils.logger import logger

# 在文件开头的导入部分保持不变

class IndexCreator:
    def __init__(self):
        self.vector_store = VectorStore()
        
        # 初始化私有云大模型客户端
        self.llm_client = PrivateLLMClient()
        
        # 初始化Embedding客户端        
        self.embedding_client = EmbeddingClient()


    def create_embeddings(self, texts: List[str]) -> List[List[float]]:
        """使用私有云大模型API创建文本嵌入"""
        if not texts:
            return []
            
        try:
            # 使用私有云大模型获取嵌入
            embeddings = []
            for text in tqdm(texts, desc="创建嵌入"):
                logger.info(f"正在为文本创建嵌入: {text[:50]}...")  # 仅打印前50个字符
                embedding = self.embedding_client.get_embedding(text)
                logger.debug(f"嵌入向量长度: {len(embedding)}")
                embeddings.append(embedding)
            return embeddings
        except Exception as e:
            print(f"使用私有云大模型创建嵌入时出错: {e}")
            print("尝试使用OpenAI API作为备选...")
            
            # 如果私有云大模型失败，尝试使用OpenAI API作为备选
            try:
                response = openai.Embedding.create(
                    model=self.model,
                    input=texts
                )
                return [data["embedding"] for data in response["data"]]
            except Exception as e2:
                print(f"使用OpenAI API创建嵌入时也出错: {e2}")
                # 返回空向量作为后备
                return [[0.0] * 1536 for _ in range(len(texts))]

    def process_excel(self, file_path: str) -> int:
        """处理Excel文件并创建索引"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 确保所有必需的列都存在
            required_columns = ["problem_signature", "raw_log", "business_context", "root_cause", "solution"]
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Excel文件缺少必需的列: {col}")
            
            # 将DataFrame转换为文档列表
            documents = df.to_dict(orient="records")
            
            # 合并三个字段的内容创建一个统一的文本
            combined_texts = []
            for doc in documents:
                problem_signature = doc.get("problem_signature", "")
                raw_log = doc.get("raw_log", "")
                business_context = doc.get("business_context", "")
                
                # 合并三个字段，用空格分隔
                combined_text = f"{problem_signature} {raw_log} {business_context}".strip()
                combined_texts.append(combined_text)
            
            print("正在为合并内容创建嵌入...")
            content_vectors = self.create_embeddings(combined_texts)
            logger.info(f"创建了{len(content_vectors)}个嵌入向量")
            logger.info(f"documents length: {len(documents)}")  
            # 添加文档到向量存储
            count = self.vector_store.add_documents(documents, content_vectors)
            logger.info(f"成功添加了{count}条记录到向量存储")
            return count
        except Exception as e:
            print(f"处理Excel文件时出错: {e}")
            raise
        finally:
            self.vector_store.close()

    def clear_index(self):
        """清除所有索引数据"""
        try:
            self.vector_store.delete_all()
            return True
        except Exception as e:
            print(f"清除索引时出错: {e}")
            return False
        finally:
            self.vector_store.close()