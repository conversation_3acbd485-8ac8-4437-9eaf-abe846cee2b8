from typing import List, Dict, Any, Optional
from .vector_store import VectorStore
from .config import VECTOR_CONFIG
from .llm_client import PrivateLLMClient
from .rerank_client import RerankClient
from .embedding_client import EmbeddingClient
from utils.logger import logger

class Retriever:
    def __init__(self):
        self.vector_store = VectorStore()
        self.top_k = VECTOR_CONFIG["TOP_K"]
        
        # 初始化私有云大模型客户端
        self.llm_client = PrivateLLMClient()
        
        # 初始化Rerank客户端
        self.rerank_client = RerankClient()

        # 初始化Embedding客户端
        self.embedding_client = EmbeddingClient()

    def create_query_embedding(self, query: str) -> List[float]:
        """为查询创建嵌入向量"""
        try:
            # 使用私有云大模型获取嵌入
            return self.embedding_client.get_embedding(query)
        except Exception as e:
            logger.error(f"创建查询嵌入时出错: {e}")
            # 返回空向量作为后备
            return [0.0] * self.llm_client.dimension

    def retrieve(self, query: str, top_k: Optional[int] = None, use_rerank: bool = False) -> List[Dict[str, Any]]:
        """检索与查询相关的文档"""
        try:
            # 创建查询嵌入
            logger.info(f"正在为查询创建嵌入: {query[:50]}...")  # 仅打印前50个字符
            query_vector = self.create_query_embedding(query)
            logger.debug(f"查询嵌入向量长度: {len(query_vector)}")
            
            # 执行相似度搜索
            results = self.vector_store.similarity_search(
                query_vector=query_vector,
                top_k=top_k or self.top_k
            )
            logger.info(f"检索到的结果数量: {results}")
            
            # 如果启用了rerank，对结果进行重排序
            if use_rerank and results:
                # 提取文档内容
                documents = []
                solutions = []
                for result in results:
                    # 合并三个字段作为文档内容
                    documents.append( f"{result['problem_signature']}|{result['raw_log']}|{result['business_context']}".strip())
                    # 仅保留根本原因和解决方案
                    solutions.append(f"{result['root_cause']}|{result['solution']}".strip())

                # 调用rerank服务
                reranked_results = self.rerank_client.rerank(query, documents)
                logger.info(f"Rerank结果: {reranked_results}")

                # 如果rerank成功，重新排序原始结果
                if reranked_results:
                    # 创建新的排序结果列表
                    sorted_results = []
                    for reranked in reranked_results:
                        #logger.debug(f"reranked: {reranked}")
                        reranked['document'] = documents[reranked['index']]
                        reranked['problem_signature'] = documents[reranked['index']].split('|')[0]
                        reranked['raw_log'] = documents[reranked['index']].split('|')[1] 
                        reranked['business_context'] = documents[reranked['index']].split('|')[2] 
                        reranked['root_cause'] = solutions[reranked['index']].split('|')[0]
                        reranked['solution'] = solutions[reranked['index']].split('|')[1]
                        sorted_results.append(reranked) 

                    logger.info(f"Rerank+document结果: {reranked_results}")
                    # 如果所有结果都成功重排序，返回重排序后的结果
                    return sorted_results
            
            # 如果没有启用rerank或rerank失败，返回原始结果
            return results
        except Exception as e:
            logger.error(f"检索时出错: {e}")
            return []
        finally:
            self.vector_store.close()