import os
from dotenv import load_dotenv

load_dotenv()

# ClickHouse配置
CLICKHOUSE_CONFIG = {
    "HOST": os.getenv("MYSCALE_HOST", "localhost"),
    "PORT": int(os.getenv("MYSCALE_PORT", "8123")),
    "USER": os.getenv("MYSCALE_USER", "default"),
    "PASSWORD": os.getenv("MYSCALE_PASSWORD", ""),
    "DATABASE": os.getenv("MYSCALE_DATABASE", "default"),
    "DB_CONN_NUM": int(os.getenv("MYSCALE_DB_CONN_NUM", "5")),
    "RETRY_TIMES": int(os.getenv("MYSCALE_RETRY_TIMES", "3")),
    "RETRY_DELAY": float(os.getenv("MYSCALE_RETRY_DELAY", "0.5")),
}

# 向量存储配置
VECTOR_CONFIG = {
    "TABLE_NAME": os.getenv("VECTOR_TABLE_NAME", "knowledge_base"),
    "VECTOR_SIZE": int(os.getenv("VECTOR_SIZE", "768")),  # 默认768维度，与.env文件保持一致
    "TOP_K": int(os.getenv("RETRIEVAL_TOP_K", "5")),
}

# 私有云 Embedding 配置
EMBEDDING_CONFIG = {
    "API_BASE": os.getenv("PRIVATE_EMBEDDING_API_BASE", "https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/embeddings"),
    "ACCESS_TOKEN": os.getenv("PRIVATE_EMBEDDING_ACCESS_TOKEN", ""),
    "MODEL": os.getenv("PRIVATE_EMBEDDING_MODEL", "text-embedding-v2"),
    "DIMENSION": int(os.getenv("EMBEDDING_DIMENSION", "1024")),
    "ENCODING_FORMAT": os.getenv("EMBEDDING_ENCODING_FORMAT", "float"),
}

# 私有云大模型配置
PRIVATE_LLM_CONFIG = {
    "API_BASE": os.getenv("PRIVATE_LLM_API_BASE", "https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/DEEPSEEK-R1-671B/completions"),
    "ACCESS_TOKEN": os.getenv("PRIVATE_LLM_ACCESS_TOKEN", ""),
    "MODEL": os.getenv("PRIVATE_LLM_MODEL", "DeepSeek-R1"),
    "TEMPERATURE": float(os.getenv("PRIVATE_LLM_TEMPERATURE", "0.6")),
}

# 私有云 Rerank 模型配置
RERANK_CONFIG = {
    "API_BASE": os.getenv("PRIVATE_RERANK_API_BASE", "https://prod-api.faw.cn/JT/DA/DA-0505/RAG/DEFAULT/rerank"),
    "ACCESS_TOKEN": os.getenv("PRIVATE_RERANK_ACCESS_TOKEN", ""),
    "MODEL": os.getenv("PRIVATE_RERANK_MODEL", "bce-reranker-local"),
    "TOP_N": int(os.getenv("RERANK_TOP_N", "5")),
    "RETURN_DOCUMENTS": os.getenv("RERANK_RETURN_DOCUMENTS", "true").lower() == "true",
}