import requests
import json
from typing import List, Dict, Any, Optional
from .config import EMBEDDING_CONFIG
from utils.get_token import get_api_prod_gateway_token
from utils.logger import logger

class EmbeddingClient:
    def __init__(self):
        # Embedding配置
        self.embedding_api_base = EMBEDDING_CONFIG["API_BASE"]
        self.embedding_model = EMBEDDING_CONFIG["MODEL"]
        self.dimension = EMBEDDING_CONFIG["DIMENSION"]
        self.encoding_format = EMBEDDING_CONFIG["ENCODING_FORMAT"]
        
    def get_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量"""
        logger.debug(f"获取嵌入向量，输入文本: {text[:50]}...")  # 仅打印前50个字符
        access_token = get_api_prod_gateway_token()
        logger.debug(f"获取嵌入向量，使用的access_token: {access_token}")
        try:
            # 构建API URL，使用embedding专用的access_token
            url = f"{self.embedding_api_base}?access_token={access_token}"
            logger.debug(f"Embedding API URL: {url}")   
            # 构建请求数据
            payload = {
                "model": self.embedding_model,
                "input": text,
                "dimension": str(self.dimension),  # 确保维度作为字符串传递
                "encoding_format": self.encoding_format
            }
            
            # 设置请求头
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json"
            }
            
            # 发送POST请求
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            #logger.debug(f"Embedding API请求结果: {response.text}")
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                # 根据实际API响应结构提取嵌入向量
                if "data" in result and len(result["data"]) > 0:
                    return result["data"][0]["embedding"]
                return [0.0] * self.dimension  # 返回默认空向量，使用配置的维度
            else:
                print(f"嵌入API请求失败: {response.status_code}, {response.text}")
                return [0.0] * self.dimension  # 返回默认空向量，使用配置的维度
        except Exception as e:
            print(f"获取嵌入向量时出错: {e}")
            return [0.0] * self.dimension  # 返回默认空向量，使用配置的维度