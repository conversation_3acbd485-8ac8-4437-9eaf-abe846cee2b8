import requests
import json
from typing import List, Dict, Any, Optional
from .config import RERANK_CONFIG
from utils.get_token import get_api_prod_gateway_token
from utils.logger import logger

class RerankClient:
    def __init__(self):
        self.api_base = RERANK_CONFIG["API_BASE"]
        self.model = RERANK_CONFIG["MODEL"]
        self.top_n = RERANK_CONFIG["TOP_N"]
        self.return_documents = RERANK_CONFIG["RETURN_DOCUMENTS"]
        
    def rerank(self, query: str, documents: List[str]) -> List[Dict[str, Any]]:
        """对文档进行重排序"""
        access_token = get_api_prod_gateway_token()
        try:
            # 构建API URL
            url = f"{self.api_base}?access_token={access_token}"
            
            # 构建请求数据
            payload = {
                "model": self.model,
                "query": query,
                "documents": documents
            }
            
            # 设置请求头
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json"
            }
            
            # 发送POST请求
            logger.info(f"url: {url}")
            logger.info(f"payload: {payload}")
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            logger.info(f"response: {response.text}")
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                # 根据实际API响应结构提取结果
                if "results" in result :
                    return result["results"]
                return []
            else:
                print(f"Rerank API请求失败: {response.status_code}, {response.text}")
                return []
        except Exception as e:
            print(f"调用Rerank API时出错: {e}")
            return []