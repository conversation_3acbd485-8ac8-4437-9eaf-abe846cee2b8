import streamlit as st
import pandas as pd
import os
import tempfile
from .index_creator import IndexCreator
from .retriever import Retriever
from .llm_client import PrivateLLMClient
from utils.logger import logger

st.set_page_config(page_title="知识库检索系统", layout="wide")

def main():
    st.title("知识库检索系统")
    
    # 创建侧边栏
    st.sidebar.title("功能选择")
    page = st.sidebar.radio("选择功能", ["创建索引", "检索知识", "大模型对话"])
    
    if page == "创建索引":
        create_index_page()
    elif page == "检索知识":
        retrieval_page()
    else:
        llm_chat_page()

def create_index_page():
    st.header("创建知识库索引")
    st.write("上传Excel文件创建知识库索引。Excel文件必须包含以下列：problem_signature, raw_log, business_context, root_cause, solution")
    
    uploaded_file = st.file_uploader("选择Excel文件", type=["xlsx", "xls"])
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("创建索引"):
            if uploaded_file is not None:
                with st.spinner("正在创建索引..."):
                    # 保存上传的文件到临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        tmp_file_path = tmp_file.name
                    
                    try:
                        # 创建索引
                        index_creator = IndexCreator()
                        count = index_creator.process_excel(tmp_file_path)
                        st.success(f"成功创建索引！共处理 {count} 条记录。")
                    except Exception as e:
                        st.error(f"创建索引时出错: {str(e)}")
                    finally:
                        # 删除临时文件
                        os.unlink(tmp_file_path)
            else:
                st.warning("请先上传Excel文件")
    
    with col2:
        if st.button("清除所有索引"):
            with st.spinner("正在清除索引..."):
                try:
                    index_creator = IndexCreator()
                    success = index_creator.clear_index()
                    if success:
                        st.success("成功清除所有索引！")
                    else:
                        st.error("清除索引失败")
                except Exception as e:
                    st.error(f"清除索引时出错: {str(e)}")
    
    # 预览上传的Excel文件
    if uploaded_file is not None:
        st.subheader("Excel文件预览")
        df = pd.read_excel(uploaded_file)
        st.dataframe(df)

# 在 retrieval_page 函数中移除字段选择部分

def retrieval_page():
    st.header("知识库检索")
    
    # 查询输入
    query = st.text_area("输入查询内容", height=100)
    
    # 设置检索数量
    top_k = st.slider("检索结果数量", min_value=1, max_value=20, value=5)
    
    # 添加是否使用rerank的选项
    use_rerank = st.checkbox("使用Rerank重排序结果", value=False, help="使用Rerank模型对检索结果进行重新排序，提高相关性")
    
    if st.button("检索"):
        if query:
            with st.spinner("正在检索..."):
                try:
                    retriever = Retriever()
                    results = retriever.retrieve(query, top_k=top_k, use_rerank=use_rerank)
                    
                    if results:
                        st.subheader(f"检索结果 （前 {top_k} 条）")
                        for result in results: 
                            st.markdown(f"**问题签名**: {result['problem_signature']}")
                            st.markdown(f"**原始日志**: {result['raw_log']}")
                            st.markdown(f"**业务上下文**: {result['business_context']}")
                            st.markdown(f"**根本原因**: {result['root_cause']}")
                            st.markdown(f"**解决方案**: {result['solution']}")
                            st.markdown("---")

                    else:
                        st.info("未找到相关结果")
                except Exception as e:
                    st.error(f"检索时出错: {str(e)}")
        else:
            st.warning("请输入查询内容")

def llm_chat_page():
    st.header("大模型对话")
    
    # 初始化聊天历史
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # 显示聊天历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # 用户输入
    if prompt := st.chat_input("请输入您的问题"):
        # 添加用户消息到聊天历史
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # 获取大模型回复
        with st.chat_message("assistant"):
            # 保存用户输入的prompt到文件
            with open("用户输入_prompts.txt", "a", encoding="utf-8") as f:
                f.write(f"{prompt}\n")
            
            with st.spinner("思考中..."):
                llm_client = PrivateLLMClient()
                response = llm_client.get_completion(prompt)
                st.markdown(response)
                
        # 添加助手消息到聊天历史
        st.session_state.messages.append({"role": "assistant", "content": response})

if __name__ == "__main__":
    main()