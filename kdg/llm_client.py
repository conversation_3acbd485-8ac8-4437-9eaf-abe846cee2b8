import requests
import json
from typing import List, Dict, Any, Optional
from .config import PRIVATE_LLM_CONFIG
from utils.get_token import get_api_prod_gateway_token
from utils.logger import logger

class PrivateLLMClient:
    def __init__(self):
        # 大模型配置
        self.api_base = PRIVATE_LLM_CONFIG["API_BASE"]
        self.model = PRIVATE_LLM_CONFIG["MODEL"]
        self.temperature = PRIVATE_LLM_CONFIG["TEMPERATURE"]
        

        
    def get_completion(self, prompt: str) -> str:
        """获取大模型的回复"""
        access_token = get_api_prod_gateway_token()
        logger.info(f"access_token: {access_token}")
        try:
            # 构建API URL
            url = f"{self.api_base}?access_token={access_token}"
            
            # 构建请求数据
            payload = {
                "model": self.model,
                "messages": [{"role": "user", "content": prompt}],
                "stream": False,
                "temperature": self.temperature
            }
            
            # 设置请求头
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json"
            }
            
            # 发送POST请求
            logger.info(f"url: {url}")  
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            #logger.info(f"response: {response.text}")
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                # 根据实际API响应结构提取内容
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                return "无法获取有效回复"
            else:
                print(f"API请求失败: {response.status_code}, {response.text}")
                return f"API请求失败: {response.status_code}"
        except Exception as e:
            print(f"调用大模型API时出错: {e}")
            return f"调用大模型API时出错: {str(e)}"
    
