from clickhouse_connect import get_client
import numpy as np
from typing import List, Dict, Any, Optional
import uuid
import json

from utils.logger import logger
from .config import CLIC<PERSON>HOUSE_CONFIG, VECTOR_CONFIG

class VectorStore:
    def __init__(self):
        self.host = CLICKHOUSE_CONFIG["HOST"]
        logger.info(f"ClickHouse client created {self.host}.")
        self.port = CLICKHOUSE_CONFIG["PORT"]
        self.user = CLICKHOUSE_CONFIG["USER"]
        self.password = CLICKHOUSE_CONFIG["PASSWORD"]
        self.database = CLICKHOUSE_CONFIG["DATABASE"]
        self.table_name = VECTOR_CONFIG["TABLE_NAME"]
        self.vector_size = VECTOR_CONFIG["VECTOR_SIZE"]
        self.top_k = VECTOR_CONFIG["TOP_K"]
        self.client = self._get_client()
        self._ensure_table_exists()

    def _get_client(self):
        """
        Get a configured ClickHouse client with experimental object type support enabled.
        
        Returns:
            A ClickHouse client instance configured with host, port, username, and password.
            Enables experimental object type support before returning the client.
        """
        client = get_client(
            host=self.host,
            port=self.port,
            username=self.user,
            password=self.password,
            database=self.database,
        )
        client.command("SET allow_experimental_object_type=1")
        return client

    def drop_table(self):
        """删除向量表"""
        self.client.command(f"DROP TABLE IF EXISTS {self.table_name}")
        logger.info(f"Dropped table {self.table_name} if it existed.")

    def _ensure_table_exists(self):
        """确保向量表存在，如果不存在则创建"""
        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {self.table_name} (
            id String,
            problem_signature String,
            raw_log String,
            business_context String,
            root_cause String,
            solution String,
            content_vector Array(Float32),
            created_at DateTime DEFAULT now(),
            CONSTRAINT vec_len CHECK length(content_vector) = {self.vector_size},
            VECTOR INDEX content_vector_idx content_vector TYPE IVFFLAT('metric_type=cosine')
        )   ENGINE = MergeTree ORDER BY id SETTINGS index_granularity = 8192;
        """
        logger.info(f"Ensuring table exists with query: {create_table_query}")
        self.client.command(create_table_query)


    def add_documents(self, documents: List[Dict[str, Any]], vectors: List[List[float]]):
        """添加文档到向量存储"""
        logger.info(f"Adding {len(documents)} documents to vector store.")
        if len(documents) != len(vectors):
            logger.error("Documents and vectors length mismatch.")
            raise ValueError("Documents and vectors must have the same length.")    
        
        rows = []
        columns = [
            "id",
            "problem_signature",
            "raw_log",
            "business_context",
            "root_cause",
            "solution",
            "content_vector"
        ]
        #print(f"Preparing to add {documents} documents with vector size {vectors}.")
        # 检查每个文档是否是字典，并且包含必要的字段

        
        logger.info(f"Preparing to add {len(documents)} documents with vector size {self.vector_size}.")

        for i, doc in enumerate(documents):
            logger.debug(f"Processing document: {len(documents)} vector length: {len(vectors[i])}")
            doc_id = str(uuid.uuid4())
            row = (
                doc_id,
                doc.get("problem_signature", ""),
                doc.get("raw_log", ""),
                doc.get("business_context", ""),
                doc.get("root_cause", ""),
                doc.get("solution", ""),
                vectors[i]
            )
            logger.debug(f"Adding document with ID: {doc_id}, vector length: {len(vectors[i])}")
            rows.append(row)
            
        logger.info(f"Preparing to insert {len(rows)} documents into vector store.")
        if not rows:
            logger.warning("No documents to insert. Skipping insertion.")
            return 0    
        try:
            self.client.insert(table=self.table_name, data=rows,column_names=columns)
        except Exception as e:
            # 处理异常，例如记录日志或清理资源
            print(f"An error occurred: {e}")
            # 抛出异常，以便上层代码处理
            raise
        logger.info(f"Added {len(rows)} documents to vector store.")
        return len(rows)

    def similarity_search(
        self, query_vector: List[float], top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """基于向量相似度搜索文档
        SELECT photo_id, photo_image_url, distance(photo_embed, {target_image_embed}) as dist
            FROM default.myscale_photos
            WHERE photo_id != '{target_image_id}'
            ORDER BY dist
            LIMIT {top_k}
        """
        logger.info(f"Starting similarity search with vector of size {len(query_vector)} top_k: {top_k} .")


        if top_k is None:
            top_k = self.top_k

        query = f"""
        SELECT 
            id, 
            problem_signature,
            raw_log,
            business_context,
            root_cause,
            solution,
            distance(content_vector, {query_vector}) as dist
        FROM {self.table_name}
        ORDER BY dist ASC
        LIMIT {top_k}
        """
        logger.info(f"Executing similarity search with query: {query[:100]}...")
        results = self.client.query(query).named_results()
        #for row in results:
            #logger.debug(f"Retrieved row: {row}")
            
        return results

    def delete_all(self):
        """删除表中所有数据"""
        self.client.command(f"TRUNCATE TABLE {self.table_name}")

    def close(self):
        """关闭客户端连接"""
        if self.client:
            self.client.close()