"""
日志分析智能体 - 主程序

Streamlit开发测试界面，用于开发人员测试日志分析功能
"""

import json
import asyncio
from typing import Optional, Dict, Any
import streamlit as st

# 导入服务模块
from services.logs_analysis_service import process_log_analysis


# =============================================================================
# Streamlit 测试界面（仅供开发人员使用）
# =============================================================================

def init_streamlit():
    """初始化Streamlit界面配置"""
    st.set_page_config(
        page_title="调用链分析工具 - 开发测试版",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 添加侧边栏说明
    with st.sidebar:
        st.title("开发测试说明")
        st.markdown("""
        ⚠️ **此界面仅供开发人员测试使用**

        1. 输入调用链的时间
        2. 输入 Trace ID
        3. 输入认证 Token
        4. **选择日志类型**（新功能）
        5. 配置 ReAct 模式（推荐启用）
        6. 配置 API 参数（可选）
        7. 点击分析按钮开始分析

        **新功能 - 智能分析流程**：
        - 🎨 **前端问题**: resource_load_slow, access_use_slow, white_screen_time_slow
        - 🔧 **后端问题**: 其他所有类型
        - 📋 **自动路由**: 根据日志类型选择最适合的分析流程

        **模型说明**：
        - 当前使用 DeepSeek-R1 模型（RESTful API）
        - 自动重试机制，提高成功率
        - 支持超时设置和重试次数调整
        - 支持ReAct智能工具调用模式
        """)

        st.divider()
        st.markdown("### 🎨 前端分析流程")
        st.markdown("""
        **前端问题分析流程**：
        1. **日志过滤**: 提取问题节点
        2. **前端资源确认**: 识别并获取前端资源文件大小
        3. **前端问题分析**: 基于资源信息生成优化建议

        **支持的前端资源类型**：
        - 脚本文件: .js, .mjs, .ts, .jsx
        - 样式文件: .css, .less, .scss, .styl
        - 图片文件: .png, .jpg, .gif, .svg, .webp 等
        - 其他资源: .pdf, .json, .xml, .mp3, .mp4 等

        ⚠️ **注意**: 如果未找到前端资源，分析流程将自动终止
        """)

        st.divider()
        st.markdown("### 🧠 ReAct 模式")
        st.markdown("""
        **ReAct（推理-行动-观察）模式**：
        - 智能化的工具调用策略
        - 动态调整工具选择
        - 更高的分析质量
        - 透明的推理过程
        """)
        
        st.divider()
        st.markdown("### API 配置")
        st.markdown("""
        **默认配置**：
        - 超时时间：600 秒
        - 重试次数：1 次
        - 重试间隔：指数退避（1s, 2s, 4s）
        """)
        
        st.divider()
        st.markdown("### 故障排除")
        st.markdown("""
        **如果遇到 API 问题**：
        1. 检查网络连接
        2. 确认 API 服务状态
        3. 增加重试次数或超时时间
        4. 查看详细错误信息
        """)

    # 主页面标题
    st.title("🔍 调用链日志分析工具 - 开发测试版")
    st.markdown("**注意：此界面仅供开发人员测试使用，生产环境请调用 services.logs_analysis_service.process_log_analysis 异步接口**")


def run_streamlit_test_interface():
    """运行Streamlit测试界面"""
    init_streamlit()
    
    # 创建输入表单
    with st.form("trace_input_form"):
        col1, col2 = st.columns(2)
        with col1:
            time = st.text_input("时间", value="2025-07-11 18:16:53", 
                               help="输入调用链的时间")
            traceid = st.text_input("Trace ID", value="3ddaa01235a743049426dcc869ef6b5c.107.17522290138112717", 
                                  help="输入需要分析的 Trace ID")
        with col2:
            authorization = st.text_input(
                "认证 Token",
                value="Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************.Zp7gFpyOC1XTnETZ-_7jk6YiEU9A8kWxtRvtDW8S_vbm2NzDKo2RK2R4UrS3vpwvmi994ScUxEoedL50fO9Baw",
                type="password",
                help="输入认证 Token"
            )

        # 日志类型选择
        st.markdown("### 📋 日志类型选择")
        log_type_options = [
            ("自动检测", None),
            ("资源加载缓慢 (前端)", "resource_load_slow"),
            ("访问使用缓慢 (前端)", "access_use_slow"),
            ("白屏时间缓慢 (前端)", "white_screen_time_slow"),
            ("http慢请求", "http_req_slow"),
            ("异常40⬇️0x", "http_req_40_50")
        ]

        log_type_display = st.selectbox(
            "选择日志类型",
            options=[option[0] for option in log_type_options],
            index=0,
            help="选择日志类型以使用对应的分析流程。前端问题将使用前端分析流程，后端问题使用后端分析流程。"
        )

        # 获取实际的log_type值
        log_type = None
        for display, value in log_type_options:
            if display == log_type_display:
                log_type = value
                break

        # 显示分析流程说明
        if log_type in ["resource_load_slow", "access_use_slow", "white_screen_time_slow"]:
            st.info("🎨 **前端问题分析流程**: 日志过滤 → 前端资源确认 → 前端问题分析")
        elif log_type is None:
            st.info("🔍 **自动检测**: 系统将根据日志内容自动选择最适合的分析流程")
        else:
            st.info("🔧 **后端问题分析流程**: 日志过滤 → 错误位置分析 → 代码上下文 → 问题分析")
        
        # ReAct模式配置
        with st.expander("🧠 ReAct模式配置", expanded=True):
            use_react_mode = st.checkbox(
                "启用ReAct模式", 
                value=False,  # 默认启用
                help="启用ReAct（推理-行动-观察）模式，实现更智能的工具调用"
            )
            if use_react_mode:
                max_react_iterations = st.slider(
                    "最大迭代次数", 
                    min_value=1, max_value=10, value=5, 
                    help="ReAct模式的最大推理迭代次数"
                )
            else:
                max_react_iterations = 5
        
        # API 配置（高级选项）
        with st.expander("🔧 高级配置（可选）", expanded=False):
            col3, col4 = st.columns(2)
            with col3:
                api_timeout = st.number_input("API 超时时间（秒）", min_value=300, max_value=18000, value=7200, help="单次 API 调用的超时时间")
                max_retries = st.number_input("最大重试次数", min_value=1, max_value=5, value=2, help="API 失败时的重试次数")
            with col4:
                api_temperature = st.slider("模型温度", min_value=0.0, max_value=1.0, value=0.3, step=0.1, help="控制模型输出的随机性，0.0 最确定，1.0 最随机")
                show_debug = st.checkbox("显示调试信息", value=True, help="显示详细的 API 调用信息")
        
        submitted = st.form_submit_button("开始分析", use_container_width=True)

    if submitted:
        # 创建进度条和状态显示
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # 创建输出容器
        thinking_container = st.empty()
        answer_container = st.empty()
        log_container = st.empty()
        
        status_text.text("正在启动异步分析...")
        progress_bar.progress(10)
        
        # 执行异步分析流程
        try:
            # 在Streamlit中运行异步函数
            result = asyncio.run(process_log_analysis(
                time=time,
                traceid=traceid,
                authorization=authorization,
                api_timeout=api_timeout,
                max_retries=max_retries,
                api_temperature=api_temperature,
                show_debug=show_debug,
                use_react_mode=use_react_mode,      # 传递ReAct模式参数
                max_react_iterations=max_react_iterations,  # 传递最大迭代次数
                log_type=log_type  # 传递日志类型参数
            ))
            
            progress_bar.progress(100)
            status_text.text("✅ 分析完成！")
            
            if result["success"]:
                data = result["data"]

                # 检查是否为前端分析结果
                is_frontend_analysis = data.get("analysis_type") == "frontend"

                if is_frontend_analysis:
                    # 前端分析结果显示
                    st.success("🎨 前端问题分析完成")

                    # 显示前端资源信息
                    if "frontend_resources" in data:
                        with st.expander("📁 前端资源详情", expanded=True):
                            resources = data["frontend_resources"]
                            st.markdown(f"**找到 {len(resources)} 个前端资源：**")

                            for i, resource in enumerate(resources, 1):
                                filename = resource.get('filename', 'unknown')
                                size_bytes = resource.get('size_bytes', 0)
                                size_kb = resource.get('size_kb', 0)
                                size_mb = resource.get('size_mb', 0)
                                url = resource.get('url', '')

                                st.markdown(f"""
                                **{i}. {filename}**
                                - 大小: {size_bytes:,} bytes ({size_kb} KB / {size_mb} MB)
                                - URL: `{url}`
                                """)

                    # 显示前端分析报告
                    answer_container.markdown("### 🎨 前端问题分析报告")
                    answer_container.markdown(data["analysis_report"])

                else:
                    # 后端分析结果显示（原有逻辑）
                    st.success("🔧 后端问题分析完成")

                    # 显示错误位置分析结果
                    if "error_locations" in data and data["error_locations"]:
                        log_container.markdown("### 🔍 错误位置分析结果")
                        log_container.code(json.dumps(data["error_locations"], ensure_ascii=False, indent=2), language="json", wrap_lines=True)

                    # 显示工具调用结果
                    if "tool_results" in data and data["tool_results"]:
                        thinking_container.markdown("### 🛠️ 工具调用结果")
                        thinking_container.markdown(data["tool_results"])

                    # 显示后端分析报告
                    answer_container.markdown("### 🔧 后端问题分析报告")
                    answer_container.markdown(data["analysis_report"])

                # 显示生成的文件（通用）
                if "files_generated" in data and data["files_generated"]:
                    with st.expander("📁 生成的文件", expanded=False):
                        st.markdown("**本次分析生成的文件：**")
                        for file_path in data["files_generated"]:
                            st.markdown(f"- `{file_path}`")
                        
            else:
                st.error(f"分析失败: {result['error']}")
                
        except Exception as e:
            st.error(f"执行分析时出现错误: {str(e)}")
            progress_bar.progress(0)
            status_text.text("❌ 分析失败")


# =============================================================================
# 主程序入口
# =============================================================================

def main():
    """主程序入口"""
    print("==== 日志分析智能体启动 ====")
    print("模式：Streamlit 开发测试界面")
    print("核心接口: services.logs_analysis_service.process_log_analysis")
    print("🧠 已支持ReAct模式工具调用")
    
    # 运行Streamlit测试界面
    run_streamlit_test_interface()


if __name__ == "__main__":
    main() 