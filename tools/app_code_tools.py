import os
import re
import json
import requests
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path
from langchain_core.tools import tool
from models.params import AppCodeSearchParams
from utils.api_prod_gateway import ucg_config
from utils.get_token import get_api_prod_gateway_token
from utils.git_manager import AsyncGitManager
from utils.logger import logger
from dotenv import load_dotenv

# 添加AST模块路径到sys.path
ast_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ast')
if ast_path not in sys.path:
    sys.path.append(ast_path)

# 导入AST分析模块
try:
    from url_to_method_mapper import find_method_by_url
    from build_graph import analyze_call_chain
    from json_query_tools import find_node_and_get_subtree, print_node_details, print_subtree_summary
    AST_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"AST模块导入失败: {e}")
    AST_MODULES_AVAILABLE = False

# 加载环境变量
load_dotenv()


def query_git_info_by_app_code(app_code: str, env: str = "prod") -> dict:
    """
    根据服务编号查询Git仓库信息
    
    Args:
        app_code: 服务编号
        env: 环境信息 (daily/pre/prod)prod
        
    Returns:
        包含Git仓库信息的字典
    """
    try:
        logger.info(f"🔍 查询服务编号 {app_code} 的Git仓库信息...")
        logger.info(f"📍 环境: {env}")
        
        # 获取Git查询专用访问令牌
        try:
            token = get_api_prod_gateway_token()
            logger.info(f"✅ 成功获取Git查询访问令牌")
        except Exception as e:
            logger.info(f"❌ 获取Git查询访问令牌失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取Git查询访问令牌失败: {str(e)}",
                "app_code": app_code
            }
        
        # 构建API请求URL - 使用固定的Git查询接口路径
        git_query_url = "/JT/BA/BA-0208/009/DEFAULT/queryAppLastBuild"
        base_url = f"{ucg_config['host']}{git_query_url}"
        
        # 构建请求参数
        params = {
            "appCode": app_code,
            "env": env,
            "access_token": token
        }
        
        logger.info(f"📡 发送Git查询请求: {base_url}")
        logger.info(f"📋 请求参数: appCode={app_code}, env={env}")
        
        # 发送API请求
        response = requests.get(base_url, params=params, timeout=30)
        
        if response.status_code != 200:
            error_msg = f"Git查询API请求失败，状态码: {response.status_code}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code
            }
        
        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            error_msg = f"Git查询API响应解析失败: {str(e)}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code
            }
        
        # 检查API响应状态
        if not result.get("success", False):
            error_msg = result.get("message", "Git查询API返回失败状态")
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code
            }
        
        # 提取仓库信息
        repo_list = result.get("data", {}).get("list", [])
        
        # 转换为标准格式
        repositories = []
        for repo_data in repo_list:
            # 只包含有效的仓库（有repoUrl的）
            repo_url = repo_data.get("repoUrl")
            if repo_url:
                repositories.append({
                    "id": repo_data.get("id"),
                    "name": repo_data.get("applicationName"),
                    "url": repo_url,
                    "last_build_branch": repo_data.get("lastBuildBranch"),
                    "last_success_build_branch": repo_data.get("lastSuccessBuildBranch"),
                    "last_build_date": repo_data.get("lastBuildDate"),
                    "last_success_build_date": repo_data.get("lastSuccessBuildDate")
                })
        
        logger.info(f"✅ 成功查询到 {len(repositories)} 个有效Git仓库")
        
        return {
            "success": True,
            "app_code": app_code,
            "env": env,
            "repositories": repositories,
            "total_count": len(repositories)
        }
        
    except requests.exceptions.RequestException as e:
        error_msg = f"Git查询网络请求异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code
        }
    except Exception as e:
        error_msg = f"查询Git信息时发生异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code
        }


@tool(args_schema=AppCodeSearchParams)
def search_code_by_app_code(
    app_code: str,
    server_url: str,
    file_name: str = "", 
    class_name: str = "",
    method_name: str = "",
    context: dict = None
) -> dict:
    """根据服务编号拉取代码并使用AST方法调用链分析查找相关代码
    
    工作流程：
    1. 根据服务编号查询Git仓库信息
    2. 拉取所有相关代码到app_code目录
    3. 使用AST分析根据server_url获取方法调用链
    4. 可选：根据file_name、class_name、method_name进一步精确定位
    5. 提取并返回代码上下文
    
    Args:
        app_code: 服务编号，用于查询和定位对应的代码库（必需）
        server_url: 服务接口URL，如/api/v1/user/list，用于AST分析获取调用链（必需）
        file_name: 要进一步搜索的文件名（可选）
        class_name: 要进一步搜索的类名（可选）
        method_name: 要进一步搜索的方法名（可选）
        context: 上下文信息（可选）
    
    Returns:
        包含代码拉取和AST分析结果的字典，结构如下：
        {
            "success": bool,                    # 整体执行是否成功
            "app_code": str,                    # 输入的服务编号
            "server_url": str,                  # 输入的服务URL
            "error": str,                       # 错误信息（仅在失败时存在）
            "step": str,                        # 失败时的步骤（仅在失败时存在）
            
            # Git仓库信息（成功时包含）
            "git_info": {
                "success": bool,                # Git信息查询是否成功
                "app_code": str,                # 服务编号
                "env": str,                     # 环境（daily/pre/prod）
                "repositories": [...]           # 仓库列表
            },
            
            # 代码克隆结果（成功时包含）
            "clone_results": {
                "successful": [str],            # 成功克隆的仓库名称列表
                "failed": [...]                 # 失败的仓库信息列表
            },
            
            # AST分析结果（成功时包含）
            "ast_analysis": {
                "entry_method": str,            # 找到的入口方法全限定名
                "method_chain_file": str,       # 保存的方法调用链文件路径
                "call_chain_summary": {         # 调用链摘要信息
                    "total_methods": int,       # 总方法数
                    "max_depth": int,           # 最大调用深度
                    "packages": [str]           # 涉及的包列表
                },
                "method_chain_data": dict       # 完整的方法调用链数据
            },
            
            # 精确查找结果（如果提供了file_name等参数且匹配成功）
            "precise_search": {
                "success": bool,                # 精确查找是否成功
                "matched_nodes": [              # 匹配的节点列表
                    {
                        "method_key": str,      # 方法全限定名
                        "method_info": dict,    # 方法详细信息
                        "subtree_summary": dict # 子树统计摘要
                    }
                ],
                "search_criteria": {            # 搜索条件
                    "file_name": str,
                    "class_name": str,
                    "method_name": str
                }
            },
            
            # 代码上下文（成功时包含）
            "code_context": {
                "entry_method_info": str,       # 入口方法的代码
                "call_chain_text": str,         # 调用链的文本格式
                "precise_node_codes": [str]     # 精确查找到的节点代码（如果有）
            }
        }
        
        注意事项：
        1. server_url为必需参数，用于AST分析找到入口方法
        2. file_name、class_name、method_name为可选参数，用于进一步精确定位
        3. 失败时只包含 success=False、error、app_code、step 字段
        4. AST分析需要确保代码仓库已成功克隆到本地
    """
    
    logger.info(f"\n==== 开始服务编号 {app_code} 的代码拉取和AST分析流程 ====")
    
    # 参数验证
    if not server_url or not server_url.strip():
        error_msg = "server_url 是必需参数，不能为空"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code,
            "step": "parameter_validation"
        }
    
    # 检查AST模块可用性
    if not AST_MODULES_AVAILABLE:
        error_msg = "AST分析模块不可用，请检查ast目录下的相关文件"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code,
            "step": "ast_module_check"
        }
    
    # 设置Git工作空间路径
    git_workspace = os.getenv("GIT_WORKSPACE", "code/code_base")
    app_code_path = os.path.join(git_workspace, app_code)
    
    logger.info(f"🎯 服务编号: {app_code}")
    logger.info(f"🌐 服务URL: {server_url}")
    logger.info(f"📁 目标路径: {app_code_path}")
    logger.info(f"🔍 精确搜索条件:")
    logger.info(f"  - 文件名: {file_name if file_name else '未指定'}")
    logger.info(f"  - 类名: {class_name if class_name else '未指定'}")
    logger.info(f"  - 方法名: {method_name if method_name else '未指定'}")
    
    try:
        # 第一步：根据服务编号查询Git仓库信息
        logger.info(f"\n📡 第一步：查询服务编号 {app_code} 的Git仓库信息...")
        
        git_info = query_git_info_by_app_code(app_code, env=os.getenv("GIT_INFO_ENV", "prod"))
        
        if not git_info.get("success", False):
            error_msg = f"无法获取服务编号 {app_code} 的Git信息: {git_info.get('error', 'Unknown error')}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "server_url": server_url,
                "step": "git_info_query"
            }
        
        repositories = git_info.get("repositories", [])
        if not repositories:
            error_msg = f"服务编号 {app_code} 没有关联的Git仓库"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "server_url": server_url,
                "step": "git_info_query"
            }
        
        logger.info(f"✅ 找到 {len(repositories)} 个Git仓库")
        
        # 第二步：拉取所有相关代码到app_code目录
        logger.info(f"\n📥 第二步：拉取代码到目录 {app_code_path}...")
        
        # 确保目标目录存在
        os.makedirs(app_code_path, exist_ok=True)
        
        # 初始化异步Git管理器
        git_manager = AsyncGitManager(workspace_dir=git_workspace)
        
        # 构建克隆任务列表
        clone_tasks = []
        for repo in repositories:
            repo_name = repo.get("name", "")
            repo_url = repo.get("url", "")
            branch = repo.get("last_success_build_branch") or repo.get("last_build_branch") or "master"
            
            if repo_name and repo_url:
                clone_tasks.append({
                    "repo_name": repo_name,
                    "repo_url": repo_url,
                    "target_dir": os.path.join(app_code_path, repo_name),
                    "branch": branch
                })
        
        if not clone_tasks:
            error_msg = f"没有有效的Git仓库可以克隆"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "server_url": server_url,
                "step": "git_clone"
            }
        
        # 执行并发克隆
        clone_results = git_manager.clone_repositories_batch(clone_tasks)
        
        # 检查克隆结果
        successful_clones = []
        failed_clones = []
        
        for task, result in zip(clone_tasks, clone_results):
            if result.get("success", False):
                successful_clones.append(task["repo_name"])
                logger.info(f"✅ 成功克隆: {task['repo_name']}")
            else:
                failed_clones.append({
                    "repo_name": task["repo_name"],
                    "error": result.get("error", "Unknown error")
                })
                logger.error(f"❌ 克隆失败: {task['repo_name']} - {result.get('error', 'Unknown error')}")
        
        if not successful_clones:
            error_msg = f"所有仓库克隆都失败了"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "server_url": server_url,
                "step": "git_clone",
                "clone_results": {
                    "successful": successful_clones,
                    "failed": failed_clones
                }
            }
        
        logger.info(f"🎉 代码拉取完成: {len(successful_clones)} 个仓库成功，{len(failed_clones)} 个失败")
        
        # 第三步：使用AST分析获取方法调用链
        logger.info(f"\n🔍 第三步：使用AST分析获取方法调用链...")
        
        ast_analysis_result = _execute_ast_analysis(app_code_path, server_url)
        
        if not ast_analysis_result["success"]:
            return {
                "success": False,
                "error": ast_analysis_result["error"],
                "app_code": app_code,
                "server_url": server_url,
                "step": "ast_analysis",
                "git_info": git_info,
                "clone_results": {
                    "successful": successful_clones,
                    "failed": failed_clones
                }
            }
        
        # 第四步：可选的精确搜索
        precise_search_result = None
        if any([file_name, class_name, method_name]):
            logger.info(f"\n🎯 第四步：执行精确搜索...")
            precise_search_result = _execute_precise_search(
                ast_analysis_result["method_chain_data"],
                file_name, class_name, method_name
            )
        
        # 第五步：构建代码上下文
        logger.info(f"\n📄 第五步：构建代码上下文...")
        code_context = _build_ast_code_context(
            ast_analysis_result, precise_search_result, app_code_path
        )
        
        # 构建最终结果
        result = {
            "success": True,
            "app_code": app_code,
            "server_url": server_url,
            "git_info": git_info,
            "clone_results": {
                "successful": successful_clones,
                "failed": failed_clones
            },
            "ast_analysis": ast_analysis_result,
            "code_context": code_context
        }
        
        # 添加精确搜索结果（如果有）
        if precise_search_result:
            result["precise_search"] = precise_search_result
        
        logger.info(f"✅ 服务编号 {app_code} 的完整AST分析流程执行成功")
        logger.info("==== 代码拉取和AST分析流程完成 ====\n")
        
        return result
        
    except Exception as e:
        error_msg = f"执行过程中发生异常: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code,
            "server_url": server_url,
            "step": "execution_error"
        }


 


def _execute_ast_analysis(app_code_path: str, server_url: str) -> dict:
    """执行AST方法调用链分析
    
    Args:
        app_code_path: 应用代码路径
        server_url: 服务URL
        
    Returns:
        AST分析结果字典
    """
    try:
        logger.info(f"🔍 开始AST分析，URL: {server_url}")
        logger.info(f"📁 代码路径: {app_code_path}")
        
        # 检查代码路径是否存在
        if not os.path.exists(app_code_path):
            return {
                "success": False,
                "error": f"代码路径不存在: {app_code_path}"
            }
        
        # 查找所有可能的仓库目录
        repo_dirs = [d for d in os.listdir(app_code_path) 
                    if os.path.isdir(os.path.join(app_code_path, d)) and not d.startswith('.')]

        if not repo_dirs:
            return {
                "success": False,
                "error": f"在 {app_code_path} 中未找到任何仓库目录"
            }
        
        logger.info(f"📋 发现仓库目录: {', '.join(repo_dirs)}")
        
        # 尝试在每个仓库中查找入口方法
        entry_method = None
        found_repo = None
        
        for repo_dir in repo_dirs:
            repo_path = os.path.join(app_code_path, repo_dir)
            logger.info(f"🔍 在仓库 {repo_dir} 中查找URL对应的方法...")
            
            try:
                # 使用AST方法查找入口方法
                found_method = find_method_by_url(server_url, repo_path, verbose=False)
                if found_method:
                    entry_method = found_method
                    found_repo = repo_dir
                    logger.info(f"✅ 在仓库 {repo_dir} 中找到入口方法: {entry_method}")
                    break
                else:
                    logger.info(f"❌ 在仓库 {repo_dir} 中未找到匹配的方法")
            except Exception as e:
                logger.warning(f"⚠️ 在仓库 {repo_dir} 中查找时出错: {str(e)}")
                continue
        
        if not entry_method:
            return {
                "success": False,
                "error": f"在所有仓库中都未找到URL '{server_url}' 对应的方法"
            }
        
        # 分析方法调用链
        logger.info(f"🔗 开始分析方法调用链...")
        repo_path = os.path.join(app_code_path, found_repo)
        
        try:
            method_chain_data = analyze_call_chain(entry_method, repo_path, verbose=False)
            
            if not method_chain_data:
                return {
                    "success": False,
                    "error": f"方法调用链分析失败，未获得任何数据"
                }
            
            # 分析调用链摘要信息
            call_chain_summary = _analyze_method_chain_summary(method_chain_data)
            
            # 生成保存文件路径（模拟build_graph.py的保存逻辑）
            repo_name = os.path.basename(repo_path)
            clean_entry_method = re.sub(r'[<>:"/\\|?*]', '_', entry_method)
            method_chain_file = f"ast/{repo_name}/{clean_entry_method}.json"
            
            logger.info(f"✅ 方法调用链分析完成")
            logger.info(f"📊 摘要: {call_chain_summary['total_methods']} 个方法，最大深度 {call_chain_summary['max_depth']}")
            
            return {
                "success": True,
                "entry_method": entry_method,
                "found_repo": found_repo,
                "method_chain_file": method_chain_file,
                "call_chain_summary": call_chain_summary,
                "method_chain_data": method_chain_data
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"方法调用链分析失败: {str(e)}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"AST分析执行异常: {str(e)}"
        }


def _execute_precise_search(method_chain_data: dict, file_name: str, class_name: str, method_name: str) -> dict:
    """执行精确搜索
    
    Args:
        method_chain_data: 方法调用链数据
        file_name: 文件名
        class_name: 类名  
        method_name: 方法名
        
    Returns:
        精确搜索结果字典
    """
    try:
        logger.info(f"🎯 开始精确搜索...")
        logger.info(f"   文件名: {file_name if file_name else '未指定'}")
        logger.info(f"   类名: {class_name if class_name else '未指定'}")
        logger.info(f"   方法名: {method_name if method_name else '未指定'}")
        
        # 使用find_node_and_get_subtree进行精确搜索
        search_results = find_node_and_get_subtree(
            method_chain_data,
            class_name=class_name if class_name else None,
            method_name=method_name if method_name else None,
            file_name=file_name if file_name else None,
            exact_match=False  # 使用模糊匹配以提高找到概率
        )
        
        if not search_results:
            logger.info(f"❌ 精确搜索未找到匹配的节点")
            return {
                "success": False,
                "error": "未找到匹配的节点",
                "search_criteria": {
                    "file_name": file_name,
                    "class_name": class_name,
                    "method_name": method_name
                }
            }
        
        # 处理搜索结果
        matched_nodes = []
        for result in search_results:
            # 获取子树摘要
            subtree_summary = {
                "total_nodes": result['subtree_full']['total_nodes'],
                "max_depth": result['subtree_full']['max_depth'],
                "leaf_count": len(result['subtree_full']['leaf_nodes']),
                "all_methods_count": len(result['subtree_full']['all_methods'])
            }
            
            matched_nodes.append({
                "method_key": result['method_key'],
                "method_info": result['method_info'],
                "subtree_summary": subtree_summary,
                "path": result['path']
            })
        
        logger.info(f"✅ 精确搜索完成，找到 {len(matched_nodes)} 个匹配节点")
        
        return {
            "success": True,
            "matched_nodes": matched_nodes,
            "search_criteria": {
                "file_name": file_name,
                "class_name": class_name,
                "method_name": method_name
            }
        }
                                    
    except Exception as e:
        logger.error(f"❌ 精确搜索失败: {str(e)}")
        return {
            "success": False,
            "error": f"精确搜索失败: {str(e)}",
            "search_criteria": {
                "file_name": file_name,
                "class_name": class_name,
                "method_name": method_name
            }
        }


def _analyze_method_chain_summary(method_chain_data: dict) -> dict:
    """分析方法调用链摘要信息
    
    Args:
        method_chain_data: 方法调用链数据
        
    Returns:
        摘要信息字典
    """
    def count_methods_recursive(chain_dict: dict) -> int:
        """递归计算方法总数"""
        count = 0
        for method_key, method_info in chain_dict.items():
            count += 1
            if 'calls' in method_info and method_info['calls']:
                count += count_methods_recursive(method_info['calls'])
        return count
    
    def get_max_depth_recursive(chain_dict: dict, current_depth: int = 1) -> int:
        """递归计算最大调用深度"""
        max_depth = current_depth
        for method_key, method_info in chain_dict.items():
            if 'calls' in method_info and method_info['calls']:
                depth = get_max_depth_recursive(method_info['calls'], current_depth + 1)
                max_depth = max(max_depth, depth)
        return max_depth
    
    def collect_packages_recursive(chain_dict: dict, packages: set):
        """递归收集所有涉及的包"""
        for method_key, method_info in chain_dict.items():
            package = method_info.get('package', '')
            if package:
                packages.add(package)
            if 'calls' in method_info and method_info['calls']:
                collect_packages_recursive(method_info['calls'], packages)
    
    # 提取实际的调用链数据
    actual_chain_data = method_chain_data
    if isinstance(method_chain_data, dict) and len(method_chain_data) == 1:
        # 如果是单个入口方法的格式，提取实际的链数据
        first_key = list(method_chain_data.keys())[0]
        actual_chain_data = {first_key: method_chain_data[first_key]}
    
    # 计算摘要信息
    total_methods = count_methods_recursive(actual_chain_data)
    max_depth = get_max_depth_recursive(actual_chain_data)
    
    packages = set()
    collect_packages_recursive(actual_chain_data, packages)
    
    return {
        "total_methods": total_methods,
        "max_depth": max_depth,
        "packages": sorted(list(packages))
    }


def _build_ast_code_context(ast_analysis_result: dict, precise_search_result: dict, app_code_path: str) -> dict:
    """构建AST分析的代码上下文
    
    Args:
        ast_analysis_result: AST分析结果
        precise_search_result: 精确搜索结果
        app_code_path: 应用代码路径
        
    Returns:
        代码上下文字典
    """
    code_context = {}
    
    try:
        # 1. 添加入口方法信息
        entry_method = ast_analysis_result.get("entry_method", "")
        if entry_method:
            code_context["entry_method_info"] = f"""# AST分析结果

## 入口方法
- **方法全限定名**: {entry_method}
- **发现仓库**: {ast_analysis_result.get('found_repo', 'Unknown')}
- **保存路径**: {ast_analysis_result.get('method_chain_file', 'Unknown')}

## 调用链摘要
- **总方法数**: {ast_analysis_result.get('call_chain_summary', {}).get('total_methods', 0)}
- **最大深度**: {ast_analysis_result.get('call_chain_summary', {}).get('max_depth', 0)}
- **涉及包数**: {len(ast_analysis_result.get('call_chain_summary', {}).get('packages', []))}

## 涉及的包
{chr(10).join('- ' + pkg for pkg in ast_analysis_result.get('call_chain_summary', {}).get('packages', []))}
"""
        
        # 2. 添加方法调用链的文本形式
        method_chain_data = ast_analysis_result.get("method_chain_data", {})
        if method_chain_data:
            call_chain_text = _format_method_chain_text(method_chain_data)
            code_context["call_chain_text"] = call_chain_text
        
        # 3. 添加精确搜索结果（如果有）
        if precise_search_result and precise_search_result.get("success", False):
            matched_nodes = precise_search_result.get("matched_nodes", [])
            for i, node in enumerate(matched_nodes, 1):
                method_key = node.get("method_key", "")
                method_info = node.get("method_info", {})
                subtree_summary = node.get("subtree_summary", {})
                
                precise_context = f"""# 精确搜索结果 {i}

## 匹配节点信息
- **方法**: {method_key}
- **类**: {method_info.get('class', 'Unknown')}
- **包**: {method_info.get('package', 'Unknown')}
- **文件**: {method_info.get('file_path', 'Unknown')}
- **调用路径**: {' -> '.join(node.get('path', []))}

## 子树统计
- **子节点数**: {subtree_summary.get('total_nodes', 0)}
- **最大深度**: {subtree_summary.get('max_depth', 0)}
- **叶子节点数**: {subtree_summary.get('leaf_count', 0)}

## 方法代码
```java
{method_info.get('code', '// 代码不可用')}
```
"""
                code_context[f"precise_search_result_{i}"] = precise_context
        
        logger.info(f"📄 构建代码上下文完成，包含 {len(code_context)} 个条目")
        
        return code_context
                
    except Exception as e:
        logger.error(f"❌ 构建代码上下文失败: {str(e)}")
        return {
            "error": f"构建代码上下文失败: {str(e)}"
        }

from tools.filter_code import filter_method

def _format_method_chain_text(method_chain_data: dict, indent: int = 0) -> str:
    """格式化方法调用链为文本形式
    
    Args:
        method_chain_data: 方法调用链数据
        indent: 缩进级别
        
    Returns:
        格式化的文本
    """
    lines = [] #有性能问题代码变量；
    filter_lines = [] #没有性能问题代码变量；
    hit=['custom']
    prefix = "  " * indent
    
    for method_key, method_info in method_chain_data.items():
        # 添加代码（完整）
        code = method_info.get('code', '')
        if code:
            lines.append(f"{prefix}📍 {method_key}")
            lines.append(f"{prefix}   包: {method_info.get('package', 'Unknown')}")
            lines.append(f"{prefix}   类: {method_info.get('class', 'Unknown')}")
            lines.append(f"{prefix}   文件: {method_info.get('file_path', 'Unknown')}")
            code,hit = filter_method(code)
            logger.info(f"filter_method code:{code}, hit:{hit}")
            if not hit:
                lines.append(f"{prefix}   此链路的代码不是缓慢代码，已经过滤，请忽略。")
                # 不命中规则，不添加代码，直接跳过
                filter_lines.append("\n-------------------------------------------------------------------------\n")
                filter_lines.append(f"{prefix}📍 {method_key}")
                filter_lines.append(f"{prefix}   包: {method_info.get('package', 'Unknown')}")
                filter_lines.append(f"{prefix}   类: {method_info.get('class', 'Unknown')}")
                filter_lines.append(f"{prefix}   文件: {method_info.get('file_path', 'Unknown')}")
                filter_lines.append(f"{prefix}   过滤代码:")
                filter_code_lines = code.split('\n')
                for line_code in filter_code_lines:
                    filter_lines.append(f"{prefix}     {line_code}")
                    filter_code_lines = code.split('\n')
                #写到一个文件里， 文件名是方法名， 文件内容是代码
            else:
                # 命中规则，添加代码，给大模型
                lines.append(f"{prefix}  命中规则: {hit}, 代码:")
                code_lines = code.split('\n')
                for line in code_lines:
                    lines.append(f"{prefix}     {line}")
        
        # 递归处理子调用
        calls = method_info.get('calls', {})
        if calls:
            lines.append(f"{prefix}   └─ 调用:")
            sub_text = _format_method_chain_text(calls, indent + 2)
            lines.append(sub_text)
        else:
           logger.info(filter_lines)
        lines.append("")  # 空行分隔
    
    return "\n".join(lines) 



