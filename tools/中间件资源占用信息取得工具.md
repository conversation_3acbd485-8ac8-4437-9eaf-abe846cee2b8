整体需求：参考下面的步骤方法，实现取得数据库，rabbitmq,redis,kafka等中间件信息占用情报的工具函数
整体入参：
    ◦ traceid (必填): （参考其他工具，从context中获取）
    ◦ middleware (必填): 中间件名称（如Redis，kafka，RabbitMq，Mysql，人大金仓）
    ◦ startTime (必填)：开始时间（如：2025-06-13 15:05:00）
    ◦ endTime (必填)：结束时间（如：2025-06-13 15:06:03）
输出为1.该中间件对应时段的cpu，内存的平均占用情况。2输出指定中间件的特定资源占用信息
在tools/路径下 创建新的文件

1.根据trace_id和时间获取链路对应数据库和中间件信息
• 接口路径
    ◦ prod：https://prod-api.faw.cn/JT/BA/BA-0214/WOI/analysisData/getDataBaseAndMiddlewareInfoByTraceId
• 请求方法: GET
• 请求参数:
    ◦ time (必填): 链路时间
    ◦ traceId (必填): 链路ID
• 调用示例
    ◦ 入参
    https://prod-api.faw.cn/JT/BA/BA-0214/WOI/analysisData/getDataBaseAndMiddlewareInfoByTraceId?traceId=0db2ac37a4394bb898a2accb724777d8.83.17497980724831609&time=2025-06-13 15:05:00
    
出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "port": "33321",
            "peer": "************:33321",
            "appId": "BA-0208_MSA_GRE",
            "spanSonLayer": "KingBase",
            "domainName": "************",
            "dbName": "db0321",
            "spanLayer": "Database",
            "clusterFlag": "xc-arm"
        }
    ],
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WOIN50200"
}

2.数据库cpu，内存等资源信息查询接口（当middleware为Mysql，人大金仓时需取得）
• 接口路径
    ◦ prod：https://prod-api.faw.cn/JT/DA/DA-0304/001/DEFAULT/getDBMonitorList
• 请求方法: POST
• 请求参数:
    ◦ ip (必填): ip地址（从上面功能接口1出获取）
    ◦ port (必填): 端口（从上面功能接口1出获取）
    ◦ startTime (必填): 开始时间（需转换成”1749536107“的形式）
    ◦ endTime (必填): 结束时间（需转换成”1749539707“的形式）
    ◦ intervalName (必填): （默认为MINUTE）
    ◦ intervalValue (必填): （根据startTime和endTime的间隔，最小为1（MINUTE））
• 调用示例
    ◦ 入参
https://prod-api.faw.cn/JT/DA/DA-0304/001/DEFAULT/getDBMonitorList
{
    "ip":"*************",
    "port": "3306",
    "startTime": "1749536107",
    "endTime": "1749539707",
    "intervalName": "MINUTE",
    "intervalValue": "10"
}    ◦ 出参
{
    "code": 200,
    "aaData": {
        "disk": [],
        "mem": [],
        "qps": [],
        "cpu": [],
        "deadLock": [],
        "connections": [],
        "maxConnections": "0"
    }
}

3.获取其他中间件CPU、内存资源使用情况接口（当middleware为Redis，kafka，RabbitMq时需取得）
• 接口路径
    ◦ prod：https://prod-api.faw.cn/JT/BA/BA-0214/WOI/analysisData/getMiddlewarePodResourceUsage
• 请求方法: POST
• 请求参数:
    ◦ middlewareInfo (必填): 中间件信息（来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“peer”字段）
    ◦ clusterFlag (必填): （来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“clusterFlag”字段）
    ◦ startTime (必填)：开始时间（需转换成”1749536107“的形式）
    ◦ endTime (必填)：结束时间（需转换成”1749539707“的形式）

    ◦ 出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "memoryUsagePrometheusValues": [
                {
                    "value": "41.30",
                    "timestamp": "1750636800"
                },
                {
                    "value": "41.34",
                    "timestamp": "1750636836"
                },
                {
                    "value": "41.34",
                    "timestamp": "1750636872"
                }
            ],
            "podName": "sit-fc2mw-yysjcfwqx-kafka-broker-2",
            "cpuUsagePrometheusValues": [
                {
                    "value": "8.14",
                    "timestamp": "1750636800"
                },
                {
                    "value": "8.24",
                    "timestamp": "1750636836"
                },
                {
                    "value": "8.24",
                    "timestamp": "1750636872"
                }
            ]
        },
        {
            "memoryUsagePrometheusValues": [
                {
                    "value": "63.69",
                    "timestamp": "1750636800"
                },
                {
                    "value": "63.70",
                    "timestamp": "1750636836"
                },
                {
                    "value": "63.69",
                    "timestamp": "1750636872"
                }
            ]
        },
        {
            "memoryUsagePrometheusValues": [
                {
                    "value": "39.96",
                    "timestamp": "1750636800"
                },
                {
                    "value": "39.96",
                    "timestamp": "1750636836"
                },
                {
                    "value": "39.96",
                    "timestamp": "1750636872"
                }
            ]
        }
    ],
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WCSN50200"
}

4.1 获取 rabbitmq 资源使用情况（当middleware为RabbitMq时需取得）
• 接口路径
    ◦ prod：https://prod-api.faw.cn/JT/BA/BA-0214/WOI/analysisData/getPodResourceUsageRabbitMQ
• 请求方法: POST
• 请求参数:
    ◦ middlewareInfo (必填): 中间件信息（来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“peer”字段）
    ◦ clusterFlag (必填): （来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“clusterFlag”字段）
    ◦ startTime (必填)：开始时间（需转换成”1749536107“的形式）
    ◦ endTime (必填)：结束时间（需转换成”1749539707“的形式）
    ◦ 出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [{
        "podName": "sit-fc2mw-yqygztxjxm-fcwbggl-rabbitmq-0",
        "activeChannelsPrometheusValues": [],
        "unackedMessageCountPrometheusValues": [],
        "queueMessageCountPrometheusValues": [],
        "diskSpaceAlertStatusPrometheusValues": [],
        "consumerUtilizationPrometheusValues": [],
        "activeConnectionsPrometheusValues": [],
        "erlangProcessUsedMemoryBytesPrometheusValues": [],
        "globalDeliveredMessageCountPrometheusValues": [],
        "diskSpaceAvailableBytesPrometheusValues": [],
        "erlangSchedulerTaskQueueLengthPrometheusValues": [],
        "memoryAlertStatusPrometheusValues": []
    }],
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WCSN50200"
}

4.2 获取 redis 资源使用情况（当middleware为Redis时需取得）
• 接口路径
    ◦ prod：https://prod-api.faw.cn/JT/BA/BA-0214/WOI/analysisData/getPodResourceUsageRedis
• 请求方法: POST
• 请求参数:
    ◦ middlewareInfo (必填): 中间件信息（来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“peer”字段）
    ◦ clusterFlag (必填): （来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“clusterFlag”字段）
    ◦ startTime (必填)：开始时间（需转换成”1749536107“的形式）
    ◦ endTime (必填)：结束时间（需转换成”1749539707“的形式）
    ◦ 出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "cpuUserUsagePrometheusValues": [],
            "isAvailablePrometheusValues": [],
            "podName": "sit-fc2mw-hqzl3plus-redis-master-0",
            "connectionsRejectedPrometheusValues": [],
            "cpuSystemUsagePrometheusValues": [],
            "clusterSlotsOKPrometheusValues": []
        }
    ],
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WCSN50200"
}

4.3 获取 kafka 资源使用情况（当middleware为kafka时需取得）
• 接口路径
    ◦ prod：https://prod-api.faw.cn/JT/BA/BA-0214/WOI/analysisData/getPodResourceUsageKafka
• 请求方法: POST
• 请求参数:
    ◦ middlewareInfo (必填): 中间件信息（来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“peer”字段）
    ◦ clusterFlag (必填): （来源：上面接口1（根据trace_id和时间获取链路对应数据库和中间件信息接口）的输出中的“clusterFlag”字段）
    ◦ startTime (必填)：开始时间（需转换成”1749536107“的形式）
    ◦ endTime (必填)：结束时间（需转换成”1749539707“的形式）
    ◦ 出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [{
        "podName": "sit-fc2mw-hqzl30plus1-kafka-broker-0",
        "kafkaActiveBrokersCountPrometheusValues": [],
        "kafkaGroupLagPrometheusValues": []
    }],
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WCSN50200"
}