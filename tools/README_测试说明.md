# search_code_by_app_code 工具测试说明

## 🎯 重构完成总结

我们已经成功完成了 `search_code_by_app_code` 工具的重构，主要改动包括：

### ✅ 完成的工作

1. **完全重构搜索策略**
   - ❌ 移除了传统的文本搜索（SQL片段、关键字搜索）
   - ✅ 使用 AST 分析方法调用链分析
   - ✅ 集成 `ast/build_graph.py` 的 `find_method_by_url` 和 `analyze_call_chain`
   - ✅ 集成 `ast/json_query_tools.py` 的 `find_node_and_get_subtree`

2. **参数更新**
   - ✅ `app_code` (必需) - 服务编号
   - ✅ `server_url` (必需) - 服务接口URL
   - ✅ `file_name` (可选) - 文件名
   - ✅ `class_name` (可选) - 类名
   - ✅ `method_name` (可选) - 方法名
   - ✅ `context` (可选) - 上下文信息

3. **代码文件更新**
   - ✅ `tools/app_code_tools.py` - 主要工具实现
   - ✅ `models/params.py` - 参数模式更新
   - ✅ `tools/code_tools.py` - 结果处理更新

4. **测试脚本创建**
   - ✅ `test_app_code_tools.py` - 完整功能测试
   - ✅ `test_app_code_tools_simple.py` - 简化测试
   - ✅ `test_app_code_tools_langchain.py` - LangChain工具测试
   - ✅ `app_code_tools_demo.py` - 使用示例

## 🔧 环境依赖

为了正常运行和测试工具，需要以下依赖：

### Python包依赖
```bash
pip install requests
pip install langchain-core
pip install pydantic
pip install python-dotenv
```

### 项目模块依赖
```
- ast/url_to_method_mapper.py
- ast/build_graph.py  
- ast/json_query_tools.py
- utils/logger.py
- utils/api_prod_gateway.py
- utils/get_token.py
- utils/git_manager.py
```

## 🚀 测试指南

### 1. 基础功能测试

在有完整依赖的环境中运行：

```bash
cd tools/
python test_app_code_tools_langchain.py
```

### 2. 使用示例

```python
from tools.app_code_tools import search_code_by_app_code

# 基本用法
result = search_code_by_app_code.invoke({
    "app_code": "your-service-code",
    "server_url": "/api/v1/your/endpoint"
})

# 带精确搜索
result = search_code_by_app_code.invoke({
    "app_code": "your-service-code",
    "server_url": "/api/v1/your/endpoint",
    "class_name": "YourController",
    "method_name": "yourMethod",
    "file_name": "YourController.java"
})

# 结果处理
if result.get("success", False):
    # 成功时的处理
    ast_analysis = result["ast_analysis"]
    entry_method = ast_analysis["entry_method"]
    call_chain_summary = ast_analysis["call_chain_summary"]
    code_context = result["code_context"]
else:
    # 失败时的处理
    error = result.get("error", "")
    step = result.get("step", "")
```

### 3. 集成使用（通过ToolCaller）

```python
from tools.code_tools import extract_tool_results

# 模拟ToolCaller格式
tool_caller_result = {
    "tool_results": [
        {
            "tool": "search_code_by_app_code",
            "status": "success",
            "arguments": {
                "app_code": "example-service",
                "server_url": "/api/v1/test"
            },
            "result": result  # 上面的result
        }
    ]
}

# 格式化显示
formatted_output = extract_tool_results(tool_caller_result)
print(formatted_output)
```

## 📋 测试用例说明

### 1. LangChain工具结构测试
- 验证工具是否正确继承 `BaseTool`
- 检查工具属性（name, description, args_schema）
- 确认参数模式定义

### 2. invoke方法测试
- 有效参数调用
- 无效参数拒绝
- 参数验证逻辑
- 返回结果格式

### 3. 参数验证测试
- 必需参数检查（app_code, server_url）
- 可选参数处理（file_name, class_name, method_name）
- 空值和格式验证

### 4. 集成功能测试
- 基本AST分析流程
- 精确搜索功能
- 错误处理机制
- 结果结构验证

### 5. 边缘情况测试
- 特殊字符处理
- 超长输入处理
- 网络异常处理
- 依赖缺失处理

## 🎯 预期测试结果

在有完整依赖的环境中，预期测试结果：

### 成功场景
```json
{
  "success": true,
  "app_code": "test-service",
  "server_url": "/api/test",
  "ast_analysis": {
    "entry_method": "com.example.Controller.testMethod",
    "call_chain_summary": {
      "total_methods": 15,
      "max_depth": 5,
      "packages": ["com.example.controller", "com.example.service"]
    }
  },
  "code_context": {
    "entry_method_info": "...",
    "call_chain_text": "..."
  }
}
```

### 失败场景
```json
{
  "success": false,
  "app_code": "test-service",
  "server_url": "/api/test",
  "error": "具体错误信息",
  "step": "git_info_query|git_clone|ast_analysis|parameter_validation"
}
```

## 🔍 故障排查

### 常见问题

1. **导入失败：No module named 'requests'**
   - 解决：`pip install requests`

2. **AST模块不可用**
   - 检查 `ast/` 目录下的文件是否存在
   - 确认模块导入路径正确

3. **参数验证失败**
   - 确认 `app_code` 和 `server_url` 都已提供
   - 检查参数类型和格式

4. **Git仓库查询失败**
   - 检查网络连接
   - 验证API访问令牌
   - 确认服务编号有效性

5. **AST分析失败**
   - 确认代码仓库已成功克隆
   - 检查URL格式是否正确
   - 验证代码中是否存在对应的接口定义

## 📚 使用建议

1. **基本用法**：只需要提供 `app_code` 和 `server_url`
2. **精确搜索**：额外提供 `class_name`、`method_name`、`file_name` 进行精确定位
3. **结果处理**：使用 `extract_tool_results` 格式化显示结果
4. **错误处理**：检查 `result['success']` 判断执行是否成功

## 🎉 总结

重构后的 `search_code_by_app_code` 工具现在具备了：

- **更高的精确度**：使用AST分析替代文本搜索
- **更强的功能**：完整的方法调用链分析
- **更好的易用性**：清晰的参数结构和错误提示
- **更强的扩展性**：模块化设计，易于维护和扩展

工具已准备就绪，可以在有完整依赖的环境中进行实际测试和使用！ 