利用以下功能接口，开发根据服务编号获取服务k8s的POD 的CPU、内存、网络、磁盘等监控数据的工具函数
请简化返回的情报只需返回关键维度的平均监控数据即可
在tools/路径下 创建新的文件
• 接口路径
    ◦ sit：https://test-api.faw.cn:30443/JT/BA/BA-0214/WOI/analysisData/getPromMonitorData
• 请求方法: POST
• 请求参数:
    ◦ appid (必填): 服务编号（如：BA-0208_MSA_036形式）
    ◦ endTime (必填): 结束时间（如：2025-06-17 15:12:54形式）
    ◦ startTime (必填): 开始时间（如：2025-06-17 15:00:54形式）
• 调用示例
    ◦ 入参
https://sit-apps-fc-monitor.faw.cn/out-inter/promMonitorController/getPromMonitorData
{
  "appid": "BA-0208_MSA_036",
  "endTime": "2025-06-17 15:12:54",
  "startTime": "2025-06-17 15:00:54"
}    ◦ 出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
        "diskIoReadLatencyList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "30.5",
                    "24.3",
                    "0.0"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:20:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ],
        "diskIoWriteLatencyList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "13.6",
                    "4.2",
                    "3.1"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:20:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ],
        "instanceList": [
            "ba-0208-msa-036-67c99745bb-rxxsq"
        ],
        "netInSpeedList": [],
        "netOutSpeedList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "0.0",
                    "0.0"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ],
        "diskIopsReadList": null,
        "diskIopsWriteList": null,
        "List": null,
        "diskOutBandWidthList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "0.0",
                    "0.0",
                    "0.0"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:20:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ],
        "memLoadList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "19.2",
                    "19.0",
                    "18.8"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:20:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ],
        "cpuLoadList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "5.0",
                    "5.1",
                    "4.6"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:20:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ],
        "diskInBandWidthList": [
            {
                "image": "ba-0208-msa-036-67c99745bb-rxxsq",
                "valList": [
                    "1195377.8",
                    "829358.3",
                    "834084.3"
                ],
                "dataList": [
                    "2025-06-17 15:10:00",
                    "2025-06-17 15:20:00",
                    "2025-06-17 15:30:00"
                ]
            }
        ]
    },
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WOIN50200"
}