#!/usr/bin/env python3
"""
search_code_by_app_code 工具使用示例

展示如何使用重构后的AST分析功能：
1. 基本用法：只使用app_code和server_url
2. 高级用法：使用精确搜索功能
3. 结果处理：如何解读返回的结果
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app_code_tools import search_code_by_app_code
from code_tools import extract_tool_results


def demo_basic_usage():
    """演示基本用法"""
    print("🔍 演示1: 基本AST分析用法")
    print("=" * 60)
    
    # 基本参数：只需要app_code和server_url
    result = search_code_by_app_code.invoke({
        "app_code": "example-service-001",
        "server_url": "/api/v1/users/list"
    })
    
    print("📋 调用参数:")
    print(f"  app_code: example-service-001")
    print(f"  server_url: /api/v1/users/list")
    print()
    
    print("📄 返回结果结构:")
    print(f"  success: {result.get('success', False)}")
    print(f"  app_code: {result.get('app_code', 'N/A')}")
    print(f"  server_url: {result.get('server_url', 'N/A')}")
    
    if result.get("success", False):
        print("  ✅ 执行成功!")
        
        # AST分析结果
        if "ast_analysis" in result:
            ast_analysis = result["ast_analysis"]
            print(f"  🔍 入口方法: {ast_analysis.get('entry_method', 'N/A')}")
            
            if "call_chain_summary" in ast_analysis:
                summary = ast_analysis["call_chain_summary"]
                print(f"  📊 调用链摘要:")
                print(f"    - 总方法数: {summary.get('total_methods', 0)}")
                print(f"    - 最大深度: {summary.get('max_depth', 0)}")
                print(f"    - 涉及包数: {len(summary.get('packages', []))}")
        
        # 代码上下文
        if "code_context" in result:
            code_context = result["code_context"]
            print(f"  📄 代码上下文条目数: {len(code_context)}")
            for key in list(code_context.keys())[:3]:  # 只显示前3个
                print(f"    - {key}")
    else:
        print("  ❌ 执行失败:")
        print(f"    错误: {result.get('error', 'N/A')}")
        print(f"    步骤: {result.get('step', 'N/A')}")
    
    print()
    return result


def demo_precise_search():
    """演示精确搜索用法"""
    print("🎯 演示2: 精确搜索用法")
    print("=" * 60)
    
    # 包含精确搜索条件的参数
    result = search_code_by_app_code.invoke({
        "app_code": "example-service-002",
        "server_url": "/api/v1/orders/create",
        "class_name": "OrderService",
        "method_name": "createOrder",
        "file_name": "OrderServiceImpl.java"
    })
    
    print("📋 调用参数:")
    print(f"  app_code: example-service-002")
    print(f"  server_url: /api/v1/orders/create")
    print(f"  class_name: OrderService")
    print(f"  method_name: createOrder")
    print(f"  file_name: OrderServiceImpl.java")
    print()
    
    print("📄 返回结果分析:")
    print(f"  success: {result.get('success', False)}")
    
    if result.get("success", False):
        print("  ✅ 执行成功!")
        
        # 精确搜索结果
        if "precise_search" in result:
            precise_search = result["precise_search"]
            print(f"  🎯 精确搜索结果:")
            print(f"    搜索成功: {precise_search.get('success', False)}")
            
            if precise_search.get("success", False):
                matched_nodes = precise_search.get("matched_nodes", [])
                print(f"    匹配节点数: {len(matched_nodes)}")
                
                for i, node in enumerate(matched_nodes[:2], 1):  # 显示前2个节点
                    method_key = node.get("method_key", "")
                    subtree_summary = node.get("subtree_summary", {})
                    print(f"    节点{i}: {method_key}")
                    print(f"      子节点数: {subtree_summary.get('total_nodes', 0)}")
                    print(f"      最大深度: {subtree_summary.get('max_depth', 0)}")
            else:
                search_criteria = precise_search.get("search_criteria", {})
                print(f"    搜索条件: {search_criteria}")
                print(f"    未找到匹配的节点")
        else:
            print("  ⚠️ 未执行精确搜索（可能AST分析失败）")
    else:
        print("  ❌ 执行失败:")
        print(f"    错误: {result.get('error', 'N/A')}")
        print(f"    步骤: {result.get('step', 'N/A')}")
    
    print()
    return result


def demo_result_processing():
    """演示结果处理"""
    print("📊 演示3: 结果处理和格式化")
    print("=" * 60)
    
    # 获取一个结果
    result = search_code_by_app_code.invoke({
        "app_code": "example-service-003",
        "server_url": "/api/v1/products/search"
    })
    
    print("📄 原始结果 JSON 格式:")
    print("-" * 40)
    # 格式化输出JSON（只显示主要字段，避免输出过长）
    simplified_result = {
        "success": result.get("success"),
        "app_code": result.get("app_code"),
        "server_url": result.get("server_url")
    }
    
    if result.get("success", False):
        if "ast_analysis" in result:
            ast_analysis = result["ast_analysis"]
            simplified_result["ast_analysis"] = {
                "entry_method": ast_analysis.get("entry_method"),
                "call_chain_summary": ast_analysis.get("call_chain_summary", {})
            }
        
        if "precise_search" in result:
            precise_search = result["precise_search"]
            simplified_result["precise_search"] = {
                "success": precise_search.get("success"),
                "matched_nodes_count": len(precise_search.get("matched_nodes", []))
            }
        
        if "code_context" in result:
            code_context = result["code_context"]
            simplified_result["code_context_keys"] = list(code_context.keys())
    else:
        simplified_result["error"] = result.get("error")
        simplified_result["step"] = result.get("step")
    
    print(json.dumps(simplified_result, ensure_ascii=False, indent=2))
    print()
    
    print("📄 使用 extract_tool_results 格式化:")
    print("-" * 40)
    
    # 模拟ToolCaller返回的格式
    tool_caller_format = {
        "tool_results": [
            {
                "tool": "search_code_by_app_code",
                "status": "success" if result.get("success", False) else "failed",
                "arguments": {
                    "app_code": "example-service-003",
                    "server_url": "/api/v1/products/search"
                },
                "result": result
            }
        ]
    }
    
    # 使用extract_tool_results格式化
    formatted_result = extract_tool_results(tool_caller_format)
    print(formatted_result)
    
    return result


def demo_error_cases():
    """演示错误情况处理"""
    print("⚠️ 演示4: 错误情况处理")
    print("=" * 60)
    
    error_cases = [
        {
            "name": "缺少server_url",
            "params": {"app_code": "test-001"},
            "description": "测试参数验证"
        },
        {
            "name": "空的server_url",
            "params": {"app_code": "test-002", "server_url": ""},
            "description": "测试空URL处理"
        },
        {
            "name": "无效的app_code",
            "params": {"app_code": "non-existent-service", "server_url": "/api/test"},
            "description": "测试不存在的服务编号"
        }
    ]
    
    for i, case in enumerate(error_cases, 1):
        print(f"错误情况 {i}: {case['name']}")
        print(f"描述: {case['description']}")
        print(f"参数: {case['params']}")
        
        try:
            result = search_code_by_app_code.invoke(case["params"])
            
            print(f"结果: success={result.get('success', False)}")
            if not result.get("success", False):
                print(f"错误: {result.get('error', 'N/A')}")
                print(f"步骤: {result.get('step', 'N/A')}")
            
        except Exception as e:
            print(f"异常: {str(e)}")
        
        print("-" * 40)
    
    print()


def main():
    """主函数"""
    print("🚀 search_code_by_app_code 工具使用示例")
    print("=" * 80)
    print()
    
    print("重构后的工具使用AST分析提供更精确的代码分析结果：")
    print("- 通过server_url找到入口方法")
    print("- 分析完整的方法调用链")
    print("- 可选的精确节点定位")
    print("- 提供详细的代码上下文")
    print()
    
    # 运行各个演示
    try:
        demo_basic_usage()
        demo_precise_search() 
        demo_result_processing()
        demo_error_cases()
        
        print("✅ 演示完成!")
        print()
        print("📚 使用建议:")
        print("1. 基本用法：只需要提供app_code和server_url")
        print("2. 精确搜索：额外提供class_name、method_name、file_name进行精确定位")
        print("3. 结果处理：使用extract_tool_results格式化显示结果")
        print("4. 错误处理：检查result['success']判断执行是否成功")
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示执行异常: {str(e)}")


if __name__ == "__main__":
    main() 