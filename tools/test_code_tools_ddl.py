"""
测试 code_tools.py 对新DDL工具的支持

验证 extract_tool_results 函数能否正确处理新DDL工具的返回结果
"""

from tools.code_tools import extract_tool_results
from utils.logger import logger
import json


def test_ddl_tool_support():
    """测试DDL工具支持"""
    
    logger.info("==== 测试 code_tools.py 对新DDL工具的支持 ====")
    
    # 模拟新DDL工具的返回结果
    mock_ddl_result = {
        "tool_results": [
            {
                "tool": "get_table_ddl_info",
                "status": "success",
                "arguments": {
                    "table_names": ["alarm_master_vote", "user_info"],
                    "context": {
                        "traceid": "test_trace_123",
                        "timestamp": {"start_time": "20250613_150500"}
                    }
                },
                "result": {
                    "success": True,
                    "trace_id": "test_trace_123",
                    "time": "2025-06-13 15:05:00",
                    "table_names": ["alarm_master_vote", "user_info"],
                    "database_info": [
                        {
                            "port": "33321",
                            "peer": "10.106.4.192:33321",
                            "appId": "BA-0208_MSA_GRE",
                            "spanSonLayer": "KingBase",
                            "domainName": "10.106.4.192",
                            "dbName": "db0321",
                            "spanLayer": "Database",
                            "clusterFlag": "xc-arm"
                        }
                    ],
                    "table_info": {
                        "alarm_master_vote": {
                            "tableInfo": {
                                "dbName": "db0321",
                                "nspName": "public",
                                "tableName": "alarm_master_vote",
                                "engine": None,
                                "tableRows": "1",
                                "dataSize": "24 kB",
                                "tableComment": "主投票表"
                            },
                            "indexList": [
                                {
                                    "nspName": "public",
                                    "tableName": "alarm_master_vote",
                                    "indexName": "alarm_master_vote_pkey",
                                    "nonUnique": "true",
                                    "nonPrimary": "true",
                                    "columnList": "id"
                                },
                                {
                                    "nspName": "public",
                                    "tableName": "alarm_master_vote",
                                    "indexName": "uk_vote",
                                    "nonUnique": "true",
                                    "nonPrimary": "false",
                                    "columnList": "vote_name"
                                }
                            ],
                            "columnList": [
                                {
                                    "nspName": "public",
                                    "tableName": "alarm_master_vote",
                                    "columnName": "id",
                                    "columnType": "int8",
                                    "dataType": "int8",
                                    "characterMaximumLength": "",
                                    "numberScale": "0",
                                    "isNullable": "NO",
                                    "columnKey": None,
                                    "columnDefault": "",
                                    "extra": None,
                                    "characterSetName": None,
                                    "columnComment": "主键"
                                },
                                {
                                    "nspName": "public",
                                    "tableName": "alarm_master_vote",
                                    "columnName": "vote_name",
                                    "columnType": "varchar",
                                    "dataType": "varchar",
                                    "characterMaximumLength": "255",
                                    "numberScale": "",
                                    "isNullable": "NO",
                                    "columnKey": None,
                                    "columnDefault": "",
                                    "extra": None,
                                    "characterSetName": None,
                                    "columnComment": "选举名称"
                                }
                            ]
                        }
                    },
                    "index_summary": {
                        "alarm_master_vote": {
                            "total_indexes": 2,
                            "primary_indexes": ["alarm_master_vote_pkey"],
                            "unique_indexes": ["uk_vote"],
                            "normal_indexes": [],
                            "index_details": [
                                {
                                    "index_name": "alarm_master_vote_pkey",
                                    "index_type": "PRIMARY",
                                    "columns": ["id"],
                                    "is_unique": True,
                                    "is_primary": True
                                },
                                {
                                    "index_name": "uk_vote",
                                    "index_type": "UNIQUE",
                                    "columns": ["vote_name"],
                                    "is_unique": True,
                                    "is_primary": False
                                }
                            ]
                        }
                    }
                }
            }
        ]
    }
    
    # 测试extract_tool_results函数
    try:
        logger.info("📋 测试参数:")
        logger.info(f"   - 工具名称: get_table_ddl_info")
        logger.info(f"   - 查询表名: {mock_ddl_result['tool_results'][0]['arguments']['table_names']}")
        
        # 调用extract_tool_results函数（不是DDL工具本身）
        extracted_result = extract_tool_results(mock_ddl_result)
        
        logger.info("✅ extract_tool_results 执行成功！")
        logger.info("\n📄 提取的结果:")
        logger.info("=" * 80)
        logger.info(extracted_result)
        logger.info("=" * 80)
        
        # 验证结果是否包含期望的内容
        expected_content = [
            "数据库表DDL信息",
            "TraceID: test_trace_123",
            "查询时间: 2025-06-13 15:05:00",
            "查询表名: alarm_master_vote, user_info",
            "数据库连接信息",
            "表结构和索引信息",
            "alarm_master_vote",
            "索引总数: 2",
            "主键索引: alarm_master_vote_pkey",
            "唯一索引: uk_vote"
        ]
        
        missing_content = []
        for content in expected_content:
            if content not in extracted_result:
                missing_content.append(content)
        
        if not missing_content:
            logger.info("✅ 所有期望的内容都已正确提取！")
        else:
            logger.info("⚠️ 以下期望内容未找到:")
            for content in missing_content:
                logger.info(f"   - {content}")
        
        # 检查结果长度
        logger.info(f"\n📊 结果统计:")
        logger.info(f"   - 总字符数: {len(extracted_result)}")
        logger.info(f"   - 总行数: {len(extracted_result.split('\\n'))}")
        
    except Exception as e:
        logger.info(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    logger.info("==== 测试完成 ====")


def test_mixed_tool_results():
    """测试混合工具结果（包含代码工具和DDL工具）"""
    
    logger.info("\n==== 测试混合工具结果 ====")
    
    # 模拟包含代码工具和DDL工具的混合结果
    mixed_result = {
        "tool_results": [
            {
                "tool": "search_code_by_app_code",
                "status": "success",
                "arguments": {
                    "app_code": "TEST-001",
                    "file_name": "UserService.java"
                },
                "result": {
                    "success": True,
                    "app_code": "TEST-001",
                    "code_context": {
                        "keyword_search_file_1_UserService.java": "public class UserService {\n    public User getUserById(Long id) {\n        return userRepository.findById(id);\n    }\n}"
                    }
                }
            },
            {
                "tool": "get_table_ddl_info",
                "status": "success",
                "arguments": {
                    "table_names": ["user_table"]
                },
                "result": {
                    "success": True,
                    "trace_id": "mixed_test_456",
                    "table_info": {
                        "user_table": {
                            "tableInfo": {
                                "tableName": "user_table",
                                "tableRows": "1000",
                                "dataSize": "128 kB"
                            }
                        }
                    },
                    "index_summary": {
                        "user_table": {
                            "total_indexes": 1,
                            "primary_indexes": ["user_pkey"],
                            "unique_indexes": [],
                            "normal_indexes": []
                        }
                    }
                }
            }
        ]
    }
    
    try:
        extracted_result = extract_tool_results(mixed_result)
        
        logger.info("✅ 混合工具结果提取成功！")
        logger.info("\n📄 提取的混合结果:")
        logger.info("=" * 60)
        logger.info(extracted_result)
        logger.info("=" * 60)
        
        # 验证是否同时包含代码和DDL内容
        has_code_content = "找到的代码内容" in extracted_result
        has_ddl_content = "数据库表DDL信息" in extracted_result
        
        logger.info(f"\n🔍 内容验证:")
        logger.info(f"   - 包含代码内容: {'✅' if has_code_content else '❌'}")
        logger.info(f"   - 包含DDL内容: {'✅' if has_ddl_content else '❌'}")
        
        if has_code_content and has_ddl_content:
            logger.info("✅ 混合工具结果处理正确！")
        else:
            logger.info("⚠️ 混合工具结果处理可能存在问题")
            
    except Exception as e:
        logger.info(f"❌ 混合测试过程中发生异常: {str(e)}")
    
    logger.info("==== 混合测试完成 ====")


if __name__ == "__main__":
    # 运行DDL工具支持测试
    test_ddl_tool_support()
    
    # 运行混合工具结果测试
    test_mixed_tool_results() 