# DDL信息获取工具集成指南

## 概述

新开发的 `get_table_ddl_info` 工具可以根据上下文信息获取数据库表的索引信息和结构信息。工具会自动从上下文中提取 trace_id 和时间信息，简化了调用过程。

## 工具文件结构

```
tools/
├── ddl_info_tools.py          # 主要工具实现
├── ddl_usage_example.py       # 使用示例
└── integration_guide.md       # 本集成指南
```

## 核心功能

### 主要工具函数：`get_table_ddl_info`

**参数：**
- `table_names`: 要查询的表名列表（必需）
- `context`: 步骤上下文信息（必需），必须包含：
  - `traceid`: 链路ID
  - `timestamp.start_time`: 链路开始时间

**返回值：**
包含数据库信息、表结构、索引详情的完整字典。

### 上下文信息要求

工具需要从 `context` 中获取以下信息：
```python
context = {
    "traceid": "链路ID",
    "timestamp": {
        "start_time": "开始时间",  # 支持多种格式
        "end_time": "结束时间"    # 可选
    },
    # 其他可选信息
    "sys_code": "系统编号",
    "error_locations": {...}  # 错误位置信息
}
```

### 支持的时间格式

工具支持多种时间格式，会自动转换为API要求的格式：
- `"20250613_150500"` → `"2025-06-13 15:05:00"`
- `"2025-06-13T15:05:00"` → `"2025-06-13 15:05:00"`
- `"2025-06-13 15:05:00"` → 保持不变

## 集成方式

### 1. 在现有分析步骤中使用

可以在以下步骤中集成DDL工具：

#### 在 `CodeContextStep` 中添加DDL查询
```python
# 在 services/logs_analysis_service.py 中的 CodeContextStep 类

def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
    # ... 现有代码 ...
    
    # 如果错误位置信息中包含SQL相关内容，获取DDL信息
    error_locations = context.get("error_locations", {})
    
    # 提取可能的表名
    table_names = self._extract_table_names_from_error(error_locations)
    
    if table_names:
        logger.info(f"🗃️ 检测到数据库相关错误，获取表DDL信息...")
        
        # 添加DDL查询工具
        from tools.ddl_info_tools import get_table_ddl_info
        ddl_tools = [get_table_ddl_info]
        
        # 创建DDL查询
        ddl_query = f"获取以下表的索引和结构信息：{', '.join(table_names)}"
        
        # 调用DDL工具（新的参数结构）
        ddl_result = self.analyzer.call_tools(ddl_tools, ddl_query, context=context)
        
        # 将DDL信息添加到上下文
        context["ddl_info"] = ddl_result
    
    # ... 继续现有逻辑 ...
```

#### 辅助方法：提取表名
```python
def _extract_table_names_from_error(self, error_locations: Dict[str, Any]) -> List[str]:
    """从错误位置信息中提取表名"""
    table_names = []
    
    issues = error_locations.get("issues", [])
    for issue in issues:
        origin_point = issue.get("root_cause", {}).get("origin_point", {})
        
        # 从SQL中提取表名
        sql = origin_point.get("sql", "")
        if sql:
            # 简单的表名提取逻辑
            import re
            table_pattern = re.compile(r'FROM\s+(\w+)|JOIN\s+(\w+)', re.IGNORECASE)
            for match in table_pattern.finditer(sql):
                table = match.group(1) if match.group(1) else match.group(2)
                if table:
                    table_names.append(table)
        
        # 从已有的table_name_list中提取
        table_list = origin_point.get("table_name_list", [])
        table_names.extend(table_list)
    
    return list(set(table_names))  # 去重
```

### 2. 创建独立的DDL分析步骤

#### 新增 `DDLAnalysisStep` 类
```python
class DDLAnalysisStep(AnalysisStep):
    """DDL信息分析步骤"""
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("\n🗃️ 开始DDL信息分析步骤...")
        
        try:
            # 检查必要的上下文信息
            if not self._validate_context(context):
                logger.info("⚠️ 上下文信息不完整，跳过DDL分析")
                return {"success": True, "ddl_info": None}
            
            # 从错误位置或其他步骤中提取表名
            table_names = self._get_table_names_from_context(context)
            
            if not table_names:
                logger.info("⚠️ 未找到需要查询的表名，跳过DDL分析")
                return {"success": True, "ddl_info": None}
            
            # 创建DDL查询工具
            from tools.ddl_info_tools import get_table_ddl_info
            
            # 调用工具（新的参数结构）
            ddl_result = get_table_ddl_info(
                table_names=table_names,
                context=context  # 工具会自动从context中提取trace_id和时间
            )
            
            # 将结果添加到上下文
            context["ddl_analysis"] = ddl_result
            
            logger.info("✅ DDL信息分析完成")
            return {"success": True, "ddl_info": ddl_result}
            
        except Exception as e:
            error_msg = f"DDL信息分析失败: {str(e)}"
            logger.info(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
    
    def _validate_context(self, context: Dict[str, Any]) -> bool:
        """验证上下文信息是否包含必要字段"""
        if not context.get("traceid"):
            return False
        
        timestamp_info = context.get("timestamp", {})
        if not timestamp_info.get("start_time"):
            return False
        
        return True
    
    def _get_table_names_from_context(self, context: Dict[str, Any]) -> List[str]:
        """从上下文中获取表名"""
        table_names = []
        
        # 从错误位置信息中提取
        error_locations = context.get("error_locations", {})
        issues = error_locations.get("issues", [])
        for issue in issues:
            origin_point = issue.get("root_cause", {}).get("origin_point", {})
            table_list = origin_point.get("table_name_list", [])
            table_names.extend(table_list)
        
        # 从代码上下文中提取（如果有的话）
        # ... 其他提取逻辑 ...
        
        return list(set(table_names))
```

### 3. 在分析流程中添加DDL步骤

#### 修改 `logs_analysis_service.py` 中的步骤定义
```python
def _build_analysis_steps(self) -> List[AnalysisStep]:
    """构建分析步骤列表"""
    return [
        ExtractStep(self.analyzer),
        ErrorLocationStep(self.analyzer),
        CodeContextStep(self.analyzer), 
        DDLAnalysisStep(self.analyzer),  # 新增DDL分析步骤
        ProblemAnalysisStep(self.analyzer)
    ]
```

## 配置和环境

### 环境变量
确保以下环境变量已正确配置：

```bash
# 在 .env 文件中
API_PROD_GATEWAY_HOST=https://prod-api.faw.cn
API_PROD_GATEWAY_USERNAME=your_username  
API_PROD_GATEWAY_PASSWORD=your_password
```

### 依赖检查
确保以下模块可正常工作：
- `utils.get_token` - 用于获取访问令牌
- `utils.logger` - 用于日志记录
- `models.params` - 用于参数验证

## 使用示例

### 直接调用工具
```python
from tools.ddl_info_tools import get_table_ddl_info

# 构建上下文信息
context = {
    "traceid": "0db2ac37a4394bb898a2accb724777d8.83.17497980724831609",
    "timestamp": {
        "start_time": "20250613_150500"  # 会自动转换为 2025-06-13 15:05:00
    },
    "sys_code": "BA-0208_MSA_GRE"
}

# 调用工具
result = get_table_ddl_info(
    table_names=["alarm_master_vote", "user_info"],
    context=context
)

if result["success"]:
    # 处理索引信息
    index_summary = result["index_summary"]
    for table_name, summary in index_summary.items():
        print(f"表 {table_name} 有 {summary['total_indexes']} 个索引")
```

### 在工具列表中使用
```python
# 在分析步骤中
from tools.ddl_info_tools import get_table_ddl_info

tools = [get_table_ddl_info]
query = "获取alarm_master_vote表的索引信息"

# 上下文会自动传递给工具
result = self.analyzer.call_tools(tools, query, context=context)
```

### 智能表名提取
工具支持从上下文中自动提取表名：
```python
# 如果 context 中包含错误位置信息，工具会自动提取表名
context = {
    "traceid": "test_trace_123",
    "timestamp": {"start_time": "20250613_150500"},
    "error_locations": {
        "issues": [{
            "root_cause": {
                "origin_point": {
                    "table_name_list": ["additional_table"]
                }
            }
        }]
    }
}

# 工具会合并 table_names 参数和上下文中的表名
result = get_table_ddl_info(
    table_names=["main_table"],
    context=context
)
# 实际查询：["main_table", "additional_table"]
```

## 注意事项

1. **上下文要求**：工具必须有有效的 `context` 参数，包含 `traceid` 和 `timestamp.start_time`。

2. **时间格式灵活性**：支持多种时间格式，会自动转换为API要求的格式。

3. **API限制**：DDL查询依赖外部API，需要注意调用频率和超时设置。

4. **权限要求**：确保有权访问生产环境的数据库信息API。

5. **错误处理**：工具内置了完整的错误处理机制，失败时会返回详细的错误信息。

6. **智能表名提取**：工具会自动从上下文的错误位置信息中提取额外的表名。

## 参数变更说明

### 旧版本参数
```python
get_table_ddl_info(
    trace_id="xxx",
    time="2025-06-13 15:05:00", 
    table_names=["table1"],
    context={}
)
```

### 新版本参数
```python
context = {
    "traceid": "xxx",
    "timestamp": {"start_time": "20250613_150500"}
}

get_table_ddl_info(
    table_names=["table1"],
    context=context
)
```

## 扩展功能

未来可考虑的扩展：

1. **缓存机制**：对于相同的trace_id和时间，缓存DDL查询结果。
2. **批量查询**：支持同时查询多个数据库的表信息。
3. **索引分析**：基于索引信息提供性能优化建议。
4. **SQL优化建议**：结合索引信息和SQL语句提供优化建议。 