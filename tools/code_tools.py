"""
代码工具执行结果整理
"""

import json
from typing import Dict, Any, List
from utils.logger import logger


def extract_tool_results(result: Dict[str, Any]) -> str:
    """从ToolCaller结果中提取工具执行结果
    
    专门处理ToolCaller返回的标准格式：
    {
        "tool_results": [
            {
                "tool": "工具名称",
                "status": "success|failed|not_found",
                "arguments": {...},
                "result": {...},  # 成功时
                "error": "错误信息"  # 失败时
            }
        ]
    }
    
    Args:
        result: ToolCaller执行结果
    
    Returns:
        格式化的工具结果字符串
    """
    extracted_info = []
    MAX_LENGTH = 150000  # 最大字符长度限制
    truncated = False  # 截断标记
    is_single_tool = False  # 是否为单个工具结果
    
    def has_meaningful_content(tool_result: Dict[str, Any]) -> bool:
        """检查工具结果是否有实际有用的内容"""
        if tool_result.get("status") != "success" or "result" not in tool_result:
            return False
        
        result_data = tool_result["result"]
        
        # 检查AST分析结果
        if "ast_analysis" in result_data and result_data["ast_analysis"]:
            return True
        
        # 检查代码上下文
        if "code_context" in result_data and result_data["code_context"]:
            return True
        
        # 检查是否有旧格式的代码内容
        for key in ["file_contents"]:
            if key in result_data and result_data[key]:
                return True
        
        # 检查是否有DDL内容（旧格式）
        if "found_tables" in result_data and result_data["found_tables"]:
            return True
        
        # 检查是否有新DDL工具的内容
        if "table_info" in result_data and result_data["table_info"]:
            return True
        
        if "index_summary" in result_data and result_data["index_summary"]:
            return True
        
        return False
    
    # 检查工具结果数量 - 只统计有实际内容的工具
    if "tool_results" in result and result["tool_results"]:
        tool_results = result["tool_results"]
        # 只统计有实际内容的成功工具结果
        successful_tools = [tr for tr in tool_results if has_meaningful_content(tr)]
        is_single_tool = len(successful_tools) == 1
    
    def safe_append(content: str) -> bool:
        """安全追加内容，检查长度限制"""
        nonlocal truncated
        if truncated:
            return False
            
        current_length = len('\n'.join(extracted_info))
        if current_length + len(content) + 1 > MAX_LENGTH:
            if not is_single_tool:
                # 多个工具结果时，使用早期截断
                extracted_info.append(f"\n⚠️ 内容过长，已截断（当前长度：{current_length}字符，限制：{MAX_LENGTH}字符）")
                truncated = True
                return False
            else:
                # 单个工具结果时，尝试截取内容
                available_space = MAX_LENGTH - current_length - 1
                if available_space > 0:
                    truncated_content = content[:available_space]
                    # 尝试在合适的位置截断（避免在代码中间截断）
                    if len(content) > available_space:
                        # 在最后一个换行符处截断
                        last_newline = truncated_content.rfind('\n')
                        if last_newline > available_space * 0.8:  # 如果最后一个换行符位置合理
                            truncated_content = truncated_content[:last_newline]
                    
                    extracted_info.append(truncated_content)
                    truncated = True
                    return True
                else:
                    # 空间不足，无法添加任何内容
                    truncated = True
                    return False
        
        extracted_info.append(content)
        return True
    
    def smart_truncate_content(content: str, max_length: int) -> str:
        """智能截取内容，在合适的位置截断"""
        if len(content) <= max_length:
            return content
        
        truncated = content[:max_length]
        
        # 尝试在合适的位置截断
        # 1. 优先在换行符处截断
        last_newline = truncated.rfind('\n')
        if last_newline > max_length * 0.7:  # 如果换行符位置合理
            return truncated[:last_newline]
        
        # 2. 在空格处截断
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:
            return truncated[:last_space]
        
        # 3. 直接截断
        return truncated
    
    def extract_search_targets(arguments: Dict[str, Any]) -> List[str]:
        """提取搜索目标信息"""
        targets = []
        if arguments.get("server_url"):
            targets.append(f"服务URL: {arguments['server_url']}")
        if arguments.get("file_name"):
            targets.append(f"文件名: {arguments['file_name']}")
        if arguments.get("class_name"):
            targets.append(f"类名: {arguments['class_name']}")
        if arguments.get("method_name"):
            targets.append(f"方法名: {arguments['method_name']}")
        # 保留对旧格式的兼容性
        if arguments.get("sql_fragment"):
            targets.append(f"SQL片段: {arguments['sql_fragment'][:50]}...")
        if arguments.get("table_names"):
            table_list = arguments["table_names"]
            if isinstance(table_list, list):
                targets.append(f"表名: {', '.join(table_list)}")
            else:
                targets.append(f"表名: {table_list}")
        return targets
    
    def process_ast_analysis_result(tool_data: Dict[str, Any]) -> bool:
        """处理AST分析结果"""
        ast_analysis = tool_data.get("ast_analysis", {})
        if not ast_analysis:
            return True
        
        # 显示AST分析摘要
        if not safe_append("🔍 AST方法调用链分析结果"):
            return False
        if not safe_append("=" * 50):
            return False
        
        # 入口方法信息
        entry_method = ast_analysis.get("entry_method", "")
        if entry_method:
            if not safe_append(f"🎯 入口方法: {entry_method}"):
                return False
        
        # 调用链摘要
        call_chain_summary = ast_analysis.get("call_chain_summary", {})
        if call_chain_summary:
            total_methods = call_chain_summary.get("total_methods", 0)
            max_depth = call_chain_summary.get("max_depth", 0)
            packages = call_chain_summary.get("packages", [])
            
            if not safe_append(f"📊 调用链摘要:"):
                return False
            if not safe_append(f"   - 总方法数: {total_methods}"):
                return False
            if not safe_append(f"   - 最大深度: {max_depth}"):
                return False
            if not safe_append(f"   - 涉及包数: {len(packages)}"):
                return False
            
            if packages and not safe_append(f"📦 涉及的包: {', '.join(packages[:5])}{'...' if len(packages) > 5 else ''}"):
                return False
        
        # 精确搜索结果
        precise_search = tool_data.get("precise_search", {})
        if precise_search and precise_search.get("success", False):
            matched_nodes = precise_search.get("matched_nodes", [])
            if matched_nodes:
                if not safe_append(f"\n🎯 精确搜索结果 (匹配 {len(matched_nodes)} 个节点):"):
                    return False
                
                for i, node in enumerate(matched_nodes[:3], 1):  # 只显示前3个
                    method_key = node.get("method_key", "")
                    subtree_summary = node.get("subtree_summary", {})
                    
                    if not safe_append(f"   {i}. {method_key}"):
                        return False
                    if not safe_append(f"      子节点数: {subtree_summary.get('total_nodes', 0)}, 深度: {subtree_summary.get('max_depth', 0)}"):
                        return False
                
                if len(matched_nodes) > 3:
                    if not safe_append(f"   ... 还有 {len(matched_nodes) - 3} 个匹配节点"):
                        return False
        
        if not safe_append(""):  # 添加空行
            return False
        
        return True
    
    def process_code_context(code_context: Dict[str, Any]) -> bool:
        """处理代码上下文"""
        if not code_context:
            return True
            
        if not safe_append("📄 代码上下文："):
            return False
        if not safe_append("=" * 50):
            return False
        
        # 处理每个代码上下文条目
        for key, content in code_context.items():
            if not safe_append(f"\n📁 {key}"):
                return False
            if not safe_append("-" * 30):
                return False
            if not safe_append("```"):
                return False
            
            # 对于代码上下文，进行智能截取
            if not is_single_tool:
                current_length = len('\n'.join(extracted_info))
                remaining_items = len([k for k in code_context.keys() if k >= key])
                available_space = (MAX_LENGTH - current_length - 500) // remaining_items
                
                if len(content) > available_space and available_space > 100:
                    truncated_content = smart_truncate_content(content, available_space)
                    extracted_info.append(truncated_content)
                    extracted_info.append("```")
                    extracted_info.append(f"\n📝 内容已截取显示，完整内容共 {len(content)} 字符，已显示 {len(truncated_content)} 字符\n")
                    continue
            
            # 对于单个工具结果，预检查内容长度
            if is_single_tool:
                current_length = len('\n'.join(extracted_info))
                reserved_space = 100
                available_space = MAX_LENGTH - current_length - reserved_space
                
                if len(content) > available_space and available_space > 0:
                    truncated_content = smart_truncate_content(content, available_space)
                    extracted_info.append(truncated_content)
                    extracted_info.append("```")
                    tip_message = f"\n📝 内容已截取显示，原内容共 {len(content)} 字符，显示 {len(truncated_content)} 字符"
                    extracted_info.append(tip_message)
                    return True
            
            if not safe_append(content):
                return False
            if not safe_append("```\n"):
                return False
        
        return True
    
    def process_ddl_info_result(tool_data: Dict[str, Any]) -> bool:
        """处理新DDL工具的返回结果 - 直接输出文本形式的索引摘要"""
        # DDL工具已经返回了格式化的文本形式索引摘要
        index_summary_text = tool_data.get("index_summary", "")
        
        if not index_summary_text:
            return True  # 如果没有索引摘要，直接返回成功
        
        # 直接输出DDL工具已经格式化好的索引摘要文本
        if not safe_append(index_summary_text):
            return False
        
        if not safe_append(""):  # 添加空行分隔
            return False
        
        return True
    
    def process_single_tool_result(tool_result: Dict[str, Any]) -> bool:
        """处理单个工具结果"""
        tool_name = tool_result.get("tool", "未知工具")
        status = tool_result.get("status", "未知")
        
        if status == "success" and "result" in tool_result:
            tool_data = tool_result["result"]
            arguments = tool_result.get("arguments", {})
            
            # 添加搜索目标信息
            search_targets = extract_search_targets(arguments)
            if search_targets:
                if not safe_append(f"🎯 查找目标：{' | '.join(search_targets)}"):
                    return False
                
                # 添加服务编号（如果有）
                app_code = tool_data.get("app_code")
                if app_code:
                    if not safe_append(f"📋 服务编号：{app_code}\n"):
                        return False
            
            # 处理不同类型的数据结构 - 优先处理AST分析结果
            processed = False
            
            # 优先处理AST分析结果
            if "ast_analysis" in tool_data and tool_data["ast_analysis"]:
                if not process_ast_analysis_result(tool_data):
                    return False
                processed = True
            
            # 处理精确搜索结果已包含在AST分析处理中
            
            # 然后处理代码上下文
            if "code_context" in tool_data and tool_data["code_context"]:
                if not process_code_context(tool_data["code_context"]):
                    return False
                processed = True
            
            # 处理DDL索引摘要信息（保持兼容性）
            if not processed and "index_summary" in tool_data and tool_data["index_summary"]:
                if not process_ddl_info_result(tool_data):
                    return False
                processed = True
            
            # 处理旧格式的代码内容（保持兼容性）
            if not processed:
                for key in ["file_contents"]:
                    if key in tool_data and tool_data[key]:
                        if not process_code_context(tool_data[key]):
                            return False
                        processed = True
                        break
            
            # 如果没有找到任何内容
            if not processed:
                if not safe_append(f"❌ 工具 {tool_name} 未返回可识别的内容\n"):
                    return False
        
        elif status in ["failed", "not_found"]:
            error_msg = tool_result.get("error", "未知错误")
            if not safe_append(f"❌ {tool_name} 执行失败：{error_msg}\n"):
                return False
        
        return True
    
    # 处理ToolCaller返回的结果格式
    if "tool_results" in result and result["tool_results"]:
        for tool_result in result["tool_results"]:
            if truncated:  # 如果已截断，停止处理
                break
            if not process_single_tool_result(tool_result):
                break
    else:
        return "❌ 未找到工具执行结果"
    
    # 如果没有提取到任何信息
    if not extracted_info:
        return "❌ 未找到任何代码信息"
        
    return "\n".join(extracted_info) 