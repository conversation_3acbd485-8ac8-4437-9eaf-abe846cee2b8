总体需求：参考下面的步骤方法，需要实现一个工具函数根据trace_id和时间和表名获取该表的索引信息
在tools/ 路径下创建新的文件


1.根据trace_id和时间获取链路对应数据库和中间件信息
• 接口路径
    ◦ sit：https://test-api.faw.cn:30443/JT/BA/BA-0214/WOI/analysisData/getDataBaseAndMiddlewareInfoByTraceId
• 请求方法: GET
• 请求参数:
    ◦ time (必填): 链路时间
    ◦ traceId (必填): 链路ID
• 调用示例
    ◦ 入参
    https://sit-apps-fc-monitor.faw.cn/out-inter/rootCauseAnaController/getDataBaseAndMiddlewareInfoByTraceId?traceId=0db2ac37a4394bb898a2accb724777d8.83.17497980724831609&time=2025-06-13 15:05:00
    
出参
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "port": "33321",
            "peer": "************:33321",
            "appId": "BA-0208_MSA_GRE",
            "spanSonLayer": "KingBase",
            "domainName": "************",
            "dbName": "db0321",
            "spanLayer": "Database",
            "clusterFlag": "xc-arm"
        }
    ],
    "msgType": "I",
    "errType": "N",
    "errMsg": null,
    "errCode": "null0IBA-0214_MSA_WOIN50200"
}


2.数据库表结构接口，根据ip,port,databaseName,dbType返回该条件下所有表的结构（可从中根据表名查找索引信息）
toekn取得参考：
from utils.get_token import get_api_prod_gateway_token
token = get_api_prod_gateway_token()

数据库表结构接口：
curl --location 'https://prod-api.faw.cn/JT/DA/DA-0304/001/DEFAULT/getDbInfo?access_token=a20b7bd3e7bc4f82b9d5a90d30a334c3' \
--header 'Content-Type: application/json' \
--data '{
    "ip": "************",
    "port": "33321",
    "databaseName": "alarmdb",
    "dbType": "KingBase"
}'

返回数据结构参照：
{"code":200,"aaData":[{"tableInfo":{"dbName":"alarmdb","nspName":"public","tableName":"alarm_master_vote","engine":null,"tableRows":"1","dataSize":"24 kB","tableComment":""},"indexList":[{"nspName":"public","tableName":"alarm_master_vote","indexName":"alarm_master_vote_pkey","nonUnique":"true","nonPrimary":"true","columnList":"id"},{"nspName":"public","tableName":"alarm_master_vote","indexName":"uk_vote","nonUnique":"true","nonPrimary":"false","columnList":"vote_name"}],"columnList":[{"nspName":"public","tableName":"alarm_master_vote","columnName":"id","columnType":"int8","dataType":"int8","characterMaximumLength":"","numberScale":"0","isNullable":"NO","columnKey":null,"columnDefault":"","extra":null,"characterSetName":null,"columnComment":"主键"},{"nspName":"public","tableName":"alarm_master_vote","columnName":"master","columnType":"varchar","dataType":"varchar","characterMaximumLength":"255","numberScale":"","isNullable":"NO","columnKey":null,"columnDefault":"","extra":null,"characterSetName":null,"columnComment":"主标识，一般是ip"},{"nspName":"public","tableName":"alarm_master_vote","columnName":"vote_name","columnType":"varchar","dataType":"varchar","characterMaximumLength":"255","numberScale":"","isNullable":"NO","columnKey":null,"columnDefault":"","extra":null,"characterSetName":null,"columnComment":"选举名称"},{"nspName":"public","tableName":"alarm_master_vote","columnName":"can_vote","columnType":"varchar","dataType":"varchar","characterMaximumLength":"64","numberScale":"","isNullable":"NO","columnKey":null,"columnDefault":"","extra":null,"characterSetName":null,"columnComment":"是否开放选举"},{"nspName":"public","tableName":"alarm_master_vote","columnName":"gmt_create","columnType":"timestamp","dataType":"timestamp","characterMaximumLength":"","numberScale":"","isNullable":"NO","columnKey":null,"columnDefault":"CURRENT_TIMESTAMP","extra":null,"characterSetName":null,"columnComment":"创建时间"},{"nspName":"public","tableName":"alarm_master_vote","columnName":"last_lease","columnType":"timestamp","dataType":"timestamp","characterMaximumLength":"","numberScale":"","isNullable":"NO","columnKey":null,"columnDefault":"","extra":null,"characterSetName":null,"columnComment":"最新签约时间"},{"nspName":"public","tableName":"alarm_master_vote","columnName":"gmt_modified","columnType":"timestamp","dataType":"timestamp","characterMaximumLength":"","numberScale":"","isNullable":"NO","columnKey":null,"columnDefault":"CURRENT_TIMESTAMP","extra":null,"characterSetName":null,"columnComment":"修改时间"}]},