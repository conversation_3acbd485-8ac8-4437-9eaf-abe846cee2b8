"""
数据库DDL信息获取工具

根据trace_id和时间获取数据库表的索引信息和结构信息
"""

import json
import requests
from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from models.params import DDLInfoSearchParams
from utils.get_token import get_api_prod_gateway_token
from utils.logger import logger
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

@tool(args_schema=DDLInfoSearchParams)
def get_table_ddl_info(
    table_names: List[str],
    start_time: str,
    app_code: str,
    context: dict = None
) -> dict:
    """根据trace_id和时间获取指定表的DDL信息和索引信息
    
    工作流程：
    1. 从上下文中提取trace_id信息
    2. 根据trace_id和时间获取链路对应的数据库和中间件信息
    3. 根据获取的数据库信息查询表结构
    4. 从表结构中提取指定表的索引信息
    
    Args:
        table_names: 要查询的表名列表
        start_time: 开始时间，格式如：2025-07-07 09:22:34 或 20250707_092234
        app_code: 服务编号，用于筛选对应的数据库连接，必须提供
        context: 步骤上下文信息，不需要大模型主动传递
    
    Returns:
        包含DDL信息查询结果的字典
    """
    
    logger.info(f"\n==== 开始获取DDL信息 ====")
    
    # 从上下文中提取trace_id信息
    if not context:
        return {
            "success": False,
            "error": "context 参数是必需的",
            "table_names": table_names,
            "step": "parameter_validation"
        }
    
    # 提取trace_id
    trace_id = context.get("traceid", "")
    if not trace_id:
        return {
            "success": False,
            "error": "context中缺少traceid信息",
            "table_names": table_names,
            "step": "parameter_validation"
        }
    
    # 格式化时间为API要求的格式
    formatted_time = _format_time_from_context(start_time)
    
    logger.info(f"🔍 TraceID: {trace_id}")
    logger.info(f"⏰ 时间: {formatted_time} (原始: {start_time})")
    logger.info(f"🏷️ 服务编号: {app_code}")
    logger.info(f"📋 查询表名: {', '.join(table_names)}")
    
    
    try:
        # 第一步：根据trace_id和时间获取数据库和中间件信息
        logger.info(f"\n📡 第一步：获取链路对应的数据库和中间件信息...")
        
        database_info_result = _get_database_middleware_info(trace_id, formatted_time)
        
        if not database_info_result.get("success", False):
            return {
                "success": False,
                "error": database_info_result.get("error", "获取数据库信息失败"),
                "trace_id": trace_id,
                "time": formatted_time,
                "table_names": table_names,
                "step": "database_info_query"
            }
        
        database_list = database_info_result.get("data", [])
        if not database_list:
            return {
                "success": False,
                "error": "未找到链路对应的数据库信息",
                "trace_id": trace_id,
                "time": formatted_time,
                "table_names": table_names,
                "step": "database_info_query"
            }
        
        # 筛选出spanLayer为Database且appId匹配的数据库服务
        database_list = [db for db in database_list 
                        if db.get("spanLayer") == "Database" and db.get("appId") == app_code]
        
        if not database_list:
            return {
                "success": False,
                "error": f"未找到链路对应的数据库服务（已过滤非数据库服务和不匹配服务编号'{app_code}'的服务）",
                "trace_id": trace_id,
                "time": formatted_time,
                "table_names": table_names,
                "step": "database_info_query"
            }
        
        logger.info(f"✅ 找到 {len(database_list)} 个匹配服务编号 '{app_code}' 的数据库连接:")
        # logger.info(f"✅ 详情 {database_list} ")
        for db in database_list:
            logger.info(f"   - {db.get('dbName', 'Unknown')} ({db.get('spanSonLayer', 'Unknown')}): {db.get('peer', 'Unknown')}")
        
        # 第二步：获取每个数据库的表结构信息
        logger.info(f"\n🗃️ 第二步：查询表结构信息...")
        
        table_info_results = {}
        index_summary_results = {}
        
        for db_info in database_list:
            domain_name = db_info.get("domainName", "")
            port = db_info.get("port", "")
            db_name = db_info.get("dbName", "")
            db_type = db_info.get("spanSonLayer", "")
            
            if not all([domain_name, port, db_name, db_type]):
                logger.info(f"⚠️ 数据库信息不完整，跳过: {db_info}")
                continue
            
            logger.info(f"\n   🔍 查询数据库: {db_name} ({db_type}) at {domain_name}:{port}")
            
            # 获取该数据库的表结构信息
            table_structure_result = _get_table_structure_info(domain_name, port, db_name, db_type)
            
            if not table_structure_result.get("success", False):
                logger.info(f"   ❌ 获取表结构失败: {table_structure_result.get('error', 'Unknown error')}")
                continue
            
            tables_data = table_structure_result.get("data", [])
            logger.info(f"   ✅ 获取到 {len(tables_data)} 个表的结构信息")
            
            # 筛选指定的表
            for table_name in table_names:
                matching_table = None
                for table_data in tables_data:
                    table_info = table_data.get("tableInfo", {})
                    if table_info.get("tableName", "").lower() == table_name.lower():
                        matching_table = table_data
                        break
                
                if matching_table:
                    table_info_results[table_name] = matching_table
                    
                    # 生成索引摘要
                    index_summary = _generate_index_summary(matching_table)
                    index_summary_results[table_name] = index_summary
                    
                    logger.info(f"   ✅ 找到表 '{table_name}': {index_summary['total_indexes']} 个索引")
                else:
                    logger.info(f"   ❌ 未找到表 '{table_name}'")
        
        if not table_info_results:
            return {
                "success": False,
                "error": f"在所有数据库中都未找到指定的表: {', '.join(table_names)}",
                "trace_id": trace_id,
                "time": formatted_time,
                "table_names": table_names,
                "step": "table_structure_query",
                "database_info": database_list
            }
        
        # 第三步：构建结果，将索引摘要转换为文本形式
        logger.info(f"\n📊 第三步：构建结果...")
        logger.info(f"   成功查询到 {len(table_info_results)} 个表的信息")
        
        # 生成索引摘要的文本形式
        index_summary_text = _generate_index_summary_text(index_summary_results)
        
        result = {
            "success": True,
            "trace_id": trace_id,
            "time": formatted_time,
            "table_names": table_names,
            "app_code": app_code,
            "database_info": database_list,
            "table_info": table_info_results,
            "index_summary": index_summary_text
        }
        
        logger.info(f"✅ DDL信息获取完成")
        logger.info("==== DDL信息获取流程完成 ====\n")
        
        return result
        
    except Exception as e:
        error_msg = f"执行过程中发生异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "trace_id": trace_id,
            "time": formatted_time,
            "table_names": table_names,
            "step": "execution_error"
        }


def _get_database_middleware_info(trace_id: str, time: str) -> Dict[str, Any]:
    """获取链路对应的数据库和中间件信息"""
    base_url = os.getenv("DATABASE_MIDDLEWARE_INFO_URL", "")
    try:
        # 获取访问令牌
        try:
            token = get_api_prod_gateway_token()
            logger.info(f"✅ 成功获取数据库信息查询访问令牌")
        except Exception as e:
            logger.info(f"❌ 获取数据库信息查询访问令牌失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取数据库信息查询访问令牌失败: {str(e)}"
            }
        
        
        
        # 构建请求参数
        params = {
            "traceId": trace_id,
            "time": time,
            "access_token": token
        }
        
        logger.info(f"📡 发送数据库信息查询请求: {base_url}")
        logger.info(f"📋 请求参数: traceId={trace_id}, time={time}")
        
        # 发送API请求
        response = requests.get(base_url, params=params, timeout=30)
        
        if response.status_code != 200:
            error_msg = f"数据库信息查询API请求失败，状态码: {response.status_code}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
        
        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            error_msg = f"数据库信息查询API响应解析失败: {str(e)}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
        
        # 检查API响应状态
        if not result.get("success", False):
            error_msg = result.get("message", "数据库信息查询API返回失败状态")
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
        
        data = result.get("data", [])
        if not data:
            logger.info("⚠️ 获取的数据库信息为空")
        else:
            logger.info(f"✅ 成功获取数据库信息")
        return {
            "success": True,
            "data": result.get("data", [])
        }
        
    except requests.exceptions.RequestException as e:
        error_msg = f"数据库信息查询网络请求异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"获取数据库信息时发生异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }


def _get_table_structure_info(ip: str, port: str, database_name: str, db_type: str) -> Dict[str, Any]:
    """获取数据库表结构信息"""
    base_url = os.getenv("TABLE_STRUCTRUE_INFO_URL", "")
    try:
        # 获取访问令牌
        try:
            token = get_api_prod_gateway_token()
            logger.info(f"✅ 成功获取访问令牌")
        except Exception as e:
            logger.info(f"❌ 获取访问令牌失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取访问令牌失败: {str(e)}"
            }
        
        
        # 构建请求参数
        params = {
            "access_token": token
        }
        
        # 构建请求体
        request_data = {
            "ip": ip,
            "port": port,
            "databaseName": database_name,
            "dbType": db_type
        }
        
        logger.info(f"📡 发送表结构查询请求: {base_url}")
        logger.info(f"📋 请求数据: ip={ip}, port={port}, databaseName={database_name}, dbType={db_type}")
        
        # 发送API请求
        headers = {
            'Content-Type': 'application/json'
        }
        
        response = requests.post(base_url, params=params, headers=headers, json=request_data, timeout=60)
        
        if response.status_code != 200:
            error_msg = f"表结构查询API请求失败，状态码: {response.status_code}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
        
        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            error_msg = f"表结构查询API响应解析失败: {str(e)}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
        
        # 检查API响应状态
        if result.get("code") != 200:
            error_msg = f"表结构查询API返回错误状态码: {result.get('code', 'Unknown')}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
        
        aaData = result.get("aaData", [])
        logger.info(f"✅ 成功获取表结构信息，共 {len(aaData)} 个表")
        return {
            "success": True,
            "data": aaData
        }
        
    except requests.exceptions.RequestException as e:
        error_msg = f"表结构查询网络请求异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"获取表结构信息时发生异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }


def _generate_index_summary(table_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成表的索引摘要信息"""
    index_list = table_data.get("indexList", [])
    
    primary_indexes = []
    unique_indexes = []
    normal_indexes = []
    index_details = []
    
    for index_info in index_list:
        index_name = index_info.get("indexName", "")
        non_unique = index_info.get("nonUnique", "").lower()
        non_primary = index_info.get("nonPrimary", "").lower()
        column_list = index_info.get("columnList", "")
        
        # 解析索引类型
        is_primary = non_primary == "true"  # nonPrimary为true表示是主键
        is_unique = non_unique == "true"    # nonUnique为true表示是唯一索引
        
        # 解析列列表
        columns = [col.strip() for col in column_list.split(",") if col.strip()] if column_list else []
        
        # 分类索引
        if is_primary:
            index_type = "PRIMARY"
            primary_indexes.append(index_name)
        elif is_unique:
            index_type = "UNIQUE"
            unique_indexes.append(index_name)
        else:
            index_type = "NORMAL"
            normal_indexes.append(index_name)
        
        # 添加详细信息
        index_details.append({
            "index_name": index_name,
            "index_type": index_type,
            "columns": columns,
            "is_unique": is_unique,
            "is_primary": is_primary
        })
    
    return {
        "total_indexes": len(index_list),
        "primary_indexes": primary_indexes,
        "unique_indexes": unique_indexes,
        "normal_indexes": normal_indexes,
        "index_details": index_details
    }


def _format_time_from_context(start_time: str) -> str:
    """将上下文中的时间格式转换为API要求的格式
    
    Args:
        start_time: 上下文中的时间字符串，可能的格式包括：
                   - "20250613_150500" 
                   - "2025-06-13 15:05:00"
                   - "2025-06-13T15:05:00"
                   等
    
    Returns:
        格式化后的时间字符串：YYYY-MM-DD HH:MM:SS
    """
    if not start_time:
        return ""
    
    # 清理输入
    time_str = start_time.strip()
    
    # 如果已经是目标格式，直接返回
    if len(time_str) == 19 and time_str[10] == ' ' and time_str.count('-') == 2 and time_str.count(':') == 2:
        return time_str
    
    # 处理格式：20250613_150500
    if len(time_str) >= 15 and '_' in time_str:
        try:
            date_part, time_part = time_str.split('_')
            if len(date_part) == 8 and len(time_part) >= 6:
                year = date_part[:4]
                month = date_part[4:6]
                day = date_part[6:8]
                hour = time_part[:2]
                minute = time_part[2:4]
                second = time_part[4:6]
                return f"{year}-{month}-{day} {hour}:{minute}:{second}"
        except Exception:
            pass
    
    # 处理格式：2025-06-13T15:05:00
    if 'T' in time_str:
        try:
            time_str = time_str.replace('T', ' ')
            # 去掉可能的毫秒和时区信息
            if '.' in time_str:
                time_str = time_str.split('.')[0]
            if '+' in time_str:
                time_str = time_str.split('+')[0]
            if 'Z' in time_str:
                time_str = time_str.replace('Z', '')
            return time_str.strip()
        except Exception:
            pass
    
    # 处理其他可能的格式
    try:
        import re
        # 尝试提取数字部分
        numbers = re.findall(r'\d+', time_str)
        if len(numbers) >= 6:
            year = numbers[0] if len(numbers[0]) == 4 else f"20{numbers[0]}"
            month = numbers[1].zfill(2)
            day = numbers[2].zfill(2)
            hour = numbers[3].zfill(2)
            minute = numbers[4].zfill(2)
            second = numbers[5].zfill(2)
            return f"{year}-{month}-{day} {hour}:{minute}:{second}"
    except Exception:
        pass
    
    # 如果无法解析，记录警告并返回原始值
    logger.info(f"⚠️ 无法解析时间格式: {start_time}，使用原始值")
    return time_str 


def _generate_index_summary_text(index_summary_results: Dict[str, Any]) -> str:
    """生成索引摘要的文本形式"""
    if not index_summary_results:
        return "未找到任何表的索引信息"
    
    summary_lines = []
    summary_lines.append("📊 索引摘要信息:")
    
    for table_name, summary in index_summary_results.items():
        summary_lines.append(f"\n表 '{table_name}' 的索引信息:")
        summary_lines.append(f"  - 总索引数: {summary.get('total_indexes', 0)}")
        summary_lines.append(f"  - 主键索引: {summary.get('primary_indexes', [])}")
        summary_lines.append(f"  - 唯一索引: {summary.get('unique_indexes', [])}")
        summary_lines.append(f"  - 普通索引: {summary.get('normal_indexes', [])}")
        
        # 显示详细索引信息
        index_details = summary.get("index_details", [])
        if index_details:
            summary_lines.append(f"  - 索引详情:")
            for idx in index_details:
                columns_text = ', '.join(idx.get('columns', []))
                summary_lines.append(f"    * {idx.get('index_name')} ({idx.get('index_type')}): {columns_text}")
    
    return '\n'.join(summary_lines) 