import os
import re
import json
import requests
from typing import Dict, Any, List, Optional
from pathlib import Path
from langchain_core.tools import tool
from models.params import AppCodeSearchParams
from utils.api_prod_gateway import ucg_config
from utils.get_token import get_api_prod_gateway_token
from utils.git_manager import AsyncGitManager
from utils.logger import logger
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def query_git_info_by_app_code(app_code: str, env: str = "prod") -> dict:
    """
    根据服务编号查询Git仓库信息
    
    Args:
        app_code: 服务编号
        env: 环境信息 (daily/pre/prod)prod
        
    Returns:
        包含Git仓库信息的字典
    """
    try:
        logger.info(f"🔍 查询服务编号 {app_code} 的Git仓库信息...")
        logger.info(f"📍 环境: {env}")
        
        # 获取Git查询专用访问令牌
        try:
            token = get_api_prod_gateway_token()
            logger.info(f"✅ 成功获取Git查询访问令牌")
        except Exception as e:
            logger.info(f"❌ 获取Git查询访问令牌失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取Git查询访问令牌失败: {str(e)}",
                "app_code": app_code
            }
        
        # 构建API请求URL - 使用固定的Git查询接口路径
        git_query_url = "/JT/BA/BA-0208/009/DEFAULT/queryAppLastBuild"
        base_url = f"{ucg_config['host']}{git_query_url}"
        
        # 构建请求参数
        params = {
            "appCode": app_code,
            "env": env,
            "access_token": token
        }
        
        logger.info(f"📡 发送Git查询请求: {base_url}")
        logger.info(f"📋 请求参数: appCode={app_code}, env={env}")
        
        # 发送API请求
        response = requests.get(base_url, params=params, timeout=30)
        
        if response.status_code != 200:
            error_msg = f"Git查询API请求失败，状态码: {response.status_code}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code
            }
        
        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            error_msg = f"Git查询API响应解析失败: {str(e)}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code
            }
        
        # 检查API响应状态
        if not result.get("success", False):
            error_msg = result.get("message", "Git查询API返回失败状态")
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code
            }
        
        # 提取仓库信息
        repo_list = result.get("data", {}).get("list", [])
        
        # 转换为标准格式
        repositories = []
        for repo_data in repo_list:
            # 只包含有效的仓库（有repoUrl的）
            repo_url = repo_data.get("repoUrl")
            if repo_url:
                repositories.append({
                    "id": repo_data.get("id"),
                    "name": repo_data.get("applicationName"),
                    "url": repo_url,
                    "last_build_branch": repo_data.get("lastBuildBranch"),
                    "last_success_build_branch": repo_data.get("lastSuccessBuildBranch"),
                    "last_build_date": repo_data.get("lastBuildDate"),
                    "last_success_build_date": repo_data.get("lastSuccessBuildDate")
                })
        
        logger.info(f"✅ 成功查询到 {len(repositories)} 个有效Git仓库")
        
        return {
            "success": True,
            "app_code": app_code,
            "env": env,
            "repositories": repositories,
            "total_count": len(repositories)
        }
        
    except requests.exceptions.RequestException as e:
        error_msg = f"Git查询网络请求异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code
        }
    except Exception as e:
        error_msg = f"查询Git信息时发生异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code
        }


@tool(args_schema=AppCodeSearchParams)
def search_code_by_app_code(
    app_code: str,
    file_name: str = "",
    server_url: str = "",
    sql_fragment: str = "",
    keyword: str = "",
    error_line_number: int = None,
    context: dict = None
) -> dict:
    """根据服务编号拉取代码并在对应的代码库中搜索相关代码文件，并提取代码上下文
    
    工作流程：
    1. 根据服务编号查询Git仓库信息
    2. 拉取所有相关代码到app_code目录
    3. 在app_code目录内实施搜索策略
    4. 提取并返回代码上下文
    
    Args:
        app_code: 服务编号，用于查询和定位对应的代码库
        file_name: 要搜索的文件名
        server_url: 服务接口URL，如/api/v1/user/list，用于查找Controller或Handler中的接口定义
        sql_fragment: SQL片段
        keyword: 关键字搜索
        error_line_number: 报错行数，如果提供则返回该行附近的内容（上下各5行）
        context: 上下文信息
    
    Returns:
        包含代码拉取和搜索结果的字典，结构如下：
        {
            "success": bool,                    # 整体执行是否成功
            "app_code": str,                    # 输入的服务编号
            "error": str,                       # 错误信息（仅在失败时存在）
            "step": str,                        # 失败时的步骤（仅在失败时存在）
            
            # Git仓库信息（成功时包含）
            "git_info": {
                "success": bool,                # Git信息查询是否成功
                "app_code": str,                # 服务编号
                "env": str,                     # 环境（daily/pre/prod）
                "repositories": [               # 仓库列表
                    {
                        "id": str,              # 仓库ID
                        "name": str,            # 仓库名称
                        "url": str,             # Git仓库URL
                        "last_build_branch": str,           # 最后构建分支
                        "last_success_build_branch": str,   # 最后成功构建分支
                        "last_build_date": str,             # 最后构建日期
                        "last_success_build_date": str      # 最后成功构建日期
                    }
                ],
                "total_count": int              # 仓库总数
            },
            
            # 代码克隆结果（成功时包含）
            "clone_results": {
                "successful": [str],            # 成功克隆的仓库名称列表
                "failed": [                     # 失败的仓库信息列表
                    {
                        "repo_name": str,       # 仓库名称
                        "error": str            # 失败原因
                    }
                ]
            },
            
            # 本地仓库列表（成功时包含）
            "repositories": [str],              # 实际存在的本地仓库目录名称列表
            
            # 搜索总结（成功时包含）
            "search_summary": {
                "total_search_types": int,      # 执行的搜索类型数量
                "total_matches": int,           # 总匹配数
                "total_files": int              # 涉及的文件数
            },
            
            # 详细搜索结果（成功时包含）
            "search_results": {
                # 关键字搜索结果（如果执行了关键字搜索）
                "keyword_search": {
                    "success": bool,            # 搜索是否成功
                    "keyword": str,             # 搜索关键字
                    "total_matches": int,       # 匹配总数
                    "matches": [                # 匹配结果列表
                        {
                            "repo": str,        # 仓库名称
                            "file": str,        # 文件相对路径
                            "line": int,        # 行号
                            "content": str,     # 匹配的内容
                            "match": str        # 匹配的关键字
                        }
                    ]
                },
                
                # 文件名搜索结果（如果执行了文件名搜索）
                "file_search": {
                    "success": bool,            # 搜索是否成功
                    "keyword": str,             # 搜索的文件名
                    "total_matches": int,       # 匹配总数
                    "matches": [                # 匹配结果列表
                        {
                            "repo": str,        # 仓库名称
                            "file": str,        # 文件相对路径
                            "line": int,        # 行号（固定为1）
                            "content": str,     # 匹配说明
                            "match": str        # 匹配的文件名
                        }
                    ]
                },
                
                # 服务接口URL搜索结果（如果执行了URL搜索）
                "server_url_search": {
                    "success": bool,            # 搜索是否成功
                    "keyword": str,             # 搜索的URL
                    "total_matches": int,       # 匹配总数
                    "matches": [                # 匹配结果列表
                        {
                            "repo": str,        # 仓库名称
                            "file": str,        # 文件相对路径
                            "line": int,        # 行号（固定为1）
                            "content": str,     # 匹配说明
                            "match": str        # 匹配的URL
                        }
                    ]
                },
                
                # URL部分搜索结果（如果执行了URL部分搜索）
                "url_part_{部分名称}": {
                    "success": bool,            # 搜索是否成功
                    "keyword": str,             # 搜索的URL部分
                    "total_matches": int,       # 匹配总数
                    "matches": [...]            # 匹配结果列表（结构同上）
                },
                
                # SQL片段搜索结果（如果执行了SQL搜索）
                "sql_search": {
                    "success": bool,            # 搜索是否成功
                    "keyword": str,             # 搜索的SQL片段
                    "total_matches": int,       # 匹配总数
                    "matches": [                # 匹配结果列表
                        {
                            "repo": str,        # 仓库名称
                            "file": str,        # 文件相对路径
                            "line": int,        # 行号（固定为1）
                            "content": str,     # 匹配说明（包含SQL关键词）
                            "match": str        # 原始SQL片段
                        }
                    ]
                },
                
                # SQL关键字搜索结果（如果执行了SQL关键字搜索）
                "sql_keyword_{关键字}": {
                    "success": bool,            # 搜索是否成功
                    "keyword": str,             # 搜索的SQL关键字
                    "total_matches": int,       # 匹配总数
                    "matches": [...]            # 匹配结果列表（结构同上）
                }
            },
            
            # 找到的文件列表（成功时包含）
            "found_files": [str],               # 所有搜索中涉及的文件路径列表（去重）
            
            # 代码上下文（成功时包含）
            "code_context": {
                "{搜索类型}_file_{序号}_{文件路径}": str,  # 键：搜索类型+文件信息，值：代码内容
                # 例如：
                # "keyword_search_file_1_src/main/java/Controller.java": "// 文件: src/main/java/Controller.java\n// 行号: 25\npublic class UserController {...}"
            }
        }
        
        注意事项：
        1. 失败时只包含 success=False、error、app_code、step 字段
        2. search_results 中的具体搜索类型取决于提供的参数
        3. code_context 提供了每个匹配文件的详细代码内容
        4. found_files 是所有搜索结果中文件的汇总列表
        5. 所有文件路径都是相对于 app_code 目录的相对路径
    """
    
    logger.info(f"\n==== 开始服务编号 {app_code} 的代码拉取和搜索流程 ====")
    
    # 设置Git工作空间路径
    git_workspace = os.getenv("GIT_WORKSPACE", "code/code_base")
    app_code_path = os.path.join(git_workspace, app_code)
    
    logger.info(f"🎯 服务编号: {app_code}")
    logger.info(f"📁 目标路径: {app_code_path}")
    logger.info(f"🔍 搜索条件:")
    logger.info(f"  - 文件名: {file_name}")
    logger.info(f"  - 服务接口URL: {server_url}")
    logger.info(f"  - SQL片段: {sql_fragment}")
    logger.info(f"  - 关键字: {keyword}")
    
    try:
        # 第一步：根据服务编号查询Git仓库信息
        logger.info(f"\n📡 第一步：查询服务编号 {app_code} 的Git仓库信息...")
        
        # 使用本地的query_git_info_by_app_code方法
        git_info = query_git_info_by_app_code(app_code, env=os.getenv("GIT_INFO_ENV", "prod"))
        
        if not git_info.get("success", False):
            error_msg = f"无法获取服务编号 {app_code} 的Git信息: {git_info.get('error', 'Unknown error')}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "step": "git_info_query",
                "found_files": [],
                "code_context": {}
            }
        
        repositories = git_info.get("repositories", [])
        if not repositories:
            error_msg = f"服务编号 {app_code} 没有关联的Git仓库"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "step": "git_info_query",
                "found_files": [],
                "code_context": {}
            }
        
        logger.info(f"✅ 找到 {len(repositories)} 个Git仓库:")
        for repo in repositories:
            logger.info(f"   - {repo.get('name', 'Unknown')}: {repo.get('url', 'No URL')}")
            if repo.get('last_success_build_branch'):
                logger.info(f"     最后成功构建分支: {repo.get('last_success_build_branch')}")
            elif repo.get('last_build_branch'):
                logger.info(f"     最后构建分支: {repo.get('last_build_branch')}")
        
        # 第二步：拉取所有相关代码到app_code目录
        logger.info(f"\n📥 第二步：拉取代码到目录 {app_code_path}...")
        
        # 确保目标目录存在
        os.makedirs(app_code_path, exist_ok=True)
        
        # 初始化异步Git管理器
        git_manager = AsyncGitManager(workspace_dir=git_workspace)
        
        # 构建克隆任务列表
        clone_tasks = []
        for repo in repositories:
            repo_name = repo.get("name", "")
            repo_url = repo.get("url", "")
            # 优先使用最后成功构建分支，其次使用最后构建分支
            branch = repo.get("last_success_build_branch") or repo.get("last_build_branch") or "master"
            
            if repo_name and repo_url:
                clone_tasks.append({
                    "repo_name": repo_name,
                    "repo_url": repo_url,
                    "target_dir": os.path.join(app_code_path, repo_name),
                    "branch": branch
                })
                logger.info(f"   准备克隆: {repo_name} (分支: {branch})")
        
        if not clone_tasks:
            error_msg = f"没有有效的Git仓库可以克隆"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "step": "git_clone",
                "found_files": [],
                "code_context": {}
            }
        
        logger.info(f"🔄 开始克隆 {len(clone_tasks)} 个仓库...")
        
        # 执行并发克隆
        clone_results = git_manager.clone_repositories_batch(clone_tasks)
        
        # 检查克隆结果
        successful_clones = []
        failed_clones = []
        
        for task, result in zip(clone_tasks, clone_results):
            if result.get("success", False):
                successful_clones.append(task["repo_name"])
                logger.info(f"✅ 成功克隆: {task['repo_name']}")
            else:
                failed_clones.append({
                    "repo_name": task["repo_name"],
                    "error": result.get("error", "Unknown error")
                })
                logger.info(f"❌ 克隆失败: {task['repo_name']} - {result.get('error', 'Unknown error')}")
        
        if not successful_clones:
            error_msg = f"所有仓库克隆都失败了"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "step": "git_clone",
                "clone_results": {
                    "successful": successful_clones,
                    "failed": failed_clones
                },
                "found_files": [],
                "code_context": {}
            }
        
        logger.info(f"🎉 代码拉取完成: {len(successful_clones)} 个仓库成功，{len(failed_clones)} 个失败")
        
        # 第三步：在app_code目录内实施搜索策略
        logger.info(f"\n🔍 第三步：在 {app_code_path} 目录内实施搜索策略...")
        
        # 验证目录结构
        if not os.path.exists(app_code_path):
            error_msg = f"代码目录不存在: {app_code_path}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "step": "directory_validation",
                "found_files": [],
                "code_context": {}
            }
        
        # 检查实际存在的仓库目录
        actual_repositories = [d for d in os.listdir(app_code_path) 
                              if os.path.isdir(os.path.join(app_code_path, d)) and not d.startswith('.')]
        
        if not actual_repositories:
            error_msg = f"代码目录下没有找到任何仓库: {app_code_path}"
            logger.info(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "app_code": app_code,
                "step": "directory_validation",
                "found_files": [],
                "code_context": {}
            }
        
        logger.info(f"✅ 发现 {len(actual_repositories)} 个本地仓库: {', '.join(actual_repositories)}")
        
        # 第四步：执行搜索并提取代码上下文
        logger.info(f"\n🎯 第四步：执行搜索并提取代码上下文...")
        search_results = {}
        total_matches = 0
        
        # 执行优化版搜索策略
        search_results, total_matches = _execute_search_strategies(
            app_code_path, keyword, file_name, server_url, sql_fragment, error_line_number
        )
        
        # 构建代码上下文
        code_context = _build_code_context(search_results, app_code_path)
        
        # 构建文件列表
        found_files = _extract_found_files(search_results)
        
        logger.info(f"\n📊 搜索总结:")
        logger.info(f"  - 执行的搜索类型: {len(search_results)}")
        logger.info(f"  - 总匹配数: {total_matches}")
        logger.info(f"  - 涉及文件数: {len(found_files)}")
        
        result = {
            "success": True,
            "app_code": app_code,
            "git_info": git_info,
            "clone_results": {
                "successful": successful_clones,
                "failed": failed_clones
            },
            "repositories": actual_repositories,
            "search_summary": {
                "total_search_types": len(search_results),
                "total_matches": total_matches,
                "total_files": len(found_files)
            },
            "search_results": search_results,
            "found_files": found_files,
            "code_context": code_context
        }
        
        logger.info(f"✅ 服务编号 {app_code} 的完整流程执行成功")
        logger.info("==== 代码拉取和搜索流程完成 ====\n")
        
        return result
        
    except Exception as e:
        error_msg = f"执行过程中发生异常: {str(e)}"
        logger.info(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "app_code": app_code,
            "step": "execution_error",
            "found_files": [],
            "code_context": {}
        }


def _generate_cascading_urls(server_url):
    """生成级联搜索的URL列表
    
    根据输入的URL，按"/"拆分后生成从完整路径到逐步缩短的URL列表
    例如: /outline/view/change/projectAdjust/getProjectViewList
    生成:
    1. /outline/view/change/projectAdjust/getProjectViewList (完整路径)
    2. /view/change/projectAdjust/getProjectViewList (从第2部分开始)
    3. /change/projectAdjust/getProjectViewList (从第3部分开始)
    4. /projectAdjust/getProjectViewList (从第4部分开始)
    5. /getProjectViewList (从第5部分开始)
    
    Args:
        server_url: 输入的服务接口URL
        
    Returns:
        级联URL信息列表，每个元素包含:
        - url: 不带前导斜杠的URL部分
        - full_url: 带前导斜杠的完整URL
        - priority: 优先级（越小优先级越高）
        - description: 描述信息
    """
    if not server_url or not server_url.strip():
        return []
    
    # 清理URL
    cleaned_url = server_url.strip()
    if cleaned_url.startswith('/'):
        cleaned_url = cleaned_url[1:]
    
    # 如果URL为空，返回空列表
    if not cleaned_url:
        return []
    
    # 按"/"拆分
    parts = cleaned_url.split('/')
    
    # 过滤掉空字符串部分
    parts = [part for part in parts if part.strip()]
    
    if not parts:
        return []
    
    # 生成级联URL列表
    cascading_urls = []
    for i in range(len(parts)):
        partial_url = '/'.join(parts[i:])
        if partial_url:
            cascading_urls.append({
                'url': partial_url,
                'full_url': '/' + partial_url,
                'priority': i + 1,  # 越小优先级越高
                'description': f"完整路径" if i == 0 else f"从第{i+1}部分开始({parts[i]}开始)",
                'start_part': parts[i] if i < len(parts) else ''
            })
    
    return cascading_urls


def _execute_search_strategies(app_code_path, keyword, file_name, server_url, sql_fragment, error_line_number=None):
    """执行搜索策略 - 优化版

    优先级: 文件名 > SQL片段 > URL
    每种搜索只返回一个最匹配的结果
    找到目标后立即返回，不执行后续搜索

    Args:
        app_code_path: 代码路径
        keyword: 关键字
        file_name: 文件名
        server_url: 服务URL
        sql_fragment: SQL片段
        error_line_number: 报错行数，如果提供则返回该行附近的内容
    """
    search_results = {}
    total_matches = 0
    
    logger.info(f"\n🎯 开始执行优化版搜索策略...")
    logger.info(f"   优先级: 文件名 > SQL片段 > URL")
    logger.info(f"   策略: 精确匹配，每种搜索只返回最佳结果")
    
    # 优先级1: 文件名搜索（最高优先级）
    if file_name and file_name.strip():
        logger.info(f"\n📁 [优先级1] 执行文件名精确搜索: {file_name}")
        if error_line_number:
            logger.info(f"   📍 指定报错行数: {error_line_number}，将返回附近内容（上下各5行）")
        else:
            logger.info(f"   📋 未指定行数，将提取类定义和方法定义等关键结构")

        workspace_path = app_code_path
        for root, dirs, files in os.walk(workspace_path):
            for file in files:
                file_lower = file.lower()
                file_name_lower = file_name.lower()
                if file_lower == file_name_lower:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, workspace_path)

                    # 根据是否有报错行数决定返回内容
                    if error_line_number:
                        content_description = f"文件名精确匹配，报错行数 {error_line_number} 附近内容"
                    else:
                        content_description = f"文件名精确匹配，提取关键结构"

                    best_match = {
                        "repo": os.path.basename(os.path.dirname(rel_path)),
                        "file": rel_path,
                        "line": error_line_number if error_line_number else 1,
                        "content": content_description,
                        "match": file_name,
                        "error_line_number": error_line_number
                    }
                    file_search_result = {
                        "success": True,
                        "keyword": file_name,
                        "total_matches": 1,
                        "matches": [best_match],
                        "search_type": "文件名精确搜索",
                        "priority": 1,
                        "has_error_line": bool(error_line_number)
                    }
                    search_results["file_search"] = file_search_result
                    total_matches += 1
                    logger.info(f"✅ 文件名精确搜索找到: {best_match['file']}")
                    logger.info(f"🎯 找到文件名目标，停止后续搜索")
                    return search_results, total_matches
        logger.info(f"❌ 未找到精确匹配的文件名")
    
    # 优先级2: SQL片段搜索（第二优先级）
    if sql_fragment and sql_fragment.strip():
        logger.info(f"\n🗃️ [优先级2] 执行SQL片段搜索: {sql_fragment}")
        
        # 基本清理
        cleaned_sql = sql_fragment.replace('?', '').replace('\n', ' ').replace('\r', ' ')
        cleaned_sql = ' '.join(cleaned_sql.split())
        
        # 提取SQL关键部分
        search_terms = []
        
        # 提取表名 (通常在FROM和JOIN后面)
        table_pattern = re.compile(r'FROM\s+(\w+)|JOIN\s+(\w+)', re.IGNORECASE)
        for match in table_pattern.finditer(cleaned_sql):
            table = match.group(1) if match.group(1) else match.group(2)
            if table and len(table) > 2:
                search_terms.append(table)
        
        # 提取WHERE条件中的字段名和值
        where_pattern = re.compile(r'WHERE\s+(\w+\.\w+|\w+)\s*=\s*[\'"]?(\w+)[\'"]?', re.IGNORECASE)
        for match in where_pattern.finditer(cleaned_sql):
            if match.group(1) and len(match.group(1)) > 3:
                search_terms.append(match.group(1))
            if match.group(2) and len(match.group(2)) > 3 and not match.group(2).isdigit():
                search_terms.append(match.group(2))
        
        # 去重并打印搜索词
        search_terms = list(set([term for term in search_terms if len(term) > 3]))
        logger.info(f"   提取的SQL关键词: {', '.join(search_terms)}")
        
        if not search_terms:
            # 如果没有提取到有效的搜索词，使用SQL中最长的单词
            words = re.findall(r'\b(\w+)\b', cleaned_sql)
            search_terms = sorted([w for w in words if len(w) > 3], key=len, reverse=True)[:3]
            logger.info(f"   使用SQL中最长的词: {', '.join(search_terms)}")
        
        # 搜索包含SQL片段的文件，找出最匹配的一个
        best_match = None
        best_match_score = 0
        
        for root, dirs, files in os.walk(app_code_path):
            for file in files:
                # 只搜索可能包含SQL的文件类型
                if file.endswith(('.xml', '.java', '.sql', '.properties', '.kt', '.scala')):
                    file_path = os.path.join(root, file)
                    try:
                        encodings_to_try = ['utf-8', 'gbk', 'latin-1']
                        file_content = None
                        
                        for encoding in encodings_to_try:
                            try:
                                with open(file_path, 'r', encoding=encoding) as f:
                                    file_content = f.read()
                                    break
                            except UnicodeDecodeError:
                                continue
                        
                        if file_content is None:
                            continue
                        
                        # 计算匹配分数
                        matches = 0
                        for term in search_terms:
                            if term.lower() in file_content.lower():
                                matches += 1
                        
                        if matches > 0:
                            match_score = (matches / len(search_terms)) * 100 if search_terms else 0
                            if match_score > best_match_score:
                                best_match_score = match_score
                                rel_path = os.path.relpath(file_path, app_code_path)
                                best_match = {
                                    "repo": os.path.basename(os.path.dirname(rel_path)),
                                    "file": rel_path,
                                    "line": 1,
                                    "content": f"包含SQL关键词: {', '.join(search_terms)} (匹配度: {match_score:.1f}%)",
                                    "match": sql_fragment,
                                    "match_score": match_score
                                }
                                
                    except Exception as e:
                        logger.info(f"   读取文件时出错: {file_path}, 错误: {str(e)}")
        
        if best_match:
            sql_search_result = {
                "success": True,
                "keyword": sql_fragment,
                "total_matches": 1,
                "matches": [best_match],
                "search_type": "SQL片段搜索",
                "priority": 2
            }
            search_results["sql_search"] = sql_search_result
            total_matches += 1
            logger.info(f"✅ SQL片段搜索找到最佳匹配: {best_match['file']} (匹配度: {best_match['match_score']:.1f}%)")
            logger.info(f"🎯 找到SQL片段目标，停止后续搜索")
            return search_results, total_matches
        else:
            logger.info(f"❌ 未找到匹配的SQL片段")
    
    # 优先级3: 服务接口URL搜索（最低优先级）- 级联搜索
    if server_url and server_url.strip():
        logger.info(f"\n🌐 [优先级3] 执行服务接口URL级联搜索: {server_url}")
        
        # 生成级联搜索的URL列表
        cascading_urls = _generate_cascading_urls(server_url)
        
        if not cascading_urls:
            logger.info(f"❌ 无法生成有效的级联搜索URL列表")
        else:
            logger.info(f"   📋 生成 {len(cascading_urls)} 个级联搜索URL:")
            for i, url_info in enumerate(cascading_urls):
                logger.info(f"      {i+1}. {url_info['description']}: {url_info['full_url']}")
            
            # 按级联顺序搜索，找到第一个匹配就返回
            best_match = None
            best_match_info = None
            
            for url_info in cascading_urls:
                current_url = url_info['url']
                full_url = url_info['full_url']
                priority_level = url_info['priority']
                description = url_info['description']
                
                logger.info(f"\n   🔍 正在搜索 {description}: {full_url}")
                
                # 为当前URL生成搜索模式
                search_patterns = [
                    f'@RequestMapping.*"{full_url}"',
                    f'@GetMapping.*"{full_url}"',
                    f'@PostMapping.*"{full_url}"',
                    f'@PutMapping.*"{full_url}"',
                    f'@DeleteMapping.*"{full_url}"',
                    f'@PatchMapping.*"{full_url}"',
                    # 不带开头斜杠的模式
                    f'@RequestMapping.*"{current_url}"',
                    f'@GetMapping.*"{current_url}"',
                    f'@PostMapping.*"{current_url}"',
                    f'@PutMapping.*"{current_url}"',
                    f'@DeleteMapping.*"{current_url}"',
                    f'@PatchMapping.*"{current_url}"',
                ]
                
                # 在代码文件中搜索这些模式
                found_match = False
                for root, dirs, files in os.walk(app_code_path):
                    if found_match:
                        break
                        
                    for file in files:
                        if found_match:
                            break
                            
                        # 只搜索可能包含Controller的文件类型
                        if file.endswith(('.java', '.kt', '.scala', '.groovy')):
                            file_path = os.path.join(root, file)
                            try:
                                encodings_to_try = ['utf-8', 'gbk', 'latin-1']
                                file_content = None
                                
                                for encoding in encodings_to_try:
                                    try:
                                        with open(file_path, 'r', encoding=encoding) as f:
                                            file_content = f.read()
                                            break
                                    except UnicodeDecodeError:
                                        continue
                                
                                if file_content is None:
                                    continue
                                
                                # 检查是否匹配任何模式
                                pattern_matches = 0
                                matched_patterns = []
                                
                                for pattern in search_patterns:
                                    if re.search(pattern, file_content, re.IGNORECASE):
                                        pattern_matches += 1
                                        matched_patterns.append(pattern)
                                
                                if pattern_matches > 0:
                                    # 计算匹配分数，级联优先级越高分数越高
                                    base_score = (pattern_matches / len(search_patterns)) * 100
                                    priority_bonus = max(0, 100 - (priority_level - 1) * 10)  # 优先级越高奖励越多
                                    
                                    # 如果文件名包含Controller等关键词，提高分数
                                    file_lower = file.lower()
                                    if any(keyword in file_lower for keyword in ['controller', 'handler', 'rest', 'api']):
                                        base_score += 20
                                    
                                    final_score = base_score + priority_bonus
                                    
                                    rel_path = os.path.relpath(file_path, app_code_path)
                                    best_match = {
                                        "repo": os.path.basename(os.path.dirname(rel_path)),
                                        "file": rel_path,
                                        "line": 1,
                                        "content": f"匹配接口定义 ({description}): {full_url} (匹配度: {final_score:.1f}%)",
                                        "match": server_url,
                                        "match_score": final_score,
                                        "matched_url": full_url,
                                        "cascade_level": priority_level,
                                        "matched_patterns": matched_patterns[:3]  # 只保留前3个匹配的模式
                                    }
                                    best_match_info = url_info
                                    found_match = True
                                    logger.info(f"      ✅ 找到匹配: {rel_path} (分数: {final_score:.1f})")
                                    break
                                    
                            except Exception as e:
                                logger.info(f"      ⚠️ 读取文件时出错: {file_path}, 错误: {str(e)}")
                
                # 如果在当前级联级别找到匹配，立即返回
                if found_match:
                    logger.info(f"   🎯 在 {description} 级别找到匹配，停止后续搜索")
                    break
                else:
                    logger.info(f"      ❌ 在此级别未找到匹配")
        
        if best_match:
            url_search_result = {
                "success": True,
                "keyword": server_url,
                "total_matches": 1,
                "matches": [best_match],
                "search_type": "URL级联搜索",
                "priority": 3,
                "cascade_info": {
                    "matched_level": best_match_info['priority'],
                    "matched_url": best_match['matched_url'],
                    "description": best_match_info['description'],
                    "total_levels": len(cascading_urls)
                }
            }
            search_results["server_url_search"] = url_search_result
            total_matches += 1
            logger.info(f"✅ URL级联搜索成功: {best_match['file']}")
            logger.info(f"   📍 匹配级别: {best_match_info['description']}")
            logger.info(f"   🎯 匹配URL: {best_match['matched_url']}")
            logger.info(f"   📊 匹配度: {best_match['match_score']:.1f}%")
            return search_results, total_matches
        else:
            logger.info(f"❌ 所有级联级别都未找到匹配的URL接口")
    
    if total_matches == 0:
        logger.info(f"\n⚠️ 所有搜索策略都未找到匹配结果")
    
    return search_results, total_matches


def _build_code_context(search_results: Dict, app_code_path: str = None) -> Dict[str, str]:
    """构建代码上下文 - 精简版

    根据搜索类型返回不同的内容：
    1. 文件名搜索：如果有报错行数，返回附近5行；否则返回关键结构
    2. SQL搜索：返回文件名+SQL片段[:200]
    3. URL搜索：返回匹配行附近5行内容
    """
    code_context = {}

    logger.info(f"\n📖 开始构建精简代码上下文...")

    for search_type, result in search_results.items():
        if not result.get("success", True):
            continue

        search_type_name = result.get("search_type", search_type)
        priority = result.get("priority", 0)

        logger.info(f"   处理 {search_type_name} 结果 (优先级: {priority})")

        # 处理匹配结果
        if "matches" in result:
            for i, match in enumerate(result["matches"]):
                file_path = match.get('file', '')
                if not file_path:
                    continue

                try:
                    # 获取完整文件路径
                    full_file_path = _get_full_file_path(file_path, app_code_path)

                    if full_file_path and os.path.exists(full_file_path):
                        logger.info(f"     读取文件: {file_path}")

                        # 读取文件内容
                        file_content = _read_file_with_encoding(full_file_path)

                        if file_content is not None:
                            key = f"{search_type}_file_{i+1}_{os.path.basename(file_path)}"

                            # 根据搜索类型决定返回内容
                            if search_type == "file_search":
                                # 文件名搜索：根据是否有报错行数决定返回内容
                                content = _extract_file_content(file_content, match, search_type_name, priority, file_path)
                            elif search_type == "sql_search":
                                # SQL搜索：返回文件名+SQL片段[:200]
                                content = _extract_sql_content(match, search_type_name, priority, file_path)
                            elif search_type == "server_url_search":
                                # URL搜索：返回匹配行附近5行内容
                                content = _extract_url_content(file_content, match, search_type_name, priority, file_path)
                            else:
                                # 其他类型保持原有逻辑
                                content = _extract_default_content(file_content, match, search_type_name, priority, file_path)

                            code_context[key] = content
                            logger.info(f"     ✅ 成功提取内容 ({len(content)} 字符)")

                        else:
                            logger.info(f"     ❌ 无法读取文件内容 (编码问题)")
                            key = f"{search_type}_file_{i+1}_{os.path.basename(file_path)}"
                            code_context[key] = _create_error_content(search_type_name, priority, file_path, "无法读取文件内容，可能是编码问题")

                    else:
                        logger.info(f"     ❌ 文件不存在: {file_path}")
                        key = f"{search_type}_file_{i+1}_{os.path.basename(file_path)}"
                        code_context[key] = _create_error_content(search_type_name, priority, file_path, "文件不存在")

                except Exception as e:
                    logger.info(f"     ❌ 处理文件时出错: {str(e)}")
                    key = f"{search_type}_file_{i+1}_error"
                    code_context[key] = _create_error_content(search_type_name, 0, file_path, f"处理错误: {str(e)}")
        
        # 处理其他类型的结果（为了兼容性保留）
        elif "definitions" in result:
            for i, definition in enumerate(result["definitions"]):
                key = f"{search_type}_definition_{i+1}_{definition.get('file', 'unknown')}"
                context_lines = definition.get("context", [])
                content = definition.get("content", "")
                if context_lines:
                    content += "\n// 上下文:\n" + "\n".join(context_lines[:10])
                code_context[key] = content
                
        elif "sql_statements" in result:
            for i, stmt in enumerate(result["sql_statements"]):
                key = f"{search_type}_sql_{i+1}_{stmt.get('file', 'unknown')}"
                content = f"// 文件: {stmt.get('file', 'unknown')}\n// 行号: {stmt.get('line', 0)}\n{stmt.get('content', '')}"
                if "context" in stmt:
                    context_text = "\n".join(stmt["context"][:10])
                    content += f"\n// 上下文:\n{context_text}"
                code_context[key] = content
                
        elif "api_endpoints" in result:
            for i, endpoint in enumerate(result["api_endpoints"]):
                key = f"{search_type}_api_{i+1}_{endpoint.get('file', 'unknown')}"
                content = f"// 文件: {endpoint.get('file', 'unknown')}\n// 行号: {endpoint.get('line', 0)}\n{endpoint.get('content', '')}"
                if "context" in endpoint:
                    context_text = "\n".join(endpoint["context"][:10])
                    content += f"\n// 上下文:\n{context_text}"
                code_context[key] = content
    
    logger.info(f"   📋 构建完成，共 {len(code_context)} 个代码上下文条目")
    return code_context


def _extract_found_files(search_results: Dict) -> List[str]:
    """提取找到的文件列表"""
    found_files = set()
    
    for search_type, result in search_results.items():
        if not result.get("success", True):
            continue
            
        if "definitions" in result:
            for definition in result["definitions"]:
                found_files.add(definition["file"])
        elif "matches" in result:
            for match in result["matches"]:
                found_files.add(match["file"])
        elif "sql_statements" in result:
            for stmt in result["sql_statements"]:
                found_files.add(stmt["file"])
        elif "api_endpoints" in result:
            for endpoint in result["api_endpoints"]:
                found_files.add(endpoint["file"])
    
    return list(found_files)


def _get_full_file_path(file_path: str, app_code_path: str = None) -> str:
    """获取完整文件路径"""
    # 如果提供了app_code_path，优先使用
    if app_code_path and not os.path.isabs(file_path):
        candidate_path = os.path.join(app_code_path, file_path)
        if os.path.exists(candidate_path):
            return candidate_path

    # 如果没有提供app_code_path或上面没找到，尝试常见路径
    possible_base_paths = [
        "/Users/<USER>/Desktop/project/gcg_project/agent（交互）设计/log_analysis_agent2/git_workspace",
        "git_workspace",
        os.getcwd()
    ]

    for base_path in possible_base_paths:
        if os.path.isabs(file_path):
            candidate_path = file_path
        else:
            candidate_path = os.path.join(base_path, file_path)

        if os.path.exists(candidate_path):
            return candidate_path

    return None


def _read_file_with_encoding(file_path: str) -> str:
    """尝试多种编码读取文件"""
    encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'latin-1']

    for encoding in encodings_to_try:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            continue
        except Exception:
            continue

    return None


def _extract_file_content(file_content: str, match: dict, search_type_name: str, priority: int, file_path: str) -> str:
    """提取文件内容 - 文件名搜索策略"""
    error_line_number = match.get('error_line_number')

    file_info = []
    file_info.append(f"// 搜索类型: {search_type_name}")
    file_info.append(f"// 优先级: {priority}")
    file_info.append(f"// 文件路径: {file_path}")
    file_info.append(f"// 匹配原因: {match.get('content', '未知')}")

    if error_line_number:
        # 有报错行数：返回附近5行内容
        file_info.append(f"// 报错行数: {error_line_number}")
        file_info.append(f"// ===== 报错行附近内容（上下各5行） =====")

        lines = file_content.split('\n')
        start_line = max(0, error_line_number - 6)  # -6 因为行号从1开始
        end_line = min(len(lines), error_line_number + 5)  # +5 因为包含当前行

        context_lines = []
        for i in range(start_line, end_line):
            line_num = i + 1
            prefix = ">>> " if line_num == error_line_number else "    "
            context_lines.append(f"{prefix}{line_num:4d}: {lines[i]}")

        return "\n".join(file_info) + "\n\n" + "\n".join(context_lines)
    else:
        # 没有行数：提取类定义和方法定义等关键结构
        file_info.append(f"// ===== 关键结构提取 =====")

        lines = file_content.split('\n')
        key_structures = []

        # 提取类定义、方法定义、接口定义等
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            line_num = i + 1

            # Java/Kotlin 类定义
            if re.search(r'\b(public|private|protected)?\s*(class|interface|enum)\s+\w+', line_stripped):
                key_structures.append(f"{line_num:4d}: {line}")
            # Java/Kotlin 方法定义
            elif re.search(r'\b(public|private|protected)\s+.*\s+\w+\s*\([^)]*\)\s*\{?', line_stripped):
                key_structures.append(f"{line_num:4d}: {line}")
            # 注解
            elif line_stripped.startswith('@'):
                key_structures.append(f"{line_num:4d}: {line}")
            # 包声明和导入
            elif line_stripped.startswith('package ') or line_stripped.startswith('import '):
                key_structures.append(f"{line_num:4d}: {line}")

        if key_structures:
            return "\n".join(file_info) + "\n\n" + "\n".join(key_structures[:50])  # 限制50行
        else:
            # 如果没有找到关键结构，返回前50行
            first_lines = lines[:50]
            numbered_lines = [f"{i+1:4d}: {line}" for i, line in enumerate(first_lines)]
            return "\n".join(file_info) + "\n\n" + "\n".join(numbered_lines)


def _extract_sql_content(match: dict, search_type_name: str, priority: int, file_path: str) -> str:
    """提取SQL内容 - SQL搜索策略"""
    sql_fragment = match.get('match', '')

    file_info = []
    file_info.append(f"// 搜索类型: {search_type_name}")
    file_info.append(f"// 优先级: {priority}")
    file_info.append(f"// 文件路径: {file_path}")
    file_info.append(f"// 匹配原因: {match.get('content', '未知')}")
    file_info.append(f"// ===== SQL片段内容 =====")

    # 返回文件名 + SQL片段[:200]
    sql_content = sql_fragment[:200]
    if len(sql_fragment) > 200:
        sql_content += "... (截断)"

    return "\n".join(file_info) + "\n\n" + sql_content


def _extract_url_content(file_content: str, match: dict, search_type_name: str, priority: int, file_path: str) -> str:
    """提取URL内容 - URL搜索策略"""
    matched_url = match.get('matched_url', '')

    file_info = []
    file_info.append(f"// 搜索类型: {search_type_name}")
    file_info.append(f"// 优先级: {priority}")
    file_info.append(f"// 文件路径: {file_path}")
    file_info.append(f"// 匹配原因: {match.get('content', '未知')}")
    file_info.append(f"// 匹配URL: {matched_url}")
    file_info.append(f"// ===== 匹配行附近内容（上下各5行） =====")

    lines = file_content.split('\n')

    # 查找包含URL的行
    target_line = None
    for i, line in enumerate(lines):
        if matched_url.replace('/', '') in line.replace('/', ''):
            target_line = i + 1  # 行号从1开始
            break

    if target_line:
        start_line = max(0, target_line - 6)  # -6 因为行号从1开始
        end_line = min(len(lines), target_line + 5)  # +5 因为包含当前行

        context_lines = []
        for i in range(start_line, end_line):
            line_num = i + 1
            prefix = ">>> " if line_num == target_line else "    "
            context_lines.append(f"{prefix}{line_num:4d}: {lines[i]}")

        return "\n".join(file_info) + "\n\n" + "\n".join(context_lines)
    else:
        # 如果没有找到具体行，返回前20行
        first_lines = lines[:20]
        numbered_lines = [f"{i+1:4d}: {line}" for i, line in enumerate(first_lines)]
        return "\n".join(file_info) + "\n\n" + "\n".join(numbered_lines)


def _extract_default_content(file_content: str, match: dict, search_type_name: str, priority: int, file_path: str) -> str:
    """提取默认内容 - 其他搜索策略"""
    file_info = []
    file_info.append(f"// 搜索类型: {search_type_name}")
    file_info.append(f"// 优先级: {priority}")
    file_info.append(f"// 文件路径: {file_path}")
    file_info.append(f"// 匹配原因: {match.get('content', '未知')}")
    if 'match_score' in match:
        file_info.append(f"// 匹配度: {match['match_score']:.1f}%")
    file_info.append(f"// ===== 文件内容（前100行） =====")

    lines = file_content.split('\n')
    first_lines = lines[:100]  # 限制100行
    numbered_lines = [f"{i+1:4d}: {line}" for i, line in enumerate(first_lines)]

    return "\n".join(file_info) + "\n\n" + "\n".join(numbered_lines)


def _create_error_content(search_type_name: str, priority: int, file_path: str, error_msg: str) -> str:
    """创建错误内容"""
    return f"""// 搜索类型: {search_type_name}
// 优先级: {priority}
// 文件路径: {file_path}
// 错误: {error_msg}

无法读取文件内容。请检查文件路径和权限。"""