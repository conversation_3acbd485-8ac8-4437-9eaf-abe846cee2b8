import re

# ---------- 规则 ----------
RULES = [
    ("loop",      lambda src: bool(re.search(r'\b(for|while)\b', src, re.I))),
    ("io",        lambda src: bool(re.search(
        r'(jdbcTemplate|query|execute|http|feign|restTemplate|okhttp|mapper\.|dao\.|repository\.|sleep\.)', src, re.I))),
    ("sync",      lambda src: bool(re.search(r'\bsynchronized\b|\.lock\(\)', src, re.I))),
    ("big_alloc", lambda src: bool(re.search(r'new\s+(byte|int|ArrayList)\s*\[\s*\d{5,}', src))),
    ("recursion", lambda src: bool(re.search(r'\bfindAll\s*\(', src))),  # 示例
    ("exception", lambda src: bool(re.search(r'\b(try|catch|exception|throw)\b', src, re.I))),
]

# ---------- 抠方法体 ----------
def _extract_body(java_snippet: str) -> str:
    """
    用正则把第一个 { ... } 区间抠出来，包括花括号本身
    """
    m = re.search(r'\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}', java_snippet, flags=re.S)
    return m.group(0) if m else ""

# ---------- 主过滤 ----------
def filter_method(code: str) -> tuple[str,list[str]]:
    code = code.strip()
    if not code:
        return '',[]

    # 1. 抠方法体
    body = _extract_body(code)
    if not body:
        return code,[]   # 抠不到，保守保留

    # 2. 命中规则？
    hit = [name for name, checker in RULES if checker(body)]
    return code if hit else '',hit  # 返回原始代码和命中规则列表


# ---------- 测试 ----------
if __name__ == '__main__':
    no_risk = """
    private String blankNull(String str) {
                           throw new test()
                           return str == null ? "" : str;
                       }
    """
    has_risk = """
    private void getRuleExpResult(String bpmParamType,String resultValue,SysRuleExpResult sysRuleExpResult,String resultKey,JSONObject item){
                          if(ELEVEN.equals(bpmParamType)||TWELVE.equals(bpmParamType)){
                              //节点人员 使用人员code 存储在bpm_result_value
                              resultValue = sysRuleExpResult.getBpmResultValue();
                          }
                          //不合理的双引号改成单引号 为了转化成drl不报错，同时可以解析成JSONArray的格式
                          if(resultKey.indexOf('\"')>-1){
                              resultKey = resultKey.replaceAll("\"","'");
                          }
                          if(resultValue.indexOf('\"')>-1){
                              resultValue = resultValue.replaceAll("\"","'");
                          }
                          //bpmParamType = 10
                          String typeName = "";
                          if(TEN.equals(bpmParamType)){
                              typeName = "model";
                          }else if(ELEVEN.equals(bpmParamType)){
                              typeName = "node";
                          }else if(TWELVE.equals(bpmParamType)){
                              typeName = "node_dept";
                          }else if(TWENTY.equals(bpmParamType)){
                              typeName = "variable";
                          }
                          item.put("type",typeName);
                          item.put("key",resultKey);
                          item.put("value",resultValue);
                          if(ELEVEN.equals(bpmParamType)||TWELVE.equals(bpmParamType)){
                              //节点人员姓名 部门名称
                              item.put("name",sysRuleExpResult.getResultValue());
                          }
                      }
    """
    has_loop = """
    public boolean update(SysRule entity) {
                       Assert.notNull(entity, RULE_MESSAGE);
                       entity.setUpdateTime(new Date());
                       return sysRuleDao.updateById(entity) > 0;
    }
    """

    for name, code in [('no_risk', no_risk), ('has_risk', has_risk), ('has_loop', has_loop)]:
        code,hit = filter_method(code)
        print(f"{name} ->", "保留" if code else "丢弃",f"命中规则: {hit}") 