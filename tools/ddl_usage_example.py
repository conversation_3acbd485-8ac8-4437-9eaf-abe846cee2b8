"""
DDL信息获取工具使用示例

展示如何使用 get_table_ddl_info 工具获取数据库表的索引信息
"""
import os
import sys
import os.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.ddl_info_tools import get_table_ddl_info
from utils.logger import logger
import json


def example_get_table_ddl():
    """DDL信息获取工具使用示例"""
    
    logger.info("==== DDL信息获取工具使用示例 ====")
    
    # 示例参数
    table_names = ["task_trigger_record", "sys_jt_user_extra","sys_login_mapping"]  # 要查询的表名列表
    start_time = "2025-07-08 17:29:13.989"  # 开始时间
    app_code = "SA-0214_MSA_CDM"  # 应用编码
    
    # 构建包含trace_id的上下文
    context = {
        "traceid": "b6099aea686745f38eb50c35dde93c23.276.17519639442405265",
        "analysis_id": "test_analysis_001"
    }
    
    logger.info(f"📋 查询参数:")
    logger.info(f"   - TraceID: {context['traceid']}")
    logger.info(f"   - 开始时间: {start_time}")
    logger.info(f"   - 服务编号: {app_code}")
    logger.info(f"   - 表名: {', '.join(table_names)}")
    
    try:
        # 调用工具（新的参数结构）- 使用invoke方法
        result = get_table_ddl_info.invoke({
            "table_names": table_names,
            "start_time": start_time,
            "app_code": app_code,
            "context": context
        })
        
        # 处理结果
        if result.get("success", False):
            logger.info("✅ DDL信息获取成功！")
            
            # 显示数据库信息
            database_info = result.get("database_info", [])
            logger.info(f"\n📊 数据库信息 ({len(database_info)} 个):")
            for db in database_info:
                logger.info(f"   - {db.get('dbName')}: {db.get('spanSonLayer')} at {db.get('peer')}")
            
            # 显示表信息
            table_info = result.get("table_info", {})
            logger.info(f"\n📋 表信息 ({len(table_info)} 个表):")
            for table_name, table_data in table_info.items():
                table_basic = table_data.get("tableInfo", {})
                logger.info(f"   - 表名: {table_basic.get('tableName')}")
                logger.info(f"     数据库: {table_basic.get('dbName')}")
                logger.info(f"     行数: {table_basic.get('tableRows')}")
                logger.info(f"     大小: {table_basic.get('dataSize')}")
            
            # 显示索引摘要（文本形式）
            index_summary_text = result.get("index_summary", "")
            if index_summary_text:
                logger.info(f"\n{index_summary_text}")
            else:
                logger.info(f"\n⚠️ 未获取到索引摘要信息")
            
            # 保存完整结果到文件（示例）
            try:
                output_file = f"ddl_result_{context['traceid'][:8]}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                logger.info(f"\n💾 完整结果已保存到: {output_file}")
            except Exception as e:
                logger.info(f"⚠️ 保存结果文件失败: {str(e)}")
                
        else:
            logger.info("❌ DDL信息获取失败！")
            logger.info(f"   错误: {result.get('error', 'Unknown error')}")
            logger.info(f"   步骤: {result.get('step', 'Unknown step')}")
            
    except Exception as e:
        logger.info(f"❌ 执行示例时发生异常: {str(e)}")
    
    logger.info("==== 示例执行完成 ====")


def example_with_error_locations_context():
    """展示如何在错误位置分析上下文中使用DDL工具"""
    
    logger.info("\n==== 带错误位置上下文的DDL查询示例 ====")
    
    # 模拟从错误位置分析步骤传递的完整上下文
    context_with_error_locations = {
        "traceid": "test_trace_123456",
        "sys_code": "TEST_MSA_SYS",
        "error_locations": {
            "issues": [
                {
                    "root_cause": {
                        "origin_point": {
                            "sql": "SELECT * FROM user_table WHERE id = ?",
                            "table_name_list": ["user_table", "order_table"],
                            "app_code": "TEST-001"
                        }
                    }
                }
            ]
        }
    }
    
    # 时间参数和服务编号单独设置
    start_time = "20250102_143000"  # 标准格式
    app_code = "TEST-001"  # 服务编号
    
    # 从上下文中提取表名
    error_locations = context_with_error_locations.get("error_locations", {})
    issues = error_locations.get("issues", [])
    
    table_names = []
    for issue in issues:
        origin_point = issue.get("root_cause", {}).get("origin_point", {})
        table_list = origin_point.get("table_name_list", [])
        table_names.extend(table_list)
    
    # 去重
    table_names = list(set(table_names))
    
    if table_names:
        logger.info(f"📋 从错误位置信息中提取到表名: {', '.join(table_names)}")
        logger.info(f"📋 开始时间: {start_time}")
        logger.info(f"📋 服务编号: {app_code}")
        logger.info(f"📋 使用上下文信息调用DDL工具...")
        
        # 实际调用工具（新的参数结构）
        try:
            result = get_table_ddl_info.invoke({
                "table_names": table_names,
                "start_time": start_time,
                "app_code": app_code,
                "context": context_with_error_locations
            })
            
            if result.get("success", False):
                logger.info("✅ DDL信息获取成功！")
                logger.info(f"   查询的表数量: {len(result.get('table_info', {}))}")
                
                # 显示简要的索引信息（文本形式）
                index_summary_text = result.get("index_summary", "")
                if index_summary_text:
                    logger.info(f"\n{index_summary_text}")
            else:
                logger.info(f"❌ DDL信息获取失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.info(f"❌ 调用DDL工具时发生异常: {str(e)}")
        
        logger.info("💡 工具已自动从上下文中提取trace_id信息，时间参数和服务编号直接传递")
    else:
        logger.info("⚠️ 未从错误位置信息中找到表名")
    
    logger.info("==== 上下文示例完成 ====")


def example_time_format_conversion():
    """展示时间格式转换功能"""
    
    logger.info("\n==== 时间格式转换示例 ====")
    
    # 导入模块而不是直接导入私有函数
    import tools.ddl_info_tools as ddl_tools
    
    # 测试不同的时间格式
    test_times = [
        "20250613_150500",           # 标准格式
        "2025-06-13 15:05:00",       # 已经是目标格式
        "2025-06-13T15:05:00",       # ISO格式
        "2025-06-13T15:05:00.123Z",  # 带毫秒和时区
        "25061315050",               # 紧凑格式
    ]
    
    logger.info("📋 时间格式转换测试:")
    for original_time in test_times:
        try:
            formatted_time = ddl_tools._format_time_from_context(original_time)
            logger.info(f"   {original_time:25} -> {formatted_time}")
        except Exception as e:
            logger.info(f"   {original_time:25} -> 错误: {str(e)}")
    
    logger.info("==== 时间格式转换示例完成 =====")


if __name__ == "__main__":
    
    # 运行基本示例
    example_get_table_ddl()
    
    # # 运行上下文示例
    # example_with_error_locations_context()
    
    # 运行时间格式转换示例
    # example_time_format_conversion() 