#!/usr/bin/env python3
"""
search_code_by_app_code 工具测试脚本

测试重构后的AST分析功能，包括：
1. 基本AST分析测试
2. 精确搜索测试
3. 错误处理测试
4. 结果格式验证
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app_code_tools import search_code_by_app_code
from utils.logger import logger
from tools.code_tools import extract_tool_results


class TestAppCodeTools:
    """search_code_by_app_code工具测试类"""
    
    def __init__(self):
        self.test_results = []
        self.success_count = 0
        self.failed_count = 0
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        if success:
            self.success_count += 1
        else:
            self.failed_count += 1
        
        print(f"{status} {test_name}")
        if details:
            print(f"    详情: {details}")
        print()
    
    def test_basic_ast_analysis(self):
        """测试基本AST分析功能"""
        print("🔍 测试1: 基本AST分析功能")
        print("-" * 50)
        
        try:
            # 测试参数 - 使用真实的服务编号
            test_params = {
                "app_code": "de-0506_msa_ass",
                "server_url": "POST:/checktask/supplierSubmit"
            }
            
            print(f"测试参数: {test_params}")
            
            # 调用函数 - 使用正确的invoke方法
            result = search_code_by_app_code.invoke(test_params)
            
            # 验证结果结构
            if not isinstance(result, dict):
                self.log_test_result("基本AST分析", False, "返回结果不是字典类型")
                return
            
            # 检查必需字段
            required_fields = ["success", "app_code", "server_url"]
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                self.log_test_result("基本AST分析", False, f"缺少必需字段: {missing_fields}")
                return
            
            # 验证输入参数是否正确保存
            if result["app_code"] != test_params["app_code"]:
                self.log_test_result("基本AST分析", False, "app_code参数保存错误")
                return
            
            if result["server_url"] != test_params["server_url"]:
                self.log_test_result("基本AST分析", False, "server_url参数保存错误")
                return
            
            # 检查成功时的字段结构
            if result.get("success", False):
                success_fields = ["git_info", "clone_results", "ast_analysis", "code_context"]
                present_fields = [field for field in success_fields if field in result]
                
                details = f"成功执行，包含字段: {present_fields}"
                
                # 验证AST分析结果结构
                if "ast_analysis" in result:
                    ast_analysis = result["ast_analysis"]
                    if isinstance(ast_analysis, dict) and "entry_method" in ast_analysis:
                        details += f", 入口方法: {ast_analysis.get('entry_method', 'N/A')}"
                    
                    if "call_chain_summary" in ast_analysis:
                        summary = ast_analysis["call_chain_summary"]
                        details += f", 方法数: {summary.get('total_methods', 0)}, 深度: {summary.get('max_depth', 0)}"
                
                self.log_test_result("基本AST分析", True, details)
            else:
                # 失败时检查错误信息
                error_msg = result.get("error", "未知错误")
                step = result.get("step", "未知步骤")
                details = f"执行失败 - 步骤: {step}, 错误: {error_msg}"
                
                # 某些失败是预期的（比如Git仓库不存在等）
                if step in ["git_info_query", "git_clone", "ast_analysis"]:
                    self.log_test_result("基本AST分析", True, f"预期失败: {details}")
                else:
                    self.log_test_result("基本AST分析", False, details)
                    
        except Exception as e:
            self.log_test_result("基本AST分析", False, f"抛出异常: {str(e)}")
    
    def test_precise_search(self):
        """测试精确搜索功能"""
        print("🎯 测试2: 精确搜索功能")
        print("-" * 50)
        
        try:
            # 测试参数 - 包含精确搜索条件
            test_params = {
                "app_code": "de-0506_msa_ass",
                "server_url": "POST:/checktask/supplierSubmit",
                "class_name": "",
                "method_name": "updToolLedgerVO",
                "file_name": "CheckTaskService.java"
            }
            
            print(f"测试参数: {test_params}")
            
            # 调用函数 - 使用正确的invoke方法
            result = search_code_by_app_code.invoke(test_params)
            
            # 验证基本结构
            if not isinstance(result, dict):
                self.log_test_result("精确搜索", False, "返回结果不是字典类型")
                return
            
            # 检查精确搜索相关字段
            if result.get("success", False):
                details_parts = []
                
                # 检查是否有精确搜索结果
                if "precise_search" in result:
                    precise_search = result["precise_search"]
                    if isinstance(precise_search, dict):
                        search_criteria = precise_search.get("search_criteria", {})
                        matched_nodes = precise_search.get("matched_nodes", [])
                        
                        details_parts.append(f"搜索条件: {search_criteria}")
                        details_parts.append(f"匹配节点数: {len(matched_nodes)}")
                        
                        if precise_search.get("success", False):
                            details_parts.append("精确搜索成功")
                        else:
                            details_parts.append("精确搜索未找到匹配")
                else:
                    details_parts.append("未执行精确搜索（可能AST分析失败）")
                
                details = ", ".join(details_parts)
                self.log_test_result("精确搜索", True, details)
            else:
                # 失败的情况
                error_msg = result.get("error", "未知错误")
                step = result.get("step", "未知步骤")
                details = f"执行失败 - 步骤: {step}, 错误: {error_msg}"
                
                # 在没有真实代码仓库的情况下，这是预期的失败
                if step in ["git_info_query", "git_clone", "ast_analysis"]:
                    self.log_test_result("精确搜索", True, f"预期失败: {details}")
                else:
                    self.log_test_result("精确搜索", False, details)
                    
        except Exception as e:
            self.log_test_result("精确搜索", False, f"抛出异常: {str(e)}")
    
    def test_parameter_validation(self):
        """测试参数验证"""
        print("📋 测试3: 参数验证")
        print("-" * 50)
        
        # 测试用例列表
        test_cases = [
            {
                "name": "缺少app_code",
                "params": {"server_url": "/api/test"},
                "expected_error": True
            },
            {
                "name": "缺少server_url",
                "params": {"app_code": "test-001"},
                "expected_error": True
            },
            {
                "name": "空的server_url",
                "params": {"app_code": "test-001", "server_url": ""},
                "expected_error": True
            },
            {
                "name": "空格的server_url",
                "params": {"app_code": "test-001", "server_url": "   "},
                "expected_error": True
            },
            {
                "name": "有效的基本参数",
                "params": {"app_code": "test-001", "server_url": "/api/test"},
                "expected_error": False
            }
        ]
        
        for case in test_cases:
            try:
                print(f"  测试子用例: {case['name']}")
                
                # 使用正确的invoke方法
                result = search_code_by_app_code.invoke(case["params"])
                
                if case["expected_error"]:
                    # 期望出错，但没有出错
                    if result.get("success", False):
                        self.log_test_result(f"参数验证-{case['name']}", False, "期望参数验证失败，但执行成功")
                    else:
                        # 检查是否是参数验证错误
                        step = result.get("step", "")
                        if step in ["parameter_validation", "ast_module_check"]:
                            self.log_test_result(f"参数验证-{case['name']}", True, f"正确拦截参数错误: {result.get('error', '')}")
                        else:
                            self.log_test_result(f"参数验证-{case['name']}", False, f"错误步骤不是参数验证: {step}")
                else:
                    # 不期望出错
                    if result.get("step") == "parameter_validation":
                        self.log_test_result(f"参数验证-{case['name']}", False, f"参数验证错误: {result.get('error', '')}")
                    else:
                        # 可能因为其他原因失败（如Git仓库不存在），这是可以接受的
                        self.log_test_result(f"参数验证-{case['name']}", True, "参数验证通过")
                        
            except Exception as e:
                if case["expected_error"]:
                    self.log_test_result(f"参数验证-{case['name']}", True, f"正确抛出异常: {str(e)}")
                else:
                    self.log_test_result(f"参数验证-{case['name']}", False, f"意外异常: {str(e)}")
    
    def test_result_format(self):
        """测试返回结果格式"""
        print("📄 测试4: 返回结果格式验证")
        print("-" * 50)
        
        try:
            # 使用标准参数
            test_params = {
                "app_code": "de-0506_msa_ass",
                "server_url": "POST:/checktask/supplierSubmit",
            }
            
            # 使用正确的invoke方法
            result = search_code_by_app_code.invoke(test_params)
            
            # 验证基本类型
            if not isinstance(result, dict):
                self.log_test_result("结果格式验证", False, "结果不是字典类型")
                return
            
            # 验证必需字段类型
            type_checks = [
                ("success", bool),
                ("app_code", str),
                ("server_url", str)
            ]
            
            for field, expected_type in type_checks:
                if field not in result:
                    self.log_test_result("结果格式验证", False, f"缺少字段: {field}")
                    return
                
                if not isinstance(result[field], expected_type):
                    self.log_test_result("结果格式验证", False, f"字段 {field} 类型错误: 期望 {expected_type}, 实际 {type(result[field])}")
                    return
            
            # 验证可选字段结构
            format_details = []
            
            if result.get("success", False):
                # 成功时的字段检查
                optional_fields = {
                    "git_info": dict,
                    "clone_results": dict,
                    "ast_analysis": dict,
                    "code_context": dict,
                    "precise_search": dict
                }
                
                for field, expected_type in optional_fields.items():
                    if field in result:
                        if isinstance(result[field], expected_type):
                            format_details.append(f"{field}: ✅")
                        else:
                            format_details.append(f"{field}: ❌ (类型错误)")
                    else:
                        format_details.append(f"{field}: 未包含")
                        
                # 检查AST分析结果的内部结构
                if "ast_analysis" in result and isinstance(result["ast_analysis"], dict):
                    ast_analysis = result["ast_analysis"]
                    ast_fields = ["entry_method", "call_chain_summary", "method_chain_data"]
                    ast_present = [f for f in ast_fields if f in ast_analysis]
                    format_details.append(f"AST字段: {ast_present}")
                        
            else:
                # 失败时的字段检查
                if "error" in result and isinstance(result["error"], str):
                    format_details.append("error: ✅")
                else:
                    format_details.append("error: ❌")
                    
                if "step" in result and isinstance(result["step"], str):
                    format_details.append("step: ✅")
                else:
                    format_details.append("step: ❌")
            
            details = ", ".join(format_details)
            self.log_test_result("结果格式验证", True, details)
            
        except Exception as e:
            self.log_test_result("结果格式验证", False, f"抛出异常: {str(e)}")
    
    def test_code_tools_result_processing(self):
        """测试code_tools.py的结果整理功能"""
        print("🛠️ 测试5: code_tools结果整理功能")
        print("-" * 50)
        
        try:
            # 首先获取一个真实的工具执行结果
            test_params = {
                "app_code": "de-0506_msa_ass",
                "server_url": "POST:/checktask/supplierSubmit"
            }
            
            print(f"获取测试数据参数: {test_params}")
            raw_result = search_code_by_app_code.invoke(test_params)
            
            # 将原始结果包装成tool_results格式
            tool_results_format = {
                "tool_results": [
                    {
                        "tool": "search_code_by_app_code",
                        "status": "success" if raw_result.get("success", False) else "failed",
                        "arguments": test_params,
                        "result": raw_result,
                        "error": raw_result.get("error") if not raw_result.get("success", False) else None
                    }
                ]
            }
            
            print("调用 extract_tool_results 进行结果整理...")
            
            # 调用结果整理函数
            formatted_result = extract_tool_results(tool_results_format)
            
            # 验证整理结果
            if not isinstance(formatted_result, str):
                self.log_test_result("code_tools结果整理", False, "返回结果不是字符串类型")
                return
            
            if len(formatted_result.strip()) == 0:
                self.log_test_result("code_tools结果整理", False, "返回结果为空")
                return
            
            # 检查结果内容特征
            content_features = []
            
            # 检查是否包含目标信息
            if "查找目标" in formatted_result or "🎯" in formatted_result:
                content_features.append("包含查找目标")
            
            # 检查是否包含AST分析结果
            if "AST方法调用链分析结果" in formatted_result or "🔍" in formatted_result:
                content_features.append("包含AST分析")
            
            # 检查是否包含代码上下文
            if "代码上下文" in formatted_result or "📄" in formatted_result:
                content_features.append("包含代码上下文")
            
            # 检查是否包含服务编号
            if "服务编号" in formatted_result or "📋" in formatted_result:
                content_features.append("包含服务编号")
            
            # 检查是否有错误处理
            if "执行失败" in formatted_result or "❌" in formatted_result:
                content_features.append("包含错误信息")
            
            # 计算格式化结果的统计信息
            lines_count = len(formatted_result.split('\n'))
            char_count = len(formatted_result)
            
            # 验证结果长度合理性
            if char_count > 20000:
                self.log_test_result("code_tools结果整理", False, f"结果过长: {char_count}字符，可能没有正确截断")
                return
            
            details = f"格式化成功 - {lines_count}行, {char_count}字符"
            if content_features:
                details += f", 特征: {', '.join(content_features)}"
            
            # 显示格式化结果的前几行作为示例
            sample_lines = formatted_result.split('\n')[:5]
            sample_text = '\n'.join(sample_lines)
            if len(sample_lines) == 5 and lines_count > 5:
                sample_text += "\n..."
            
            print(f"格式化结果示例:\n{sample_text}")
            print()
            
            self.log_test_result("code_tools结果整理", True, details)
            
        except Exception as e:
            self.log_test_result("code_tools结果整理", False, f"抛出异常: {str(e)}")
    
    def test_code_tools_multiple_results(self):
        """测试code_tools处理多个工具结果的情况"""
        print("🔧 测试6: code_tools多工具结果处理")
        print("-" * 50)
        
        try:
            # 模拟多个工具结果的情况
            multi_tool_results = {
                "tool_results": [
                    {
                        "tool": "search_code_by_app_code",
                        "status": "success",
                        "arguments": {
                            "app_code": "test-service-1",
                            "server_url": "/api/test1"
                        },
                        "result": {
                            "success": True,
                            "app_code": "test-service-1",
                            "server_url": "/api/test1",
                            "ast_analysis": {
                                "entry_method": "TestController.testMethod",
                                "call_chain_summary": {
                                    "total_methods": 5,
                                    "max_depth": 3,
                                    "packages": ["com.test.controller", "com.test.service"]
                                }
                            },
                            "code_context": {
                                "TestController.java": "public class TestController {\n    public String testMethod() {\n        return \"test\";\n    }\n}"
                            }
                        }
                    },
                    {
                        "tool": "search_code_by_app_code",
                        "status": "failed",
                        "arguments": {
                            "app_code": "non-existent",
                            "server_url": "/api/404"
                        },
                        "error": "服务编号不存在"
                    }
                ]
            }
            
            # 调用结果整理函数
            formatted_result = extract_tool_results(multi_tool_results)
            
            # 验证结果
            if not isinstance(formatted_result, str):
                self.log_test_result("多工具结果处理", False, "返回结果不是字符串类型")
                return
            
            # 检查是否包含成功和失败的工具结果
            has_success_content = "TestController" in formatted_result
            has_error_content = "执行失败" in formatted_result and "服务编号不存在" in formatted_result
            
            if has_success_content and has_error_content:
                details = "成功处理混合结果 - 包含成功和失败工具的信息"
            elif has_success_content:
                details = "仅包含成功工具结果"
            elif has_error_content:
                details = "仅包含失败工具结果"
            else:
                details = "内容识别失败"
            
            char_count = len(formatted_result)
            details += f", {char_count}字符"
            
            self.log_test_result("多工具结果处理", True, details)
            
        except Exception as e:
            self.log_test_result("多工具结果处理", False, f"抛出异常: {str(e)}")
    
    def test_code_tools_edge_cases(self):
        """测试code_tools的边缘情况处理"""
        print("⚡ 测试7: code_tools边缘情况处理")
        print("-" * 50)
        
        edge_cases = [
            {
                "name": "空工具结果列表",
                "data": {"tool_results": []},
                "should_handle": True
            },
            {
                "name": "无tool_results字段",
                "data": {"other_field": "value"},
                "should_handle": True
            },
            {
                "name": "超长内容截断测试",
                "data": {
                    "tool_results": [
                        {
                            "tool": "test_tool",
                            "status": "success",
                            "arguments": {},
                            "result": {
                                "success": True,
                                "code_context": {
                                    "LongFile.java": "public class LongFile {\n" + "    // Long content\n" * 1000 + "}"
                                }
                            }
                        }
                    ]
                },
                "should_handle": True
            }
        ]
        
        for case in edge_cases:
            try:
                print(f"  测试边缘情况: {case['name']}")
                
                result = extract_tool_results(case["data"])
                
                if isinstance(result, str):
                    char_count = len(result)
                    # 检查是否正确处理超长内容
                    if case["name"] == "超长内容截断测试":
                        if char_count > 20000:
                            self.log_test_result(f"边缘情况-{case['name']}", False, f"内容过长未截断: {char_count}字符")
                        else:
                            self.log_test_result(f"边缘情况-{case['name']}", True, f"正确截断内容: {char_count}字符")
                    else:
                        self.log_test_result(f"边缘情况-{case['name']}", True, f"正确处理: {char_count}字符")
                else:
                    self.log_test_result(f"边缘情况-{case['name']}", False, "返回结果类型错误")
                    
            except Exception as e:
                if case["should_handle"]:
                    self.log_test_result(f"边缘情况-{case['name']}", False, f"应该处理但抛出异常: {str(e)}")
                else:
                    self.log_test_result(f"边缘情况-{case['name']}", True, f"正确拒绝: {str(e)}")
    
    def test_edge_cases(self):
        """测试边缘情况"""
        print("⚠️ 测试5: 边缘情况处理")
        print("-" * 50)
        
        edge_cases = [
            {
                "name": "特殊字符URL",
                "params": {"app_code": "edge-001", "server_url": "/api/test-url_with@special#chars"},
                "should_handle": True
            },
            {
                "name": "很长的URL",
                "params": {"app_code": "edge-002", "server_url": "/api/" + "very-long-path/" * 20 + "endpoint"},
                "should_handle": True
            },
            {
                "name": "中文服务编号",
                "params": {"app_code": "测试服务-001", "server_url": "/api/test"},
                "should_handle": True
            },
            {
                "name": "只有精确搜索参数",
                "params": {"app_code": "edge-003", "server_url": "/api/test", "class_name": "TestClass"},
                "should_handle": True
            }
        ]
        
        for case in edge_cases:
            try:
                print(f"  测试边缘情况: {case['name']}")
                
                # 使用正确的invoke方法
                result = search_code_by_app_code.invoke(case["params"])
                
                if isinstance(result, dict) and "success" in result:
                    if case["should_handle"]:
                        self.log_test_result(f"边缘情况-{case['name']}", True, "正确处理边缘情况")
                    else:
                        self.log_test_result(f"边缘情况-{case['name']}", False, "应该拒绝但接受了请求")
                else:
                    self.log_test_result(f"边缘情况-{case['name']}", False, "返回结果格式异常")
                    
            except Exception as e:
                if case["should_handle"]:
                    self.log_test_result(f"边缘情况-{case['name']}", False, f"应该处理但抛出异常: {str(e)}")
                else:
                    self.log_test_result(f"边缘情况-{case['name']}", True, f"正确拒绝边缘情况: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始执行 search_code_by_app_code 工具测试")
        print("=" * 80)
        print()
        
        start_time = time.time()
        
        # 执行所有测试
        # self.test_basic_ast_analysis()
        # self.test_precise_search()
        # self.test_parameter_validation()
        self.test_result_format()
        self.test_code_tools_result_processing()
        self.test_code_tools_multiple_results()
        self.test_code_tools_edge_cases()
        # self.test_edge_cases()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出测试总结
        print("📊 测试结果总结")
        print("=" * 80)
        print(f"总测试数: {len(self.test_results)}")
        print(f"成功: {self.success_count} ✅")
        print(f"失败: {self.failed_count} ❌")
        print(f"成功率: {(self.success_count / len(self.test_results) * 100):.1f}%")
        print(f"执行时间: {duration:.2f}秒")
        print()
        
        # 输出失败的测试详情
        if self.failed_count > 0:
            print("❌ 失败的测试:")
            print("-" * 40)
            for result in self.test_results:
                if not result["success"]:
                    print(f"  • {result['test_name']}: {result['details']}")
            print()
        
        # 保存测试结果到文件
        self.save_test_results()
        
        return self.failed_count == 0
    
    def save_test_results(self):
        """保存测试结果到文件"""
        try:
            results_file = "test_app_code_tools_results.json"
            test_summary = {
                "timestamp": time.time(),
                "total_tests": len(self.test_results),
                "success_count": self.success_count,
                "failed_count": self.failed_count,
                "success_rate": self.success_count / len(self.test_results) * 100 if self.test_results else 0,
                "test_details": self.test_results
            }
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, ensure_ascii=False, indent=2)
            
            print(f"📁 测试结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {str(e)}")


def main():
    """主函数"""
    try:
        # 创建测试实例
        tester = TestAppCodeTools()
        
        # 运行所有测试
        success = tester.run_all_tests()
        
        # 退出代码
        exit_code = 0 if success else 1
        
        if success:
            print("🎉 所有测试通过！")
        else:
            print("💥 部分测试失败，请检查详细信息")
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试执行异常: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 