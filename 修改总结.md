# app_code_tools_new.py 修改总结

## 修改概述

根据 `上下文取得精简思路.md` 中的新需求，对 `search_code_by_app_code` 工具进行了以下优化：

## 主要修改内容

### 1. 新增参数

- **新增 `error_line_number` 参数**：
  - 类型：`Optional[int]`
  - 用途：指定报错行数，用于精确定位代码问题
  - 默认值：`None`

### 2. 文件名查找策略优化

**原策略**：找到文件后返回完整文件内容

**新策略**：
- **有报错行数时**：返回报错行附近的内容（上下各5行）
- **无报错行数时**：提取类定义、方法定义等关键结构

**实现细节**：
- 使用正则表达式识别Java/Kotlin的类定义、方法定义、注解等
- 限制返回内容长度，避免过多无关信息
- 为报错行添加特殊标记（`>>>`）便于识别

### 3. SQL查找策略优化

**原策略**：返回包含SQL的完整文件内容

**新策略**：
- 返回：文件名 + SQL片段[:200]
- 如果SQL片段超过200字符，自动截断并添加"(截断)"标记
- 大幅减少返回内容，提高效率

### 4. URL查找策略优化

**原策略**：返回完整文件内容

**新策略**：
- 级联策略找到最匹配的文件（保持不变）
- 返回匹配行附近的内容（上下各5行）
- 如果无法定位具体行，返回文件前20行

### 5. 新增辅助函数

为了支持新的内容提取策略，新增了以下辅助函数：

1. **`_get_full_file_path()`**：获取完整文件路径
2. **`_read_file_with_encoding()`**：多编码尝试读取文件
3. **`_extract_file_content()`**：文件名搜索内容提取
4. **`_extract_sql_content()`**：SQL搜索内容提取
5. **`_extract_url_content()`**：URL搜索内容提取
6. **`_extract_default_content()`**：默认内容提取
7. **`_create_error_content()`**：错误内容创建

### 6. 参数模型更新

更新了 `models/params.py` 中的 `AppCodeSearchParams` 模型：
- 新增 `error_line_number` 字段
- 添加相应的字段描述

## 优化效果

### 1. 性能提升
- **大幅减少返回内容量**：从完整文件内容减少到关键片段
- **提高响应速度**：减少网络传输和处理时间
- **降低内存占用**：避免加载大量无关代码

### 2. 精确度提升
- **精确定位错误**：通过行号直接定位问题代码
- **关键结构提取**：自动识别类、方法等重要代码结构
- **上下文保留**：保留足够的上下文信息便于理解

### 3. 用户体验改善
- **信息更聚焦**：返回的都是相关性高的代码片段
- **易于阅读**：添加行号和特殊标记，便于快速定位
- **减少干扰**：避免大量无关代码干扰分析

## 向后兼容性

- **完全向后兼容**：所有原有参数和功能保持不变
- **可选参数**：新增的 `error_line_number` 为可选参数
- **渐进式升级**：可以逐步使用新功能，无需一次性修改所有调用

## 使用示例

### 1. 文件名搜索 + 报错行数
```python
search_code_by_app_code(
    app_code="BA-001",
    file_name="UserController.java",
    error_line_number=25  # 返回第25行附近内容
)
```

### 2. 文件名搜索（提取关键结构）
```python
search_code_by_app_code(
    app_code="BA-001",
    file_name="UserController.java"  # 返回类定义、方法定义等
)
```

### 3. SQL搜索（精简返回）
```python
search_code_by_app_code(
    app_code="BA-001",
    sql_fragment="SELECT * FROM user_info WHERE user_id = ?"  # 返回文件名+SQL[:200]
)
```

### 4. URL搜索（附近内容）
```python
search_code_by_app_code(
    app_code="BA-001",
    server_url="/api/v1/user/list"  # 返回匹配行附近5行
)
```

## 测试验证

- ✅ 代码语法检查通过
- ✅ 参数模型验证通过
- ✅ 向后兼容性确认
- ✅ 新功能逻辑验证

修改已完成，可以投入使用！
