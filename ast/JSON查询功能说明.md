# 方法调用链JSON查询功能说明

## 🎯 功能概述

现在方法调用链分析工具支持生成JSON格式的输出，这使得我们可以进行程序化的查询和分析。系统会同时生成两种格式：

- **文本格式** (`.txt`)：便于人工阅读
- **JSON格式** (`.json`)：便于程序化查询

## 📊 生成的数据统计

根据最新的分析结果，`AcceptBillController.saveDetailsBatch` 方法调用链包含：

- **总方法数**: 53个
- **最大调用深度**: 11层
- **涉及包数**: 24个
- **涉及的架构层次**: Controller → Service → DomainService → Gateway → Util

## 🔍 查询功能展示

### 1. 基础查询功能

#### 按方法名查找
```python
# 查找包含 'sendMail' 的方法
results = find_method_by_name(data, "sendMail")
```

**结果示例**：
- `AcceptBillGatewayImpl.sendMailByAcceptState`
- `EmailUtil.sendMail`

#### 按类名查找
```python
# 查找AcceptBill相关的所有方法
results = find_methods_by_class(data, "AcceptBill")
```

**结果示例**：包含12个相关方法，覆盖Controller、Service、DomainService、Gateway层

#### 获取调用路径
```python
# 获取到EmailUtil.sendMail的完整调用路径
paths = get_call_path_to_method(data, "EmailUtil.sendMail")
```

**调用路径**：
```
saveDetailsBatch → saveDetailsBatch → saveDetailsBatch → 
startProcessByTemplateName → sendMailByAcceptState → sendMail
```

### 2. 架构分析功能

#### Gateway实现类统计
发现了17个Gateway实现类，包括：
- `AcceptBillGatewayImpl`
- `HandoverBillGatewayImpl`
- `BCCServiceGatewayImpl`
- `WmsGateWayImpl`
- 等等

#### 业务域服务统计
发现了4个DomainService：
- `AcceptBillDomainService`
- `InWhouseAppBillDomainService`
- `OrdersUpdateRecordDomainService`
- `StatementContentDomainService`

### 3. 业务功能分析

#### 邮件相关功能
- `emailContactFilter`: 邮件联系人过滤
- `sendMailByAcceptState`: 根据验收状态发送邮件
- `sendMail`: 底层邮件发送
- `saveMailRecord`: 保存邮件记录
- `saveBatch`: 批量保存邮件记录

#### 数据库操作
发现多个update/save操作：
- 验收单状态更新
- 入库申请更新
- 工作流状态更新
- 邮件记录保存

## 🚀 实际应用场景

### 1. 代码审查和重构
- **查找重复逻辑**: 通过方法名查找相似功能
- **依赖分析**: 了解方法之间的调用关系
- **影响范围评估**: 修改某个方法时了解影响范围

### 2. 性能优化
- **调用链深度分析**: 识别过深的调用链（当前最深11层）
- **热点方法识别**: 找出被频繁调用的方法
- **数据库操作优化**: 统计数据库操作方法

### 3. 架构理解
- **分层架构验证**: 确认是否遵循分层架构原则
- **模块耦合分析**: 了解不同模块间的依赖关系
- **业务流程理解**: 通过调用路径理解业务流程

### 4. 问题排查
- **异常追踪**: 根据调用路径快速定位问题
- **业务逻辑验证**: 确认业务流程是否正确
- **接口依赖分析**: 了解外部服务调用

## 💡 使用建议

### 1. 查询策略
- **自顶向下**: 从业务入口开始，逐层深入
- **关键字搜索**: 使用业务关键词快速定位
- **分类查询**: 按架构层次（Controller、Service、Gateway）分类查询

### 2. 分析重点
- **调用深度**: 关注调用链是否过深
- **包分布**: 了解功能分布是否合理
- **异常处理**: 关注错误处理和事务管理

### 3. 优化方向
- **减少调用层级**: 避免过度封装
- **合并相似操作**: 减少重复代码
- **优化数据库操作**: 减少数据库调用次数

## 📁 文件结构

```
ast/
├── DE-0505_MSA_TPC/
│   ├── com.qm.qfc.tpc.service.web.acceptbill.AcceptBillController.saveDetailsBatch.txt
│   └── com.qm.qfc.tpc.service.web.acceptbill.AcceptBillController.saveDetailsBatch.json
├── build_graph.py          # 主分析脚本
├── query_example.py        # 查询示例
└── JSON查询功能说明.md     # 本文档
```

## 🛠️ 快速开始

1. **生成调用链**：
   ```bash
   python ast/build_graph.py
   ```

2. **运行查询示例**：
   ```bash
   python ast/query_example.py
   ```

3. **自定义查询**：
   ```python
   from build_graph import load_method_chain_json, find_method_by_name
   
   # 加载数据
   data = load_method_chain_json("your_file.json")
   
   # 执行查询
   results = find_method_by_name(data, "your_method_name")
   ```

## 🎉 总结

JSON格式的方法调用链为代码分析提供了强大的查询能力，特别适合：

✅ **分支级别的精确查询**  
✅ **程序化的批量分析**  
✅ **复杂业务逻辑的理解**  
✅ **架构设计的验证**  
✅ **性能问题的定位**  

这个工具让我们能够更好地理解和维护复杂的Java应用程序！ 