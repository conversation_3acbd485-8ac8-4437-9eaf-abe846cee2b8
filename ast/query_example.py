#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方法调用链JSON查询示例
演示如何使用build_graph.py中的JSON查询工具
"""

import sys
import os
from pathlib import Path

# 添加当前目录到path，以便导入模块
sys.path.append(str(Path(__file__).parent))

from json_query_tools import (
    load_method_chain_json,
    find_method_by_name,
    find_methods_by_class,
    get_call_path_to_method,
    get_method_tree_at_depth,
    print_method_summary,
    find_node_and_get_subtree,
    print_node_details,
    print_subtree_summary
)

def count_branches(method_chain):
    """统计方法调用链中的分支数量
    
    Args:
        method_chain: 方法调用链数据
    
    Returns:
        dict: 包含分支统计信息的字典
    """
    def count_paths_to_leaves(chain_dict, current_path=[]):
        """递归计算到叶子节点的路径数量"""
        total_branches = 0
        leaf_paths = []
        
        for method_key, method_info in chain_dict.items():
            new_path = current_path + [method_key]
            
            # 检查是否是叶子节点（没有calls或calls为空）
            calls = method_info.get('calls', {})
            if not calls:
                # 这是一个叶子节点
                total_branches += 1
                leaf_paths.append(new_path.copy())
            else:
                # 递归处理子节点
                sub_branches, sub_paths = count_paths_to_leaves(calls, new_path)
                total_branches += sub_branches
                leaf_paths.extend(sub_paths)
        
        return total_branches, leaf_paths
    
    # 提取方法调用链数据
    chain_data = method_chain
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        chain_data = method_chain['method_call_chain']
    
    # 计算分支
    branch_count, all_paths = count_paths_to_leaves(chain_data)
    
    # 计算其他统计信息
    max_depth = max(len(path) for path in all_paths) if all_paths else 0
    min_depth = min(len(path) for path in all_paths) if all_paths else 0
    avg_depth = sum(len(path) for path in all_paths) / len(all_paths) if all_paths else 0
    
    # 统计叶子节点
    leaf_nodes = set()
    for path in all_paths:
        if path:
            leaf_nodes.add(path[-1])
    
    return {
        'total_branches': branch_count,
        'leaf_count': len(leaf_nodes),
        'max_depth': max_depth,
        'min_depth': min_depth,
        'avg_depth': round(avg_depth, 2),
        'all_paths': all_paths,
        'leaf_nodes': leaf_nodes
    }

def demo_node_subtree_query():
    """演示基于类名+方法名+文件名定位节点并获取子树的功能"""
    
    # 使用新的JSON文件路径
    json_file = "ast/DE-0102_MSA_003/com.faw.cloudbom.bommgmt.integration.commons.rpc.BomIntegrationRpc.findBomService.json"
    
    print("\n🎯 基于类名+方法名+文件名定位节点并获取子树")
    print("=" * 80)
    
    # 1. 加载JSON数据
    print("\n📂 1. 加载JSON数据...")
    data = load_method_chain_json(json_file)
    if not data:
        print("❌ 无法加载JSON文件，请确保文件存在")
        return
    
    print("✅ JSON数据加载成功")
    
    # 2. 基于类名查找节点及子树
    print("\n🔍 2. 基于类名查找: 'BomIntegrationServiceImpl'")
    results = find_node_and_get_subtree(data, class_name="BomIntegrationServiceImpl")
    
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print_node_details(result)
        print_subtree_summary(result['subtree_full'])
    
    # 3. 基于方法名查找
    print("\n🔍 3. 基于方法名查找: 'getBomIntegrationResHeader'")
    results = find_node_and_get_subtree(data, method_name="getBomIntegrationResHeader")
    
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print_node_details(result)
        print_subtree_summary(result['subtree_full'])
    
    # 4. 基于文件名查找
    print("\n🔍 4. 基于文件名查找: 'CheckParamUtil.java'")
    results = find_node_and_get_subtree(data, file_name="CheckParamUtil.java")
    
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print_node_details(result)
        print_subtree_summary(result['subtree_full'])
    
    # 5. 组合查找：类名+方法名
    print("\n🔍 5. 组合查找: 类名='AbstractBomIntegrationPlugin' + 方法名='doCheckParams'")
    results = find_node_and_get_subtree(data, 
                                       class_name="AbstractBomIntegrationPlugin", 
                                       method_name="doCheckParams")
    
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print_node_details(result)
        print_subtree_summary(result['subtree_full'])
    
    # 6. 精确匹配示例
    print("\n🔍 6. 精确匹配示例: 类名='BomIntegrationServiceImpl' (精确匹配)")
    results = find_node_and_get_subtree(data, 
                                       class_name="BomIntegrationServiceImpl", 
                                       exact_match=True)
    
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print_node_details(result)
        print_subtree_summary(result['subtree_full'])
    
    # 7. 实际业务场景示例
    print("\n🔍 7. 实际业务场景: 查找参数校验相关的所有子方法")
    results = find_node_and_get_subtree(data, method_name="doCheckParams")
    
    if results:
        result = results[0]  # 取第一个结果
        print(f"\n🎯 从 '{result['method_key'].split('.')[-1]}' 开始的完整参数校验流程:")
        print_node_details(result)
        
        # 展示子树中的所有校验方法
        subtree_info = result['subtree_full']
        if subtree_info['all_methods']:
            print(f"\n📋 包含的校验方法:")
            for method in subtree_info['all_methods']:
                method_name = method['method_key'].split('.')[-1]
                class_name = method['class'].split('.')[-1]
                print(f"   - {method_name} (类: {class_name})")
                
                # 如果是CheckParamUtil的方法，显示其用途
                if 'CheckParamUtil' in method['class']:
                    if 'checkPlantCode' in method_name:
                        print(f"     └─ 校验工厂编码")
                    elif 'checkProjectCode' in method_name:
                        print(f"     └─ 校验项目编码")
                    elif 'checkProductCode' in method_name:
                        print(f"     └─ 校验产品编码")
                    elif 'checkActiveStatus' in method_name:
                        print(f"     └─ 校验BOM生效状态")
    
    print("\n" + "=" * 80)
    print("🎉 节点子树查询演示完成！")

def main():
    """主函数：演示各种JSON查询功能"""
    
    # JSON文件路径 - 更新为新的文件
    json_file = "ast/DE-0102_MSA_003/com.faw.cloudbom.bommgmt.integration.commons.rpc.BomIntegrationRpc.findBomService.json"
    
    print("🔍 方法调用链JSON查询示例")
    print("=" * 60)
    
    # 1. 加载JSON数据
    print("\n📂 1. 加载JSON数据...")
    data = load_method_chain_json(json_file)
    if not data:
        print("❌ 无法加载JSON文件，请确保文件存在")
        return
    
    print("✅ JSON数据加载成功")
    
    # 2. 打印整体摘要
    print("\n📋 2. 方法调用链摘要:")
    print_method_summary(data)
    
    # 3. 新增：统计分支信息
    print("\n🌳 3. 分支统计分析:")
    branch_stats = count_branches(data)
    print(f"   📊 总分支数: {branch_stats['total_branches']}")
    print(f"   🍃 叶子节点数: {branch_stats['leaf_count']}")
    print(f"   📏 最大深度: {branch_stats['max_depth']}")
    print(f"   📏 最小深度: {branch_stats['min_depth']}")
    print(f"   📏 平均深度: {branch_stats['avg_depth']}")
    
    # 显示一些示例分支路径
    print(f"\n   🛤️  示例分支路径 (显示前3条):")
    for i, path in enumerate(branch_stats['all_paths'][:3], 1):
        path_names = [p.split('.')[-1] for p in path]
        print(f"      {i}. {' → '.join(path_names)}")
    
    if len(branch_stats['all_paths']) > 3:
        print(f"      ... 还有 {len(branch_stats['all_paths']) - 3} 条分支路径")
    
    # 显示叶子节点
    print(f"\n   🍃 叶子节点 (前5个):")
    leaf_list = list(branch_stats['leaf_nodes'])
    for i, leaf in enumerate(leaf_list[:5], 1):
        leaf_name = leaf.split('.')[-1]
        leaf_class = leaf.split('.')[-2] if '.' in leaf else "Unknown"
        print(f"      {i}. {leaf_name} (类: {leaf_class})")
    
    if len(leaf_list) > 5:
        print(f"      ... 还有 {len(leaf_list) - 5} 个叶子节点")

    # 4. 按方法名查找 - 更新为新数据中存在的方法
    print("\n🔎 4. 按方法名查找 (包含 'checkPlantCode'):")
    results = find_method_by_name(data, "checkPlantCode")
    for i, result in enumerate(results, 1):
        print(f"   {i}. {result['method_key']}")
        print(f"      调用路径: {' -> '.join(result['path'])}")
    
    # 5. 按类名查找 - 更新为新数据中存在的类
    print("\n🔎 5. 按类名查找 (包含 'BomIntegration'):")
    results = find_methods_by_class(data, "BomIntegration")
    for i, result in enumerate(results, 1):
        print(f"   {i}. {result['method_key']}")
        print(f"      类: {result['method_info']['class']}")
    
    # 6. 获取调用路径 - 更新为新数据中存在的方法
    print("\n🛤️  6. 获取到特定方法的调用路径 ('checkPlantCode'):")
    paths = get_call_path_to_method(data, "checkPlantCode")
    for i, path in enumerate(paths, 1):
        print(f"   路径 {i}: {' -> '.join([p.split('.')[-1] for p in path])}")
    
    # 7. 获取指定深度的调用树
    print("\n🌲 7. 获取深度为2的方法调用树:")
    tree = get_method_tree_at_depth(data, depth=2)
    count = 0
    for method_key, method_info in tree.items():
        count += 1
        if count > 5:  # 只显示前5个
            print("   ... (更多方法)")
            break
        print(f"   {method_key.split('.')[-1]} (类: {method_info['class'].split('.')[-1]})")
    
    # 8. 查找特定业务逻辑 - 更新为新数据中存在的逻辑
    print("\n🏢 8. 查找参数校验相关业务逻辑:")
    check_methods = find_method_by_name(data, "check")
    for result in check_methods:
        method_name = result['method_key'].split('.')[-1]
        class_name = result['method_info']['class'].split('.')[-1]
        print(f"   📋 {method_name} (类: {class_name})")
    
    # 9. 查找插件相关操作
    print("\n🔌 9. 查找插件相关操作 (包含 'Plugin'):")
    plugin_operations = find_methods_by_class(data, "Plugin")
    
    # 去重并显示
    seen = set()
    for result in plugin_operations:
        key = result['method_key']
        if key not in seen:
            seen.add(key)
            method_name = key.split('.')[-1]
            class_name = result['method_info']['class'].split('.')[-1]
            print(f"   🔌 {method_name} (类: {class_name})")
    
    print("\n" + "=" * 60)
    print("🎉 查询示例完成！")
    print(f"💡 提示：可以根据这些示例编写自己的查询逻辑")

def demo_advanced_queries():
    """演示高级查询功能"""
    json_file = "ast/DE-0102_MSA_003/com.faw.cloudbom.bommgmt.integration.commons.rpc.BomIntegrationRpc.findBomService.json"
    data = load_method_chain_json(json_file)
    
    if not data:
        return
    
    print("\n🚀 高级查询示例")
    print("=" * 40)
    
    # 查找所有Plugin实现类
    print("\n1. 查找所有Plugin实现:")
    plugin_methods = find_methods_by_class(data, "Plugin")
    plugin_classes = set()
    for result in plugin_methods:
        plugin_classes.add(result['method_info']['class'])
    
    for cls in sorted(plugin_classes):
        print(f"   🔌 {cls.split('.')[-1]}")
    
    # 查找业务服务类
    print("\n2. 查找业务服务类 (Service):")
    service_methods = find_methods_by_class(data, "Service")
    service_classes = set()
    for result in service_methods:
        service_classes.add(result['method_info']['class'])
    
    for cls in sorted(service_classes):
        print(f"   🏢 {cls.split('.')[-1]}")
    
    # 查找工具类
    print("\n3. 查找工具类 (Util):")
    util_methods = find_methods_by_class(data, "Util")
    util_classes = set()
    for result in util_methods:
        util_classes.add(result['method_info']['class'])
    
    for cls in sorted(util_classes):
        print(f"   🔧 {cls.split('.')[-1]}")

if __name__ == "__main__":
    try:
        main()
        # demo_advanced_queries()
        demo_node_subtree_query()  # 新增的演示
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}") 