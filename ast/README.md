# Java 方法调用链分析工具

## 📁 项目结构

重构后的代码分为以下独立模块：

### 1. 核心模块
- **`build_graph.py`** - 方法调用链分析核心模块
- **`url_to_method_mapper.py`** - URL到方法映射模块
- **`json_query_tools.py`** - JSON查询工具模块

### 2. 示例和文档
- **`query_example.py`** - 查询功能使用示例
- **`JSON查询功能说明.md`** - JSON查询功能详细说明
- **`分支统计功能说明.md`** - 分支统计功能说明

## 🚀 使用方法

### 基本用法（通过URL分析）

```python
from build_graph import analyze_call_chain
from url_to_method_mapper import find_method_by_url

# 1. 通过URL找到入口方法
repo_path = "/path/to/your/java/repo"
request_url = "/AcceptBill/saveDetailsBatch"
entry_method = find_method_by_url(request_url, repo_path)

# 2. 分析方法调用链
if entry_method:
    method_chain = analyze_call_chain(entry_method, repo_path)
    print("✅ 方法调用链分析完成")
```

### 直接使用方法名分析

```python
from build_graph import analyze_call_chain

# 直接使用已知的方法全限定名
entry_method = "com.example.controller.UserController.getUserById"
repo_path = "/path/to/your/java/repo"

method_chain = analyze_call_chain(entry_method, repo_path)
```

### JSON查询功能

```python
from json_query_tools import *

# 加载JSON格式的分析结果
data = load_method_chain_json("ast/repo_name/method_name.json")

# 查找特定方法
results = find_method_by_name(data, "saveDetailsBatch")

# 按类名查找
results = find_methods_by_class(data, "AcceptBill")

# 获取调用路径
paths = get_call_path_to_method(data, "sendMail")

# 打印摘要
print_method_summary(data)
```

### 节点定位和子树查询

```python
from json_query_tools import *

# 1. 基于类名定位节点并获取子树
results = find_node_and_get_subtree(data, class_name="BomIntegrationServiceImpl")

# 2. 基于方法名定位
results = find_node_and_get_subtree(data, method_name="doCheckParams")

# 3. 基于文件名定位
results = find_node_and_get_subtree(data, file_name="CheckParamUtil.java")

# 4. 组合查找：类名+方法名
results = find_node_and_get_subtree(data, 
                                   class_name="AbstractBomIntegrationPlugin", 
                                   method_name="doCheckParams")

# 5. 精确匹配
results = find_node_and_get_subtree(data, 
                                   class_name="BomIntegrationServiceImpl", 
                                   exact_match=True)

# 6. 显示节点详情和子树信息
for result in results:
    print_node_details(result)
    print_subtree_summary(result['subtree_full'])
```

## 🎯 节点定位和子树查询特性

### 核心功能
- **灵活定位**：支持基于类名、方法名、文件名的单独或组合查找
- **智能匹配**：支持模糊匹配（默认）和精确匹配模式
- **完整子树**：从定位节点开始，获取所有子方法的完整调用链
- **统计分析**：提供总节点数、最大深度、叶子节点等详细统计

### 适用场景
- **业务流程分析**：从某个业务方法开始，了解完整的处理流程
- **代码审查**：快速了解某个类或方法的影响范围
- **重构准备**：了解修改某个方法会影响哪些下游方法
- **学习代码**：从入口方法开始，逐步了解整个业务逻辑

### 查询结果信息
- **节点详情**：方法名、类名、包名、文件路径、调用路径
- **代码片段**：显示方法的前几行代码
- **子树统计**：总节点数、最大深度、叶子节点数
- **方法列表**：子树中所有方法的类名和深度信息

## 🔧 URL格式支持

工具支持多种URL格式：

- `api/v1/AcceptBill/saveDetailsBatch` (完整路径)
- `/AcceptBill/saveDetailsBatch` (简化路径)
- `AcceptBill/saveDetailsBatch` (最简路径)

## 🎯 搜索策略

URL到方法的映射使用逆向搜索策略：

1. **第一步**: 搜索最具体的方法名（如 `/saveDetailsBatch"`）
2. **第二步**: 在候选文件中搜索Controller路径（如 `/AcceptBill`）
3. **第三步**: 计算置信度，选择最佳匹配

## 📊 输出格式

工具会自动生成两种格式的分析结果：

- **文本格式**: `ast/repo_name/method_name.txt`
- **JSON格式**: `ast/repo_name/method_name.json`

## 🔍 支持的架构

- **传统Controller**: `@RestController`, `@Controller`
- **微服务架构**: `@FeignClient`接口
- **接口实现**: 自动解析接口到实现类的映射

## 🛠️ 配置选项

### 分析深度控制

```python
# 控制递归分析的最大深度（默认15层）
method_chain = analyze_call_chain(entry_method, repo_path, max_depth=10)
```

### 详细日志控制

```python
# 控制是否显示详细的分析日志
method_chain = analyze_call_chain(entry_method, repo_path, verbose=True)
```

## 🔧 故障排除

### 常见问题

1. **找不到方法**: 确保URL格式正确，检查Controller注解
2. **解析失败**: 检查Java文件编码是否为UTF-8
3. **接口方法无实现**: 检查接口实现类是否正确配置

### 调试技巧

```python
# 开启详细日志查看具体的搜索过程
entry_method = find_method_by_url(request_url, repo_path, verbose=True)
```

## 📈 性能优化

- 工具会自动缓存解析结果
- 支持大型代码库（测试过2000+Java文件）
- 智能过滤无效的方法调用

## 🤝 贡献指南

代码结构清晰，模块化设计，易于扩展：

- `build_graph.py` - 扩展AST解析功能
- `url_to_method_mapper.py` - 扩展URL映射规则
- `json_query_tools.py` - 扩展查询分析功能

## 📝 更新日志

### v2.0 (重构版本)
- ✅ 模块化设计，功能分离
- ✅ 支持URL到方法的自动映射
- ✅ 改进的接口实现解析
- ✅ 完善的JSON查询工具
- ✅ 清晰的代码结构和文档 