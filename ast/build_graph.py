import javalang
import os
import sys
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import re
from url_to_method_mapper import find_method_by_url

def parse_java_file(file_path):
    """解析Java文件，返回AST和源代码。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            code = f.read()
        tree = javalang.parse.parse(code)
        return tree, code
    except Exception as e:
        # print(f"警告: 解析文件 {file_path} 时出错: {e}")
        return None, None

def scan_java_files(repo_path):
    """扫描指定路径下的所有Java文件。"""
    java_files = []
    for root, _, files in os.walk(repo_path):
        for file in files:
            if file.endswith('.java'):
                java_files.append(os.path.join(root, file))
    return java_files

def extract_full_body(node, full_code):
    """提取节点（类或方法）的完整代码体，精确处理大括号匹配，正确处理字符串字面量和注释。"""
    if not hasattr(node, 'position') or not node.position:
        return ""
    
    code_lines = full_code.split('\n')
    start_line = node.position.line - 1
    
    # 寻找第一个 '{'
    first_brace_line = -1
    first_brace_col = -1
    for i in range(start_line, len(code_lines)):
        line = code_lines[i]
        brace_pos = line.find('{')
        if brace_pos != -1:
            first_brace_line = i
            first_brace_col = brace_pos
            break
    
    if first_brace_line == -1:
        return ""

    # 使用更智能的大括号匹配，考虑字符串字面量和注释
    brace_count = 0
    end_line = -1
    in_string = False
    in_char = False
    in_single_comment = False
    in_multi_comment = False
    escape_next = False
    
    for i in range(first_brace_line, len(code_lines)):
        line = code_lines[i]
        j = 0
        if i == first_brace_line:
            j = first_brace_col
            
        while j < len(line):
            char = line[j]
            
            # 处理转义字符
            if escape_next:
                escape_next = False
                j += 1
                continue
            
            # 检查是否在注释中
            if not in_string and not in_char and not in_single_comment and not in_multi_comment:
                if j < len(line) - 1 and line[j:j+2] == '//':
                    in_single_comment = True
                    j += 2
                    continue
                elif j < len(line) - 1 and line[j:j+2] == '/*':
                    in_multi_comment = True
                    j += 2
                    continue
            
            # 检查多行注释结束
            if in_multi_comment and j < len(line) - 1 and line[j:j+2] == '*/':
                in_multi_comment = False
                j += 2
                continue
            
            # 如果在注释中，跳过这个字符
            if in_single_comment or in_multi_comment:
                j += 1
                continue
            
            # 处理字符串
            if char == '"' and not in_char:
                in_string = not in_string
            elif char == "'" and not in_string:
                in_char = not in_char
            elif char == '\\' and (in_string or in_char):
                escape_next = True
            
            # 只有在不在字符串或字符字面量中时才计算大括号
            if not in_string and not in_char:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_line = i
                        break
            
            j += 1
        
        # 单行注释在行末结束
        if in_single_comment:
            in_single_comment = False
            
        if end_line != -1:
            break
            
    if end_line == -1:
        end_line = len(code_lines) - 1

    return '\n'.join(code_lines[start_line:end_line + 1])

def get_package_name(tree):
    """安全地获取包名。"""
    try:
        if hasattr(tree, 'package') and tree.package and hasattr(tree.package, 'name'):
            return tree.package.name
    except:
        pass
    return ''

def get_imports(tree):
    """安全地获取导入语句。"""
    imports = []
    try:
        if hasattr(tree, 'imports'):
            for imp in tree.imports:
                if hasattr(imp, 'path'):
                    imports.append(imp)
    except:
        pass
    return imports

def get_class_declarations(tree):
    """安全地获取类声明。"""
    classes = []
    try:
        for path, node in tree.filter(javalang.tree.ClassDeclaration):
            classes.append(node)
    except:
        pass
    return classes

def get_interface_declarations(tree):
    """安全地获取接口声明。"""
    interfaces = []
    try:
        for path, node in tree.filter(javalang.tree.InterfaceDeclaration):
            interfaces.append(node)
    except:
        pass
    return interfaces

def get_methods(node):
    """安全地获取方法列表。"""
    methods = []
    try:
        if hasattr(node, 'methods'):
            methods = node.methods
    except:
        pass
    return methods

def get_fields(node):
    """安全地获取字段列表。"""
    fields = []
    try:
        if hasattr(node, 'fields'):
            fields = node.fields
    except:
        pass
    return fields

def get_method_invocations(method_node):
    """安全地获取方法调用。"""
    invocations = []
    try:
        for path, invocation in method_node.filter(javalang.tree.MethodInvocation):
            invocations.append(invocation)
    except:
        pass
    return invocations

def build_definitions(repo_path):
    """构建代码库中所有类、方法、字段和导入的定义。"""
    java_files = scan_java_files(repo_path)
    print(f"找到 {len(java_files)} 个Java文件，开始解析...")
    
    definitions = {
        'classes': {},
        'interfaces': {},
        'methods': {},
        'fields': defaultdict(dict),
        'imports': defaultdict(dict),
        'interface_implementations': defaultdict(list)  # 接口到实现类的映射
    }
    
    # 第一遍：收集所有类和接口信息
    for file_path in java_files:
        tree, code = parse_java_file(file_path)
        if not tree:
            continue
            
        package_name = get_package_name(tree)
        
        # 提取导入
        imports = get_imports(tree)
        for imp in imports:
            if hasattr(imp, 'path'):
                definitions['imports'][file_path][imp.path.split('.')[-1]] = imp.path
                if hasattr(imp, 'wildcard') and imp.wildcard:
                    definitions['imports'][file_path]['*'] = imp.path

        # 处理类声明
        classes = get_class_declarations(tree)
        for node in classes:
            if hasattr(node, 'name'):
                class_name = node.name
                full_class_name = f"{package_name}.{class_name}" if package_name else class_name
                definitions['classes'][full_class_name] = {
                    'path': file_path,
                    'package': package_name,
                    'code': extract_full_body(node, code),
                    'implements': [],
                    'extends': None
                }
                # 修正：实现类与接口的映射，接口用全限定名
                if hasattr(node, 'implements') and node.implements:
                    for impl in node.implements:
                        if hasattr(impl, 'name'):
                            interface_short_name = impl.name
                            # 在所有已知接口中查找短名匹配的全限定名
                            for iface_full_name in definitions['interfaces']:
                                if iface_full_name.split('.')[-1] == interface_short_name:
                                    definitions['classes'][full_class_name]['implements'].append(iface_full_name)
                                    definitions['interface_implementations'][iface_full_name].append(full_class_name)
                                    break
                # 检查继承的类
                if hasattr(node, 'extends') and node.extends:
                    if hasattr(node.extends, 'name'):
                        parent_name = node.extends.name
                        full_parent_name = f"{package_name}.{parent_name}" if package_name else parent_name
                        definitions['classes'][full_class_name]['extends'] = full_parent_name
                
                # 提取字段
                fields = get_fields(node)
                for field in fields:
                    if hasattr(field, 'type') and hasattr(field.type, 'name'):
                        field_type = field.type.name
                        if hasattr(field, 'declarators'):
                            for declarator in field.declarators:
                                if hasattr(declarator, 'name'):
                                    definitions['fields'][full_class_name][declarator.name] = field_type
                
                # 提取方法
                methods = get_methods(node)
                for method in methods:
                    if hasattr(method, 'name'):
                        method_name = method.name
                        method_key = f"{full_class_name}.{method_name}"
                        definitions['methods'][method_key] = {
                            'class': full_class_name,
                            'code': extract_full_body(method, code),
                            'file_path': file_path,
                            'calls': [],
                            'is_interface_method': False
                        }

        # 处理接口声明
        interfaces = get_interface_declarations(tree)
        for node in interfaces:
            if hasattr(node, 'name'):
                interface_name = node.name
                full_interface_name = f"{package_name}.{interface_name}" if package_name else interface_name
                definitions['interfaces'][full_interface_name] = {
                    'path': file_path,
                    'package': package_name,
                    'code': extract_full_body(node, code)
                }
                
                # 提取接口方法
                methods = get_methods(node)
                for method in methods:
                    if hasattr(method, 'name'):
                        method_name = method.name
                        method_key = f"{full_interface_name}.{method_name}"
                        # 新增：提取接口方法声明
                        if hasattr(method, 'position') and method.position:
                            code_lines = code.split('\n')
                            decl_line = code_lines[method.position.line - 1].strip()
                        else:
                            decl_line = f"{method_name}(...)"  # fallback
                        definitions['methods'][method_key] = {
                            'class': full_interface_name,
                            'code': decl_line,  # 用声明行替代
                            'file_path': file_path,
                            'calls': [],
                            'is_interface_method': True
                        }

    print("代码定义解析完成，开始分析方法调用...")
    
    # 第二遍：解析方法调用
    for method_key, method_info in definitions['methods'].items():
        file_path = method_info['file_path']
        tree, _ = parse_java_file(file_path)
        if not tree:
            continue

        caller_class_name = method_info['class']
        caller_method_name = method_key.split('.')[-1]

        # 定位到当前方法节点
        classes = get_class_declarations(tree)
        interfaces = get_interface_declarations(tree)
        
        # 在类和接口中查找方法
        method_node = None
        for node in classes + interfaces:
            if hasattr(node, 'name') and node.name == caller_class_name.split('.')[-1]:
                methods = get_methods(node)
                for method in methods:
                    if hasattr(method, 'name') and method.name == caller_method_name:
                        method_node = method
                        break
                if method_node:
                    break

        if method_node:
            # 提取此方法中的调用
            invocations = get_method_invocations(method_node)
            for invocation in invocations:
                if hasattr(invocation, 'member'):
                    callee_name = invocation.member
                    qualifier = getattr(invocation, 'qualifier', None)

                    resolved_class = None
                    # 1. 解析调用者的类
                    if qualifier:
                        # a. 检查是否是类字段
                        if qualifier in definitions['fields'][caller_class_name]:
                            resolved_class = definitions['fields'][caller_class_name][qualifier]
                        # b. 检查是否是导入的类名
                        for imp_name, imp_path in definitions['imports'][file_path].items():
                            if qualifier == imp_name:
                                resolved_class = imp_path
                                break
                            # 通配符导入
                            if imp_name == '*' and any(c.startswith(f"{imp_path}.{qualifier}") for c in definitions['classes']):
                                resolved_class = f"{imp_path}.{qualifier}"
                                break
                        # c. 如果限定符本身就是全限定名
                        if not resolved_class and '.' in qualifier and qualifier in definitions['classes']:
                            resolved_class = qualifier

                    # 2. 确定被调用方法的全限定名
                    callee_key = None
                    if resolved_class:
                        # 尝试在所有类和接口中找到匹配的类型
                        for c_name in list(definitions['classes'].keys()) + list(definitions['interfaces'].keys()):
                            # 检查类名是否匹配（支持短名称和全限定名）
                            if (c_name.endswith('.' + resolved_class) or 
                                c_name == resolved_class or 
                                c_name.split('.')[-1] == resolved_class):
                                potential_key = f"{c_name}.{callee_name}"
                                if potential_key in definitions['methods']:
                                    callee_key = potential_key
                                    break
                    else:
                        # 无限定符调用，在同一个类中查找
                        potential_key = f"{caller_class_name}.{callee_name}"
                        if potential_key in definitions['methods']:
                            callee_key = potential_key

                    if callee_key and callee_key not in method_info['calls']:
                         method_info['calls'].append(callee_key)
    
    print("方法调用分析完成。")
    return definitions

def find_interface_implementation(interface_method_key, definitions):
    """查找接口方法的实现类。"""
    interface_name = '.'.join(interface_method_key.split('.')[:-1])
    method_name = interface_method_key.split('.')[-1]
    print(f"[调试] find_interface_implementation: interface_method_key={interface_method_key}, interface_name={interface_name}, method_name={method_name}")
    implementations = definitions['interface_implementations'].get(interface_name, [])
    print(f"[调试] interface_implementations[{interface_name}] = {implementations}")
    for impl_class in implementations:
        impl_method_key = f"{impl_class}.{method_name}"
        print(f"[调试] 尝试实现类方法: {impl_method_key}")
        if impl_method_key in definitions['methods']:
            print(f"[调试] 找到实现类方法: {impl_method_key}")
            return impl_method_key
    for impl_class in implementations:
        parent_class = definitions['classes'].get(impl_class, {}).get('extends')
        if parent_class:
            parent_impl_key = f"{parent_class}.{method_name}"
            if parent_impl_key in definitions['methods']:
                print(f"[调试] 找到父类实现方法: {parent_impl_key}")
                return parent_impl_key
    print(f"[调试] 未找到实现类方法")
    return None

def analyze_method_recursively(method_key, definitions, visited=None, depth=0, max_depth=15):
    """递归分析方法调用链。"""
    if visited is None:
        visited = set()
    method_info = definitions['methods'].get(method_key)
    if not method_info:
        return {}
    visited_key = (method_key, method_info.get('is_interface_method', False))
    if depth > max_depth or visited_key in visited:
        return {}
    visited.add(visited_key)
    package_name = ""
    if method_info['class'] in definitions['classes']:
        package_name = definitions['classes'][method_info['class']]['package']
    elif method_info['class'] in definitions['interfaces']:
        package_name = definitions['interfaces'][method_info['class']]['package']
    result = {
        'package': package_name,
        'class': method_info['class'],
        'code': method_info['code'],
        'file_path': method_info['file_path'],
        'calls': {},
        'is_interface_method': method_info.get('is_interface_method', False)
    }
    print(f"[调试] analyze_method_recursively: method_key={method_key}, is_interface={method_info.get('is_interface_method', False)}, depth={depth}")
    for callee_key in method_info.get('calls', []):
        if callee_key in definitions['methods']:
            callee_info = definitions['methods'][callee_key]
            if callee_info.get('is_interface_method', False):
                impl_key = find_interface_implementation(callee_key, definitions)
                print(f"[调试] callee_key={callee_key}, impl_key={impl_key}")
                if impl_key:
                    child_result = analyze_method_recursively(impl_key, definitions, visited, depth + 1, max_depth)
                    if child_result:
                        result['calls'][impl_key] = child_result
            else:
                child_result = analyze_method_recursively(callee_key, definitions, visited, depth + 1, max_depth)
                if child_result:
                    result['calls'][callee_key] = child_result
    if method_info.get('is_interface_method', False):
        impl_key = find_interface_implementation(method_key, definitions)
        print(f"[调试] 当前接口方法: {method_key}, impl_key={impl_key}")
        if impl_key and impl_key not in result['calls']:
            child_result = analyze_method_recursively(impl_key, definitions, visited, depth + 1, max_depth)
            if child_result:
                result['calls'][impl_key] = child_result
    return result

def save_method_chain(method_chain, repo_path, entry_method, indent=0, output_lines=None):
    """保存方法调用链到文件
    
    Args:
        method_chain: 方法调用链字典
        repo_path: 仓库路径
        entry_method: 入口方法名
        indent: 缩进级别
        output_lines: 输出行列表（用于递归调用）
    
    Returns:
        输出行列表
    """
    if output_lines is None:
        output_lines = []
    
    for method_key, method_info in method_chain.items():
        # 添加方法信息
        output_lines.append("\n" + "  " * indent + f"方法: {method_key}")
        output_lines.append("  " * indent + f"包: {method_info['package']}")
        output_lines.append("  " * indent + f"文件: {method_info['file_path']}")
        output_lines.append("  " * indent + "代码:")
        
        # 添加代码（缩进处理）
        code_lines = method_info['code'].split('\n')
        for line in code_lines:
            output_lines.append("  " * indent + line)
        
        output_lines.append("\n" + "  " * indent + "-" * 80)
        
        # 递归处理调用的方法
        if method_info['calls']:
            output_lines.append("  " * indent + "调用的方法:")
            save_method_chain(method_info['calls'], repo_path, entry_method, indent + 1, output_lines)
    
    # 在最顶层调用时保存到文件
    if indent == 0:
        # 构建保存路径: ast/repo_path（最后部分）/entry_method
        repo_name = Path(repo_path).name if repo_path else "unknown_repo"
        save_dir = Path("ast") / repo_name
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 清理入口方法名，移除特殊字符
        clean_entry_method = re.sub(r'[<>:"/\\|?*]', '_', entry_method)
        save_file = save_dir / f"{clean_entry_method}.txt"
        
        # 保存到文件
        try:
            with open(save_file, 'w', encoding='utf-8') as f:
                # 添加文件头信息
                header = [
                    f"方法调用链分析报告",
                    f"=" * 60,
                    f"仓库路径: {repo_path}",
                    f"入口方法: {entry_method}",
                    f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    f"=" * 60,
                    ""
                ]
                f.write('\n'.join(header + output_lines))
            
            print(f"✅ 方法调用链已保存到: {save_file}")
            print(f"📁 保存路径格式: ast/{repo_name}/{clean_entry_method}.txt")
        except Exception as e:
            print(f"❌ 保存文件失败: {str(e)}")
    
    return output_lines

def print_method_chain(method_chain, indent=0):
    """格式化并打印方法调用链。"""
    for method_key, method_info in method_chain.items():
        prefix = "  " * indent
        print(f"\n{prefix}--------------------------------------------------")
        print(f"{prefix}包: {method_info['package']}")
        print(f"{prefix}类: {method_info['class']}")
        print(f"{prefix}方法: {method_key.split('.')[-1]}")
        if method_info.get('is_interface_method', False):
            print(f"{prefix}类型: 接口方法")
        print(f"{prefix}文件: {method_info['file_path']}")
        print(f"{prefix}代码:")
        if not method_info['code'].strip():
            print(f"{prefix}  // 接口方法无实现体")
        else:
            for line in method_info['code'].split('\n'):
                print(f"{prefix}  {line}")
        # 调试输出：打印calls的key
        if method_info['calls']:
            print(f"{prefix}调用 -> (calls: {list(method_info['calls'].keys())})")
            print_method_chain(method_info['calls'], indent + 1)

def save_method_chain_json(method_chain, repo_path, entry_method):
    """保存方法调用链为JSON格式
    
    Args:
        method_chain: 方法调用链字典
        repo_path: 仓库路径
        entry_method: 入口方法名
    
    Returns:
        保存的JSON文件路径
    """
    # 构建保存路径: ast/repo_path（最后部分）/entry_method.json
    repo_name = Path(repo_path).name if repo_path else "unknown_repo"
    save_dir = Path("ast") / repo_name
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # 清理入口方法名，移除特殊字符
    clean_entry_method = re.sub(r'[<>:"/\\|?*]', '_', entry_method)
    save_file = save_dir / f"{clean_entry_method}.json"
    
    # 构建JSON数据结构
    json_data = {
        "metadata": {
            "repo_path": repo_path,
            "entry_method": entry_method,
            "generated_time": datetime.now().isoformat(),
            "tool_version": "1.0"
        },
        "method_call_chain": method_chain
    }
    
    try:
        # 保存为JSON文件
        with open(save_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 方法调用链JSON已保存到: {save_file}")
        print(f"📁 JSON保存路径格式: ast/{repo_name}/{clean_entry_method}.json")
        return str(save_file)
    except Exception as e:
        print(f"❌ 保存JSON文件失败: {str(e)}")
        return None

def save_method_chain_with_context(method_chain, repo_path=None, entry_method=None):
    """保存方法调用链到文件的包装函数（同时保存文本和JSON格式）
    
    Args:
        method_chain: 方法调用链字典或单个方法信息字典
        repo_path: 仓库路径，如果为None则尝试从环境变量或当前目录获取
        entry_method: 入口方法名，如果为None则使用默认值
    """
    # 如果没有提供repo_path，尝试从多个来源获取
    if repo_path is None:
        # 1. 尝试从环境变量获取
        repo_path = os.environ.get('REPO_PATH')
        
        # 2. 如果环境变量没有，尝试从当前工作目录获取
        if repo_path is None:
            repo_path = os.getcwd()
        
        # 3. 如果是在工程目录下，尝试获取上级目录作为repo_path
        current_dir = Path(os.getcwd())
        if current_dir.name in ['ast', 'analysis']:
            repo_path = str(current_dir.parent)
    
    # 如果没有提供entry_method，使用默认值
    if entry_method is None:
        # 尝试从method_chain中获取第一个方法作为entry_method
        if isinstance(method_chain, dict) and 'package' in method_chain:
            # method_chain是单个方法信息字典
            entry_method = "unknown_method"
        elif method_chain:
            entry_method = list(method_chain.keys())[0]
        else:
            entry_method = "unknown_method"
    
    # 检查method_chain的格式，如果是单个方法信息，需要包装成正确格式
    if isinstance(method_chain, dict) and 'package' in method_chain:
        # method_chain是单个方法信息字典，需要包装
        wrapped_method_chain = {entry_method: method_chain}
        # 保存文本格式
        save_method_chain(wrapped_method_chain, repo_path, entry_method)
        # 保存JSON格式
        save_method_chain_json(wrapped_method_chain, repo_path, entry_method)
    else:
        # method_chain已经是正确的格式
        # 保存文本格式
        save_method_chain(method_chain, repo_path, entry_method)
        # 保存JSON格式
        save_method_chain_json(method_chain, repo_path, entry_method)

def analyze_call_chain(entry_method, repo_path, verbose=True):
    """分析方法调用链的主要函数
    
    Args:
        entry_method: 入口方法全限定名 (包名.类名.方法名)
        repo_path: 仓库路径
        verbose: 是否显示详细日志
    
    Returns:
        dict: 方法调用链数据
    """
    if verbose:
        print(f"\n🔍 开始分析方法调用链")
        print(f"   入口方法: {entry_method}")
        print(f"   仓库路径: {repo_path}")

    if not os.path.isdir(repo_path):
        raise ValueError(f"无效的仓库路径: {repo_path}")

    if verbose:
        print("\n开始构建代码定义...")
    definitions = build_definitions(repo_path)
    
    if entry_method not in definitions['methods']:
        raise ValueError(f"入口方法 '{entry_method}' 未在代码库中找到。")

    if verbose:
        print(f"\n开始从 '{entry_method}' 进行递归分析...")
    method_chain = analyze_method_recursively(entry_method, definitions)
    
    if not method_chain:
        if verbose:
            print("分析完成，未发现可追踪的内部方法调用。")
        return {}
    
    if verbose:
        print("\n========== 方法调用树 ==========")
        print_method_chain({entry_method: method_chain})
        
        # 保存方法调用链到文件（同时保存文本和JSON格式）
        save_method_chain_with_context(method_chain, repo_path, entry_method)
    
    return {entry_method: method_chain}

# 主函数（示例用法）
def main():
    # 使用URL查找入口方法的示例
    # repo_path = "/Users/<USER>/Desktop/project/gcg_project/agent（交互）设计/code_base/SA-0205_MSA_B02"
    # request_url = "/repair/business/findAll"  # 新的URL参数
    repo_path = "/Users/<USER>/Desktop/project/gcg_project/agent（交互）设计/code_base/BT-0001_MSA_306"
    request_url = "POST:/v1/member-integral" 
    # entry_method = "com.dcp.bpm.statistical.system.controller.SystemTableController.getSubWFhistory"

    if not os.path.isdir(repo_path):
        print("错误: 无效的仓库路径。")
        return

    print("🔍 根据URL查找对应的Controller方法...")
    entry_method = find_method_by_url(request_url, repo_path)
    
    if not entry_method:
        print(f"❌ 错误: 无法根据URL '{request_url}' 找到对应的Controller方法")
        print("请检查URL格式，支持的格式:")
        print("  - api/v1/AcceptBill/saveDetailsBatch")
        print("  - /AcceptBill/saveDetailsBatch")
        print("  - http://localhost:8080/api/v1/AcceptBill/saveDetailsBatch")
        return
    
    print(f"✅ 找到入口方法: {entry_method}")

    # 分析方法调用链
    try:
        method_chain = analyze_call_chain(entry_method, repo_path)
        print(f"\n✅ 方法调用链分析完成")
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

if __name__ == "__main__":
    main()
