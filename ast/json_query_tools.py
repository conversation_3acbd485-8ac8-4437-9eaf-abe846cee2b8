import json

def load_method_chain_json(json_file_path):
    """加载JSON格式的方法调用链
    
    Args:
        json_file_path: JSON文件路径
    
    Returns:
        解析后的方法调用链数据
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载JSON文件失败: {str(e)}")
        return None

def find_method_by_name(method_chain, method_name, exact_match=False):
    """在方法调用链中按名称查找方法
    
    Args:
        method_chain: 方法调用链数据
        method_name: 要查找的方法名（可以是部分名称）
        exact_match: 是否精确匹配
    
    Returns:
        匹配的方法列表
    """
    results = []
    
    def search_recursive(chain_dict, path=[]):
        for method_key, method_info in chain_dict.items():
            if exact_match:
                if method_name == method_key.split('.')[-1]:
                    results.append({
                        'method_key': method_key,
                        'method_info': method_info,
                        'path': path + [method_key]
                    })
            else:
                if method_name.lower() in method_key.lower():
                    results.append({
                        'method_key': method_key,
                        'method_info': method_info,
                        'path': path + [method_key]
                    })
            
            # 递归查找子调用
            if 'calls' in method_info and method_info['calls']:
                search_recursive(method_info['calls'], path + [method_key])
    
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        search_recursive(method_chain['method_call_chain'])
    else:
        search_recursive(method_chain)
    
    return results

def find_methods_by_class(method_chain, class_name):
    """在方法调用链中按类名查找方法
    
    Args:
        method_chain: 方法调用链数据
        class_name: 要查找的类名（可以是部分名称）
    
    Returns:
        匹配的方法列表
    """
    results = []
    
    def search_recursive(chain_dict, path=[]):
        for method_key, method_info in chain_dict.items():
            if class_name.lower() in method_info.get('class', '').lower():
                results.append({
                    'method_key': method_key,
                    'method_info': method_info,
                    'path': path + [method_key]
                })
            
            # 递归查找子调用
            if 'calls' in method_info and method_info['calls']:
                search_recursive(method_info['calls'], path + [method_key])
    
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        search_recursive(method_chain['method_call_chain'])
    else:
        search_recursive(method_chain)
    
    return results

def get_call_path_to_method(method_chain, target_method):
    """获取到指定方法的调用路径
    
    Args:
        method_chain: 方法调用链数据
        target_method: 目标方法名
    
    Returns:
        调用路径列表
    """
    paths = []
    
    def find_paths(chain_dict, current_path=[]):
        for method_key, method_info in chain_dict.items():
            new_path = current_path + [method_key]
            
            if target_method in method_key:
                paths.append(new_path.copy())
            
            # 递归查找子调用
            if 'calls' in method_info and method_info['calls']:
                find_paths(method_info['calls'], new_path)
    
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        find_paths(method_chain['method_call_chain'])
    else:
        find_paths(method_chain)
    
    return paths

def get_method_tree_at_depth(method_chain, depth=1):
    """获取指定深度的方法调用树
    
    Args:
        method_chain: 方法调用链数据
        depth: 要获取的深度层级
    
    Returns:
        指定深度的方法调用树
    """
    def extract_at_depth(chain_dict, current_depth=0):
        if current_depth >= depth:
            return {}
        
        result = {}
        for method_key, method_info in chain_dict.items():
            if current_depth == depth - 1:
                # 到达指定深度，只保留方法信息，不包含子调用
                result[method_key] = {
                    k: v for k, v in method_info.items() if k != 'calls'
                }
            else:
                # 继续递归
                result[method_key] = method_info.copy()
                if 'calls' in method_info and method_info['calls']:
                    result[method_key]['calls'] = extract_at_depth(
                        method_info['calls'], current_depth + 1
                    )
        
        return result
    
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        return extract_at_depth(method_chain['method_call_chain'])
    else:
        return extract_at_depth(method_chain)

def print_method_summary(method_chain):
    """打印方法调用链的摘要信息
    
    Args:
        method_chain: 方法调用链数据
    """
    def count_methods(chain_dict):
        count = 0
        for method_key, method_info in chain_dict.items():
            count += 1
            if 'calls' in method_info and method_info['calls']:
                count += count_methods(method_info['calls'])
        return count
    
    def get_max_depth(chain_dict, current_depth=0):
        max_depth = current_depth
        for method_key, method_info in chain_dict.items():
            if 'calls' in method_info and method_info['calls']:
                depth = get_max_depth(method_info['calls'], current_depth + 1)
                max_depth = max(max_depth, depth)
        return max_depth
    
    def get_packages(chain_dict):
        packages = set()
        for method_key, method_info in chain_dict.items():
            if 'package' in method_info:
                packages.add(method_info['package'])
            if 'calls' in method_info and method_info['calls']:
                packages.update(get_packages(method_info['calls']))
        return packages
    
    chain_data = method_chain
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        chain_data = method_chain['method_call_chain']
        print(f"📋 方法调用链分析摘要")
        print(f"=" * 50)
        if 'metadata' in method_chain:
            metadata = method_chain['metadata']
            print(f"🎯 入口方法: {metadata.get('entry_method', 'N/A')}")
            print(f"📁 仓库路径: {metadata.get('repo_path', 'N/A')}")
            print(f"⏰ 生成时间: {metadata.get('generated_time', 'N/A')}")
            print(f"-" * 50)
    
    total_methods = count_methods(chain_data)
    max_depth = get_max_depth(chain_data)
    packages = get_packages(chain_data)
    
    print(f"📊 统计信息:")
    print(f"   - 总方法数: {total_methods}")
    print(f"   - 最大调用深度: {max_depth}")
    print(f"   - 涉及包数: {len(packages)}")
    print(f"📦 涉及的包:")
    for pkg in sorted(packages):
        print(f"   - {pkg}")

def find_node_and_get_subtree(method_chain, class_name=None, method_name=None, file_name=None, exact_match=True):
    """基于类名+方法名+文件名定位节点并获取所有子节点
    
    Args:
        method_chain: 方法调用链数据
        class_name: 要查找的类名（可选，支持部分匹配）
        method_name: 要查找的方法名（可选，支持部分匹配）
        file_name: 要查找的文件名（可选，支持部分匹配）
        exact_match: 是否精确匹配（默认False，支持部分匹配）
    
    Returns:
        list: 匹配的节点列表，每个节点包含完整的子树信息
    """
    if not any([class_name, method_name, file_name]):
        print("❌ 至少需要提供一个查找条件（类名、方法名或文件名）")
        return []
    
    results = []
    
    def search_recursive(chain_dict, path=[]):
        for method_key, method_info in chain_dict.items():
            # 检查是否匹配查找条件
            match_conditions = []
            
            # 检查类名匹配
            if class_name:
                current_class = method_info.get('class', '')
                if exact_match:
                    match_conditions.append(class_name == current_class or class_name == current_class.split('.')[-1])
                else:
                    match_conditions.append(class_name.lower() in current_class.lower())
            
            # 检查方法名匹配
            if method_name:
                current_method = method_key.split('.')[-1]
                if exact_match:
                    match_conditions.append(method_name == current_method)
                else:
                    match_conditions.append(method_name.lower() in current_method.lower())
            
            # 检查文件名匹配
            if file_name:
                current_file = method_info.get('file_path', '')
                if exact_match:
                    match_conditions.append(file_name in current_file)
                else:
                    match_conditions.append(file_name.lower() in current_file.lower())
            
            # 如果所有提供的条件都匹配，则返回该节点及其子树
            if match_conditions and all(match_conditions):
                results.append({
                    'method_key': method_key,
                    'method_info': method_info,
                    'path': path + [method_key],
                    'subtree': method_info.get('calls', {}),
                    'subtree_full': get_full_subtree(method_info.get('calls', {}))
                })
            
            # 递归搜索子节点
            if 'calls' in method_info and method_info['calls']:
                search_recursive(method_info['calls'], path + [method_key])
    
    # 提取方法调用链数据
    chain_data = method_chain
    if isinstance(method_chain, dict) and 'method_call_chain' in method_chain:
        chain_data = method_chain['method_call_chain']
    
    search_recursive(chain_data)
    return results

def get_full_subtree(subtree_dict):
    """获取完整的子树信息，包括所有嵌套的子节点
    
    Args:
        subtree_dict: 子树字典
    
    Returns:
        dict: 包含统计信息和完整子树的字典
    """
    if not subtree_dict:
        return {
            'total_nodes': 0,
            'max_depth': 0,
            'leaf_nodes': [],
            'all_methods': [],
            'subtree_data': {}
        }
    
    all_methods = []
    leaf_nodes = []
    
    def collect_nodes(chain_dict, current_depth=1):
        max_depth = current_depth
        
        for method_key, method_info in chain_dict.items():
            all_methods.append({
                'method_key': method_key,
                'class': method_info.get('class', ''),
                'package': method_info.get('package', ''),
                'file_path': method_info.get('file_path', ''),
                'depth': current_depth
            })
            
            # 检查是否是叶子节点
            calls = method_info.get('calls', {})
            if not calls:
                leaf_nodes.append(method_key)
            else:
                # 递归处理子节点
                sub_depth = collect_nodes(calls, current_depth + 1)
                max_depth = max(max_depth, sub_depth)
        
        return max_depth
    
    max_depth = collect_nodes(subtree_dict)
    
    return {
        'total_nodes': len(all_methods),
        'max_depth': max_depth,
        'leaf_nodes': leaf_nodes,
        'all_methods': all_methods,
        'subtree_data': subtree_dict
    }

def print_subtree_summary(subtree_info):
    """打印子树摘要信息
    
    Args:
        subtree_info: 子树信息字典
    """
    print(f"📊 子树统计信息:")
    print(f"   - 总节点数: {subtree_info['total_nodes']}")
    print(f"   - 最大深度: {subtree_info['max_depth']}")
    print(f"   - 叶子节点数: {len(subtree_info['leaf_nodes'])}")
    
    if subtree_info['all_methods']:
        print(f"\n📋 所有方法列表:")
        for i, method in enumerate(subtree_info['all_methods'], 1):
            method_name = method['method_key'].split('.')[-1]
            class_name = method['class'].split('.')[-1]
            print(f"   {i:2d}. {method_name} (类: {class_name}, 深度: {method['depth']})")
    
    if subtree_info['leaf_nodes']:
        print(f"\n🍃 叶子节点:")
        for i, leaf in enumerate(subtree_info['leaf_nodes'], 1):
            leaf_name = leaf.split('.')[-1]
            print(f"   {i}. {leaf_name}")

def print_node_details(node_result):
    """打印节点详细信息
    
    Args:
        node_result: 节点查询结果
    """
    method_info = node_result['method_info']
    
    print(f"\n🎯 找到匹配节点:")
    print(f"   方法: {node_result['method_key']}")
    print(f"   类: {method_info.get('class', 'N/A')}")
    print(f"   包: {method_info.get('package', 'N/A')}")
    print(f"   文件: {method_info.get('file_path', 'N/A')}")
    print(f"   调用路径: {' -> '.join([p.split('.')[-1] for p in node_result['path']])}")
    
    # 打印代码片段（前几行）
    code = method_info.get('code', '')
    if code:
        code_lines = code.split('\n')
        print(f"   代码片段:")
        for i, line in enumerate(code_lines[:5], 1):
            print(f"     {i}. {line.strip()}")
        if len(code_lines) > 5:
            print(f"     ... (还有 {len(code_lines) - 5} 行)")

# ========== 使用示例 ==========
# 
# from json_query_tools import *
#
# # 1. 加载JSON文件:
# data = load_method_chain_json("ast/repo_name/method_name.json")
#
# # 2. 查找特定方法:
# results = find_method_by_name(data, "saveDetailsBatch")
#
# # 3. 按类名查找:
# results = find_methods_by_class(data, "AcceptBill")
#
# # 4. 获取调用路径:
# paths = get_call_path_to_method(data, "sendMail")
#
# # 5. 获取指定深度的调用树:
# tree = get_method_tree_at_depth(data, depth=2)
#
# # 6. 打印摘要:
# print_method_summary(data) 
#
# # 1. 基于类名+方法名+文件名定位节点:
# results = find_node_and_get_subtree(data, 
#                                    class_name="BomIntegrationServiceImpl", 
#                                    method_name="getBomIntegrationResHeader")
#
# # 2. 基于文件名查找:
# results = find_node_and_get_subtree(data, file_name="CheckParamUtil.java")
#
# # 3. 精确匹配:
# results = find_node_and_get_subtree(data, 
#                                    class_name="BomIntegrationServiceImpl", 
#                                    exact_match=True)
#
# # 4. 打印节点详情和子树信息:
# for result in results:
#     print_node_details(result)
#     print_subtree_summary(result['subtree_full']) 