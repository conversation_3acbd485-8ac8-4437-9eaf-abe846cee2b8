import os
import re
from pathlib import Path
import javalang
from collections import defaultdict

def parse_java_file(file_path):
    """解析Java文件，返回AST和源代码。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            code = f.read()
        tree = javalang.parse.parse(code)
        return tree, code
    except Exception as e:
        return None, None

def get_package_name(tree):
    """安全地获取包名。"""
    try:
        if hasattr(tree, 'package') and tree.package and hasattr(tree.package, 'name'):
            return tree.package.name
    except:
        pass
    return ''

def get_class_declarations(tree):
    """安全地获取类声明。"""
    classes = []
    try:
        for path, node in tree.filter(javalang.tree.ClassDeclaration):
            classes.append(node)
    except:
        pass
    return classes

def get_interface_declarations(tree):
    """安全地获取接口声明。"""
    interfaces = []
    try:
        for path, node in tree.filter(javalang.tree.InterfaceDeclaration):
            interfaces.append(node)
    except:
        pass
    return interfaces

def get_methods(node):
    """安全地获取方法列表。"""
    methods = []
    try:
        if hasattr(node, 'methods'):
            methods = node.methods
    except:
        pass
    return methods

def extract_full_body(node, full_code):
    """提取节点（类或方法）的完整代码体，精确处理大括号匹配。"""
    if not hasattr(node, 'position') or not node.position:
        return ""
    
    code_lines = full_code.split('\n')
    start_line = node.position.line - 1
    
    # 寻找第一个 '{'
    first_brace_line = -1
    first_brace_col = -1
    for i in range(start_line, len(code_lines)):
        line = code_lines[i]
        brace_pos = line.find('{')
        if brace_pos != -1:
            first_brace_line = i
            first_brace_col = brace_pos
            break
    
    if first_brace_line == -1:
        return ""

    brace_count = 0
    end_line = -1
    
    for i in range(first_brace_line, len(code_lines)):
        line = code_lines[i]
        for j, char in enumerate(line):
            if i == first_brace_line and j < first_brace_col:
                continue
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_line = i
                    break
        if end_line != -1:
            break
            
    if end_line == -1:
        end_line = len(code_lines) - 1

    return '\n'.join(code_lines[start_line:end_line + 1])

def extract_url_segments(request_url):
    """从请求URL中提取路径段和HTTP方法信息

    Args:
        request_url: 请求URL，如 "api/v1/AcceptBill/saveDetailsBatch" 或 "POST:/v1/member-integral"

    Returns:
        tuple: (search_paths, http_method)
               search_paths: 路径段列表，从最具体到最一般，如 ["/saveDetailsBatch", "/AcceptBill"]
               http_method: HTTP方法类型，如 "POST", "GET" 等，如果没有则为None
    """
    http_method = None
    path_part = request_url

    # 清理URL
    if '://' in request_url:
        # 去除协议部分
        url_part = request_url.split('://', 1)[1]
        if '/' in url_part:
            path_part = '/' + url_part.split('/', 1)[1]
        else:
            path_part = '/'
    else:
        path_part = request_url

    # 检查并提取HTTP方法前缀
    if ':' in path_part and path_part.split(':')[0].upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
        method_and_path = path_part.split(':', 1)
        http_method = method_and_path[0].upper()
        path_part = method_and_path[1]

    # 分割路径并清理
    path_part = path_part.strip('/')
    segments = path_part.split('/') if path_part else []

    # 过滤掉版本号等信息，只保留业务相关的路径段
    business_segments = []
    for segment in segments:
        # 跳过版本号（v1, v2等）和api前缀
        if not (segment.lower().startswith('v') and segment[1:].isdigit()) and segment.lower() != 'api':
            business_segments.append(segment)

    # 从最具体的方法名开始，逆向构建搜索路径
    search_paths = []
    if business_segments:
        # 最后一个段通常是方法名
        method_name = business_segments[-1]
        search_paths.append(f"/{method_name}")

        # 倒数第二个通常是Controller路径
        if len(business_segments) > 1:
            controller_path = business_segments[-2]
            search_paths.append(f"/{controller_path}")

        # 如果还有更多层级，也加入搜索
        for i in range(len(business_segments) - 3, -1, -1):
            search_paths.append(f"/{business_segments[i]}")

    return search_paths, http_method

def search_files_by_content(repo_path, search_pattern, file_pattern="*.java"):
    """在指定路径中搜索包含特定内容的文件
    
    Args:
        repo_path: 仓库路径
        search_pattern: 搜索模式
        file_pattern: 文件匹配模式
    
    Returns:
        list: 匹配的文件路径列表
    """
    matching_files = []
    
    # 递归搜索所有Java文件
    for root, dirs, files in os.walk(repo_path):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if search_pattern in content:
                            matching_files.append(file_path)
                except Exception as e:
                    # 忽略读取失败的文件
                    continue
    
    return matching_files

def extract_method_from_file(file_path, method_name, http_method=None, verbose=False):
    """从Java文件中提取指定方法的信息

    Args:
        file_path: Java文件路径
        method_name: 方法名或URL路径段
        http_method: HTTP方法类型，如 "GET", "POST" 等，用于精确匹配
        verbose: 是否显示详细日志

    Returns:
        dict: 方法信息，包含full_name, class_name, package等
    """
    if verbose:
        print(f"🔍 开始解析文件: {file_path}")
        print(f"🎯 查找方法: {method_name}")
    
    try:
        tree, code = parse_java_file(file_path)
        if not tree:
            if verbose:
                print(f"❌ 无法解析Java文件: {file_path}")
            return None
        
        if verbose:
            print(f"✅ Java文件解析成功")
        
        package_name = get_package_name(tree)
        if verbose:
            print(f"📦 包名: {package_name}")
        
        # 查找类和接口
        classes = get_class_declarations(tree)
        interfaces = get_interface_declarations(tree)
        
        if verbose:
            print(f"📊 找到 {len(classes)} 个类, {len(interfaces)} 个接口")
        
        # 先查找Controller类（优先级最高）
        if verbose:
            print(f"🔍 第一轮: 查找Controller类...")
        for i, class_node in enumerate(classes):
            if not hasattr(class_node, 'name'):
                if verbose:
                    print(f"   ⚠️ 类 {i+1} 没有名称属性")
                continue
            
            class_name = class_node.name
            full_class_name = f"{package_name}.{class_name}" if package_name else class_name
            if verbose:
                print(f"   📋 检查类: {class_name} (完整名称: {full_class_name})")
            
            # 检查是否是Controller类（检查整个文件内容）
            is_controller = ('Controller' in class_name or '@Controller' in code or '@RestController' in code)
            if verbose:
                print(f"   🎯 是否为Controller: {is_controller}")
            
            if is_controller:
                # 查找方法
                methods = get_methods(class_node)
                if verbose:
                    print(f"   📝 该类有 {len(methods)} 个方法")
                
                # 第一次尝试：精确方法名匹配
                for j, method in enumerate(methods):
                    if hasattr(method, 'name'):
                        if verbose and j < 10:  # 只显示前10个方法名
                            print(f"      方法 {j+1}: {method.name}")
                        if method.name == method_name:
                            if verbose:
                                print(f"   ✅ 找到精确匹配的Controller方法: {method_name}")
                            class_code = extract_full_body(class_node, code)
                            return {
                                'full_name': f"{full_class_name}.{method_name}",
                                'class_name': full_class_name,
                                'method_name': method_name,
                                'package': package_name,
                                'file_path': file_path,
                                'class_code': class_code,
                                'type': 'controller'
                            }
                
                # 第二次尝试：基于Spring注解的URL路径匹配
                if verbose:
                    print(f"   🔍 精确方法名匹配失败，尝试注解路径匹配...")

                method_result = _find_method_by_annotation(class_node, method_name, code, full_class_name, package_name, file_path, http_method, verbose)
                if method_result:
                    return method_result
        
        # 如果没找到Controller，再查找FeignClient接口（优先级次之）
        if verbose:
            print(f"🔍 第二轮: 查找FeignClient接口...")
        for i, interface_node in enumerate(interfaces):
            if not hasattr(interface_node, 'name'):
                if verbose:
                    print(f"   ⚠️ 接口 {i+1} 没有名称属性")
                continue
            
            interface_name = interface_node.name
            full_interface_name = f"{package_name}.{interface_name}" if package_name else interface_name
            if verbose:
                print(f"   📋 检查接口: {interface_name} (完整名称: {full_interface_name})")
            
            # 改进FeignClient检测：检查整个文件内容，不仅仅是接口体
            is_feign_client = ('@FeignClient' in code or 'FeignClient' in interface_name)
            if verbose:
                print(f"   🎯 是否为FeignClient: {is_feign_client}")
                
                # 显示文件中@FeignClient相关的行
                feign_lines = [line.strip() for line in code.split('\n') if '@FeignClient' in line]
                if feign_lines:
                    print(f"   🔍 找到FeignClient注解: {feign_lines}")
            
            if is_feign_client:
                # 查找方法
                methods = get_methods(interface_node)
                if verbose:
                    print(f"   📝 该接口有 {len(methods)} 个方法")
                for j, method in enumerate(methods):
                    if hasattr(method, 'name'):
                        if verbose and j < 10:  # 只显示前10个方法名
                            print(f"      方法 {j+1}: {method.name}")
                        if method.name == method_name:
                            if verbose:
                                print(f"   ✅ 找到匹配的FeignClient方法: {method_name}")
                            interface_code = extract_full_body(interface_node, code)
                            return {
                                'full_name': f"{full_interface_name}.{method_name}",
                                'class_name': full_interface_name,
                                'method_name': method_name,
                                'package': package_name,
                                'file_path': file_path,
                                'class_code': interface_code,
                                'type': 'feign_client'
                            }
        
        # 最后查找其他类（如果都没找到）
        if verbose:
            print(f"🔍 第三轮: 查找其他类...")
        for i, class_node in enumerate(classes):
            if not hasattr(class_node, 'name'):
                continue
            
            class_name = class_node.name
            full_class_name = f"{package_name}.{class_name}" if package_name else class_name
            if verbose:
                print(f"   📋 检查其他类: {class_name}")
            
            class_code = extract_full_body(class_node, code)
            # 查找方法
            methods = get_methods(class_node)
            if verbose:
                print(f"   📝 该类有 {len(methods)} 个方法")
            for method in methods:
                if hasattr(method, 'name') and method.name == method_name:
                    if verbose:
                        print(f"   ✅ 找到匹配的其他类型方法: {method_name}")
                    return {
                        'full_name': f"{full_class_name}.{method_name}",
                        'class_name': full_class_name,
                        'method_name': method_name,
                        'package': package_name,
                        'file_path': file_path,
                        'class_code': class_code,
                        'type': 'other'
                    }
        
        if verbose:
            print(f"❌ 未在任何类或接口中找到方法: {method_name}")
            
            # 额外调试：检查文件中是否真的包含该方法名
            if method_name in code:
                print(f"🔍 文件中确实包含方法名 '{method_name}'，但解析器未找到对应的方法节点")
                # 显示包含该方法名的行
                method_lines = [f"第{i+1}行: {line.strip()}" for i, line in enumerate(code.split('\n')) if method_name in line]
                for line in method_lines[:5]:  # 只显示前5行
                    print(f"   {line}")
            else:
                print(f"🔍 文件中不包含方法名 '{method_name}'")
        
        return None
    except Exception as e:
        if verbose:
            print(f"⚠️  解析文件失败 {file_path}: {str(e)}")
            import traceback
            traceback.print_exc()
        return None

def _find_method_by_annotation(class_node, url_path, code, full_class_name, package_name, file_path, http_method=None, verbose=False):
    """根据Spring注解匹配URL路径和HTTP方法来查找方法

    Args:
        class_node: 类节点
        url_path: URL路径段，如 "list"
        code: 源代码
        full_class_name: 完整类名
        package_name: 包名
        file_path: 文件路径
        http_method: HTTP方法类型，如 "GET", "POST" 等，用于精确匹配
        verbose: 是否显示详细日志

    Returns:
        dict: 方法信息，如果找到匹配的方法，否则返回None
    """
    if verbose:
        print(f"   🔍 使用注解匹配策略查找URL路径: /{url_path}")
        if http_method:
            print(f"   🎯 HTTP方法类型: {http_method}")

    # 构建可能的URL匹配模式
    url_patterns = [
        f'/{url_path}"',           # "/list"
        f'/.*/{url_path}"',        # "/item/list"
        f'/{url_path}[^a-zA-Z]',   # "/list" 后面跟非字母字符
        f'/{url_path}$',           # "/list" 行尾
    ]

    if verbose:
        print(f"   📝 URL匹配模式: {url_patterns}")

    # 将代码按行分割，便于分析
    code_lines = code.split('\n')

    # 查找Spring映射注解
    mapping_annotations = [
        '@PostMapping', '@GetMapping', '@PutMapping',
        '@DeleteMapping', '@PatchMapping', '@RequestMapping'
    ]

    # 根据HTTP方法类型确定优先匹配的注解
    preferred_annotations = []
    if http_method:
        method_to_annotation = {
            'GET': '@GetMapping',
            'POST': '@PostMapping',
            'PUT': '@PutMapping',
            'DELETE': '@DeleteMapping',
            'PATCH': '@PatchMapping'
        }
        preferred_annotation = method_to_annotation.get(http_method.upper())
        if preferred_annotation:
            preferred_annotations = [preferred_annotation]
            if verbose:
                print(f"   🎯 优先匹配注解: {preferred_annotation}")

        # 也检查@RequestMapping注解中的method属性
        preferred_annotations.append('@RequestMapping')
    
    # 获取类中的所有方法
    methods = get_methods(class_node)

    # 存储匹配结果，优先返回HTTP方法匹配的结果
    exact_matches = []  # HTTP方法完全匹配的结果
    url_matches = []    # 仅URL匹配的结果

    for method in methods:
        if not hasattr(method, 'name') or not hasattr(method, 'position'):
            continue

        method_name = method.name
        method_line = method.position.line

        if verbose:
            print(f"   🔍 检查方法: {method_name} (行号: {method_line})")

        # 向前查找方法上的注解（查找方法定义前的几行）
        annotation_context = ""
        start_search_line = max(0, method_line - 10)  # 向前查找10行
        end_search_line = min(len(code_lines), method_line + 5)  # 向后查找5行

        for line_idx in range(start_search_line, end_search_line):
            if line_idx < len(code_lines):
                annotation_context += code_lines[line_idx] + "\n"

        if verbose:
            print(f"      📝 注解上下文:")
            for line_idx in range(start_search_line, end_search_line):
                if line_idx < len(code_lines):
                    line_content = code_lines[line_idx].strip()
                    if line_content:
                        print(f"         第{line_idx+1}行: {line_content}")

        # 检查是否包含Spring映射注解
        found_annotation = None
        for annotation in mapping_annotations:
            if annotation in annotation_context:
                found_annotation = annotation
                break

        if found_annotation:
            if verbose:
                print(f"      ✅ 找到映射注解: {found_annotation}")

            # 检查注解中是否包含目标URL路径
            url_matched = False
            for pattern in url_patterns:
                import re
                if re.search(pattern, annotation_context):
                    url_matched = True
                    if verbose:
                        print(f"      ✅ URL模式匹配成功: {pattern}")
                    break

            if url_matched:
                method_info = {
                    'full_name': f"{full_class_name}.{method_name}",
                    'class_name': full_class_name,
                    'method_name': method_name,
                    'package': package_name,
                    'file_path': file_path,
                    'class_code': extract_full_body(class_node, code),
                    'type': 'controller'
                }

                # 检查HTTP方法是否匹配
                if http_method and _check_http_method_match(found_annotation, annotation_context, http_method, verbose):
                    if verbose:
                        print(f"      🎯 HTTP方法匹配成功: {http_method} -> {found_annotation}")
                        print(f"      ✅ 找到精确匹配方法: {method_name}")
                    method_info['http_method_matched'] = True
                    exact_matches.append(method_info)
                else:
                    if verbose:
                        print(f"      ⚠️ URL匹配但HTTP方法不匹配或未指定")
                    method_info['http_method_matched'] = False
                    url_matches.append(method_info)
            else:
                if verbose:
                    print(f"      ❌ 找到注解但URL模式不匹配")
        else:
            if verbose:
                print(f"      ❌ 未找到映射注解")

    # 优先返回HTTP方法精确匹配的结果
    if exact_matches:
        if verbose:
            print(f"   ✅ 找到 {len(exact_matches)} 个HTTP方法精确匹配的方法，返回第一个")
        return exact_matches[0]
    elif url_matches:
        if verbose:
            print(f"   ⚠️ 未找到HTTP方法精确匹配，返回 {len(url_matches)} 个URL匹配中的第一个")
        return url_matches[0]

    if verbose:
        print(f"   ❌ 注解匹配策略未找到方法")

    return None

def _check_http_method_match(found_annotation, annotation_context, http_method, verbose=False):
    """检查找到的Spring注解是否与指定的HTTP方法匹配

    Args:
        found_annotation: 找到的Spring注解，如 "@GetMapping", "@PostMapping" 等
        annotation_context: 注解上下文代码
        http_method: 期望的HTTP方法，如 "GET", "POST" 等
        verbose: 是否显示详细日志

    Returns:
        bool: 如果HTTP方法匹配返回True，否则返回False
    """
    if not http_method:
        return True  # 如果没有指定HTTP方法，认为匹配

    http_method = http_method.upper()

    # 直接的注解匹配
    annotation_to_method = {
        '@GetMapping': 'GET',
        '@PostMapping': 'POST',
        '@PutMapping': 'PUT',
        '@DeleteMapping': 'DELETE',
        '@PatchMapping': 'PATCH'
    }

    if found_annotation in annotation_to_method:
        expected_method = annotation_to_method[found_annotation]
        is_match = expected_method == http_method
        if verbose:
            print(f"         🔍 注解 {found_annotation} 对应HTTP方法 {expected_method}, 期望 {http_method}, 匹配: {is_match}")
        return is_match

    # 处理@RequestMapping注解，需要检查method属性
    if found_annotation == '@RequestMapping':
        import re

        # 查找method属性的各种写法
        method_patterns = [
            rf'method\s*=\s*RequestMethod\.{http_method}',  # method = RequestMethod.GET
            rf'method\s*=\s*\{{\s*RequestMethod\.{http_method}\s*\}}',  # method = {RequestMethod.GET}
            rf'httpMethod\s*=\s*"{http_method}"',  # httpMethod = "GET" (在@ApiOperation中)
        ]

        for pattern in method_patterns:
            if re.search(pattern, annotation_context, re.IGNORECASE):
                if verbose:
                    print(f"         🔍 @RequestMapping中找到HTTP方法匹配: {pattern}")
                return True

        # 如果@RequestMapping没有指定method，默认支持所有方法
        if 'method' not in annotation_context and 'httpMethod' not in annotation_context:
            if verbose:
                print(f"         🔍 @RequestMapping未指定method，默认匹配所有HTTP方法")
            return True

        if verbose:
            print(f"         ❌ @RequestMapping中未找到匹配的HTTP方法 {http_method}")
        return False

    if verbose:
        print(f"         ⚠️ 未知注解类型: {found_annotation}")
    return False

def calculate_confidence(method_info, search_paths):
    """计算匹配的置信度
    
    Args:
        method_info: 方法信息
        search_paths: 搜索路径列表
    
    Returns:
        float: 置信度分数 (0-100)
    """
    confidence = 0
    class_code = method_info.get('class_code', '')
    method_type = method_info.get('type', 'other')
    
    # 基础分：根据类型给不同的基础分
    if method_type == 'controller':
        confidence += 50  # Controller类最高优先级
    elif method_type == 'feign_client':
        confidence += 30  # FeignClient次之
    else:
        confidence += 20  # 其他类型最低
    
    # 路径匹配分：每匹配一个路径段 +15分
    for path in search_paths:
        if path in class_code:
            confidence += 15
    
    # Controller类型分
    if '@RestController' in class_code:
        confidence += 15
    elif '@Controller' in class_code:
        confidence += 10
    elif '@FeignClient' in class_code:
        confidence += 5  # FeignClient给较低分数
    
    # 类名匹配分
    if any(path.strip('/') in method_info['class_name'] for path in search_paths[1:]):
        confidence += 10
    
    return min(confidence, 100)  # 最高100分

def find_feign_client_implementation(feign_interface_name, repo_path, method_name, verbose=False):
    """专门搜索FeignClient接口的实现类
    
    Args:
        feign_interface_name: FeignClient接口的完整类名
        repo_path: 仓库路径
        method_name: 方法名
        verbose: 是否显示详细日志
    
    Returns:
        tuple: (method_info, file_path, confidence) 如果找到实现类，否则返回 (None, None, 0)
    """
    if verbose:
        print(f"🔍 搜索FeignClient实现类: {feign_interface_name}")
    
    # 提取接口简名
    interface_simple_name = feign_interface_name.split('.')[-1]
    
    # 搜索实现模式
    implementation_patterns = [
        f"implements {interface_simple_name}",
        f"implements.*{interface_simple_name}",
        # 有时候会有全限定名的实现
        f"implements {feign_interface_name}",
        f"implements.*{feign_interface_name}"
    ]
    
    if verbose:
        print(f"📝 搜索实现模式: {implementation_patterns}")
    
    # 收集所有候选实现类文件
    implementation_candidates = []
    
    for pattern in implementation_patterns:
        try:
            candidate_files = search_files_by_content(repo_path, pattern)
            implementation_candidates.extend(candidate_files)
            if verbose:
                print(f"   模式 '{pattern}' 找到 {len(candidate_files)} 个候选文件")
        except Exception as e:
            if verbose:
                print(f"   模式 '{pattern}' 搜索失败: {str(e)}")
            continue
    
    # 去重
    implementation_candidates = list(set(implementation_candidates))
    if verbose:
        print(f"📋 总共找到 {len(implementation_candidates)} 个候选实现文件")
    
    if not implementation_candidates:
        if verbose:
            print("❌ 未找到FeignClient实现类")
        return None, None, 0
    
    # 在候选文件中查找方法
    best_implementation = None
    highest_confidence = 0
    
    for candidate_file in implementation_candidates:
        if verbose:
            print(f"🔍 检查候选实现文件: {candidate_file}")
        
        # 验证是否真的实现了该接口
        try:
            with open(candidate_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含RestController注解和implements声明
            has_rest_controller = '@RestController' in content or '@Controller' in content
            has_implements = f"implements {interface_simple_name}" in content or f"implements.*{interface_simple_name}" in content
            
            if verbose:
                print(f"   📋 RestController注解: {has_rest_controller}")
                print(f"   📋 implements接口: {has_implements}")
            
            if has_rest_controller and has_implements:
                # 提取方法信息
                method_info = extract_method_from_file(candidate_file, method_name, None, verbose=False)
                if method_info:
                    # 强制设置为controller类型（因为它是实现类）
                    method_info['type'] = 'controller_implementation'
                    
                    # 计算置信度 - 实现类应该有更高的置信度
                    confidence = 80  # 基础高分
                    if '@RestController' in content:
                        confidence += 15
                    elif '@Controller' in content:
                        confidence += 10
                    
                    # 检查类名是否有意义的匹配
                    class_name = method_info.get('class_name', '').split('.')[-1]
                    if 'Rpc' in class_name or 'Controller' in class_name or 'Impl' in class_name:
                        confidence += 5
                    
                    confidence = min(confidence, 100)
                    
                    if verbose:
                        print(f"🎯 找到实现类方法: {method_info['full_name']} (置信度: {confidence})")
                    
                    if confidence > highest_confidence:
                        highest_confidence = confidence
                        best_implementation = (method_info, candidate_file, confidence)
                else:
                    if verbose:
                        print(f"   ⚠️ 文件中未找到方法 '{method_name}'")
            else:
                if verbose:
                    print(f"   ❌ 文件不符合实现类条件")
                    
        except Exception as e:
            if verbose:
                print(f"   ⚠️ 解析文件失败: {str(e)}")
            continue
    
    if best_implementation:
        if verbose:
            print(f"✅ 找到最佳FeignClient实现: {best_implementation[0]['full_name']} (置信度: {best_implementation[2]})")
        return best_implementation
    else:
        if verbose:
            print("❌ 未找到合适的FeignClient实现类")
        return None, None, 0

def find_exact_controller_method(repo_path, request_url, verbose=False):
    """使用逆向搜索策略找到精确的Controller方法
    
    Args:
        repo_path: 仓库路径
        request_url: 请求URL，如 "api/v1/AcceptBill/saveDetailsBatch"
        verbose: 是否显示详细日志
    
    Returns:
        tuple: (method_full_name, file_path, confidence_score)
    """
    if verbose:
        print(f"🔍 使用逆向搜索策略分析URL: {request_url}")

    # 1. 提取URL路径段和HTTP方法
    search_paths, http_method = extract_url_segments(request_url)
    if verbose:
        print(f"📝 提取的搜索路径: {search_paths}")
        if http_method:
            print(f"🎯 提取的HTTP方法: {http_method}")

    if not search_paths:
        if verbose:
            print("❌ 无法从URL中提取有效的搜索路径")
        return None, None, 0
    
    # 2. 第一步：用最具体的路径（通常是方法名）搜索
    method_path = search_paths[0]
    search_pattern = f'{method_path}"'  # 添加双引号以提高搜索精度
    if verbose:
        print(f"🔎 第一步搜索: 查找包含 '{search_pattern}' 的文件")
    
    candidate_files = search_files_by_content(repo_path, search_pattern)
    if verbose:
        print(f"📋 找到 {len(candidate_files)} 个候选文件")
    
    if not candidate_files:
        if verbose:
            print("❌ 未找到包含方法路径的文件")
            print("🔄 立即尝试备选搜索策略：使用不带'/'的搜索模式")
        
        # 6. 备选搜索策略：使用不带"/"的搜索模式
        fallback_search_paths = [path.strip('/') for path in search_paths]
        if verbose:
            print(f"📝 备选搜索路径: {fallback_search_paths}")
        
        # 重新搜索，使用不带"/"的模式
        fallback_method_name = fallback_search_paths[0]
        fallback_pattern = f'{fallback_method_name}'  # 不带引号的简单搜索
        if verbose:
            print(f"🔎 备选搜索: 查找包含 '{fallback_pattern}' 的文件")
        
        candidate_files = search_files_by_content(repo_path, fallback_pattern)
        if verbose:
            print(f"📋 备选搜索找到 {len(candidate_files)} 个候选文件")
        
        if not candidate_files:
            if verbose:
                print("❌ 备选搜索策略也未找到候选文件")
            return None, None, 0
        
        # 使用备选搜索路径替换原始路径
        search_paths = fallback_search_paths
        method_path = fallback_method_name
        if verbose:
            print("✅ 备选搜索找到候选文件，继续使用不带'/'的搜索模式进行后续处理")
    
    # 3. 逐级过滤：在候选文件中查找更高级的路径
    final_candidates = candidate_files.copy()
    
    for i, higher_path in enumerate(search_paths[1:], 1):
        if verbose:
            print(f"🔎 第{i+1}步过滤: 在候选文件中查找 '{higher_path}'")
        
        filtered_candidates = []
        for file_path in final_candidates:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if higher_path in content:
                        filtered_candidates.append(file_path)
            except:
                continue
        
        if filtered_candidates:
            final_candidates = filtered_candidates
            if verbose:
                print(f"📋 过滤后剩余 {len(final_candidates)} 个候选文件")
        else:
            if verbose:
                print(f"⚠️  过滤后无候选文件，保持上一步结果")
            break
    
    # 4. 从最终候选文件中提取方法信息
    best_match = None
    highest_confidence = 0
    feign_client_matches = []  # 记录找到的FeignClient
    
    for file_path in final_candidates:
        method_info = extract_method_from_file(file_path, method_path.strip('/'), http_method, verbose=verbose)
        if method_info:
            confidence = calculate_confidence(method_info, search_paths)

            # 如果有HTTP方法匹配，给予额外的置信度加分
            if http_method and method_info.get('http_method_matched'):
                confidence += 20
                if verbose:
                    print(f"🎯 候选方法: {method_info['full_name']} (类型: {method_info.get('type', 'unknown')}, 置信度: {confidence}, HTTP方法匹配: ✅)")
            else:
                if verbose:
                    print(f"🎯 候选方法: {method_info['full_name']} (类型: {method_info.get('type', 'unknown')}, 置信度: {confidence})")

            # 记录FeignClient以备后用
            if method_info.get('type') == 'feign_client':
                feign_client_matches.append((method_info, file_path, confidence))

            if confidence > highest_confidence:
                highest_confidence = confidence
                best_match = (method_info['full_name'], file_path, confidence)
    
    # 5. 优化的FeignClient实现类搜索逻辑
    if best_match and len(feign_client_matches) > 0:
        best_method_info = None
        for feign_info, feign_file, feign_confidence in feign_client_matches:
            if feign_info['full_name'] == best_match[0]:
                best_method_info = feign_info
                break
        
        if best_method_info and best_method_info.get('type') == 'feign_client':
            if verbose:
                print(f"🔄 检测到最佳匹配是FeignClient，开始搜索实现类...")
            
            # 新增：专门搜索FeignClient实现类
            feign_interface_name = best_method_info['class_name']
            implementation_result = find_feign_client_implementation(
                feign_interface_name, 
                repo_path, 
                method_path.strip('/'), 
                verbose=verbose
            )
            
            if implementation_result[0]:  # 找到了实现类
                impl_method_info, impl_file_path, impl_confidence = implementation_result
                if verbose:
                    print(f"🎯 找到FeignClient实现类: {impl_method_info['full_name']} (置信度: {impl_confidence})")
                
                # 实现类优先于接口，即使置信度相近也要替换
                if impl_confidence >= 70:  # 实现类的最低置信度要求
                    if verbose:
                        print(f"✅ 使用FeignClient实现类替换接口")
                    best_match = (impl_method_info['full_name'], impl_file_path, impl_confidence)
                    highest_confidence = impl_confidence
                else:
                    if verbose:
                        print(f"⚠️ 实现类置信度不够高 ({impl_confidence}), 保持FeignClient接口")
            else:
                if verbose:
                    print(f"⚠️ 未找到FeignClient实现类，使用接口作为结果")
            
            # 原有的通用Controller搜索逻辑作为备选方案
            if not implementation_result[0]:
                if verbose:
                    print(f"🔄 尝试通用Controller搜索策略...")
                
                # 尝试搜索可能的Controller类名
                controller_search_patterns = []
                
                # 从URL路径推测Controller名称
                if len(search_paths) > 1:
                    controller_path = search_paths[1].strip('/')
                    controller_search_patterns.extend([
                        f"{controller_path}Controller",
                        f"{controller_path.capitalize()}Controller",
                        f"@RequestMapping.*{search_paths[1]}"
                    ])
                
                # 从方法名推测
                method_name = search_paths[0].strip('/')
                controller_search_patterns.extend([
                    f"{method_name}Controller",
                    f"@PostMapping.*{search_paths[0]}",
                    f"@GetMapping.*{search_paths[0]}",
                    f"@RequestMapping.*{search_paths[0]}"
                ])
                
                if verbose:
                    print(f"🔍 搜索Controller实现，使用模式: {controller_search_patterns}")
                
                controller_candidates = []
                for pattern in controller_search_patterns:
                    try:
                        pattern_files = search_files_by_content(repo_path, pattern)
                        controller_candidates.extend(pattern_files)
                    except:
                        continue
                
                # 去重
                controller_candidates = list(set(controller_candidates))
                if verbose:
                    print(f"📋 Controller搜索找到 {len(controller_candidates)} 个候选文件")
                
                # 在Controller候选文件中查找方法
                for controller_file in controller_candidates:
                    controller_method_info = extract_method_from_file(controller_file, method_path.strip('/'), http_method, verbose=False)
                    if controller_method_info and controller_method_info.get('type') == 'controller':
                        controller_confidence = calculate_confidence(controller_method_info, search_paths)
                        if verbose:
                            print(f"🎯 找到Controller实现: {controller_method_info['full_name']} (置信度: {controller_confidence})")
                        
                        # 如果Controller的置信度足够高，替换最佳匹配
                        if controller_confidence > highest_confidence or controller_confidence >= 70:
                            if verbose:
                                print(f"✅ Controller实现置信度更高，替换最佳匹配")
                            best_match = (controller_method_info['full_name'], controller_file, controller_confidence)
                            highest_confidence = controller_confidence
                            break
    
    if best_match:
        if verbose:
            print(f"✅ 最佳匹配: {best_match[0]} (置信度: {best_match[2]})")
        return best_match
    else:
        if verbose:
            print("❌ 未能从候选文件中提取到有效的方法信息")
        return None, None, 0

def find_method_by_url(request_url, repo_path, verbose=True):
    """根据请求URL找到对应的Controller方法（使用逆向搜索策略）
    
    Args:
        request_url: 请求URL
        repo_path: 仓库路径
        verbose: 是否显示详细日志
    
    Returns:
        str: 对应的方法全限定名，如果未找到返回None
    """
    if verbose:
        print(f"🚀 开始逆向搜索策略")
        print(f"   URL: {request_url}")
        print(f"   仓库: {repo_path}")
    
    result = find_exact_controller_method(repo_path, request_url, verbose=verbose)
    
    if result[0]:  # method_full_name
        if verbose:
            print(f"✅ 成功找到方法: {result[0]}")
            print(f"📁 文件位置: {result[1]}")
            print(f"🎯 置信度: {result[2]}")
        return result[0]
    else:
        if verbose:
            print("❌ 未找到匹配的Controller方法")
        return None

# ========== 使用示例 ==========
# 
# from url_to_method_mapper import find_method_by_url
#
# # 基本使用
# method = find_method_by_url("/AcceptBill/saveDetailsBatch", "/path/to/repo")
#
# # 支持的URL格式:
# # - "api/v1/AcceptBill/saveDetailsBatch"  (完整路径)
# # - "/AcceptBill/saveDetailsBatch"        (简化路径)  
# # - "AcceptBill/saveDetailsBatch"         (最简路径)
#
# # 搜索策略:
# # 步骤1: 搜索 '/saveDetailsBatch"' (方法级精确搜索)
# # 步骤2: 在结果中搜索 '/AcceptBill' (Controller级过滤)
# # 步骤3: 计算置信度，选择最佳匹配 