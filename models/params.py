"""
数据模型定义

定义工具输入参数的Pydantic模型
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, model_validator


class CodeSearchParams(BaseModel):
    """代码搜索工具的输入模型"""
    file_name: Optional[str] = Field(default="", description="目标文件名，如WorkBenchService.java")
    class_name: Optional[str] = Field(default="", description="类名，如WorkBenchService")
    function_name: Optional[str] = Field(default="", description="函数名，如sendGet")
    error_line: Optional[int] = Field(default=0, description="报错行号")
    request_url: Optional[str] = Field(default="", description="请求url，用于识别代码中接口定义位置")
    sql_fragment: Optional[str] = Field(default="", description="SQL语句片段，用于搜索包含该SQL的代码")
    
    @model_validator(mode='after')
    def validate_at_least_one_field(self):
        """验证至少提供一个搜索条件"""
        fields_to_check = [
            self.file_name,
            self.class_name, 
            self.function_name,
            self.request_url,
            self.sql_fragment
        ]
        
        # 检查是否有非空字符串字段
        has_string_field = any(field and field.strip() for field in fields_to_check)
        
        # 检查是否有有效的行号
        has_valid_line = self.error_line and self.error_line > 0
        
        if not has_string_field and not has_valid_line:
            raise ValueError(
                "至少需要提供以下搜索条件之一：file_name, class_name, function_name, error_line(>0), request_url, sql_fragment"
            )
        
        return self


class TableDDLSearchParams(BaseModel):
    """DDL搜索工具的输入模型"""
    table_name: Optional[str] = Field(default="", description="要搜索的单个表名，如user_info")
    table_name_list: Optional[List[str]] = Field(default=None, description="要搜索的表名列表，如['user_info', 'order_data']")
    exact_match: Optional[bool] = Field(default=True, description="是否执行精确匹配，若为False则进行模糊匹配")
    
    @model_validator(mode='after')
    def validate_table_names(self):
        """验证至少提供一个表名"""
        has_single_table = self.table_name and self.table_name.strip()
        has_table_list = self.table_name_list and len(self.table_name_list) > 0
        
        if not has_single_table and not has_table_list:
            raise ValueError("至少需要提供 table_name 或 table_name_list 中的一个")
        
        return self 


class AppCodeSearchParams(BaseModel):
    """应用代码搜索参数"""
    app_code: str = Field(..., description="服务编号，用于查询和定位对应的代码库")
    server_url: str = Field(..., description="服务接口URL，如自带GET/PUT前缀请保留")
    file_name: str = Field("", description="要进一步搜索的文件名（可选）")
    class_name: str = Field("", description="要进一步搜索的类名（可选）")
    method_name: str = Field("", description="要进一步搜索的方法名（可选）")
    context: dict = Field(None, description="上下文信息（可选）")
    
    @model_validator(mode='after')
    def validate_search_params(self):
        """验证搜索参数"""
        # server_url是必需的
        if not self.server_url or not self.server_url.strip():
            raise ValueError("server_url是必需参数，不能为空")
        
        # app_code是必需的
        if not self.app_code or not self.app_code.strip():
            raise ValueError("app_code是必需参数，不能为空")
            
        # file_name, class_name, method_name都是可选的
        # 至少要有server_url才能进行AST分析
        
        return self


class DDLInfoSearchParams(BaseModel):
    """DDL信息搜索参数"""
    table_names: List[str] = Field(description="要查询的表名列表，必须提供至少一个表名")
    start_time: str = Field(description="开始时间，格式如：2025-07-07 09:22:34")
    app_code: str = Field(default="", description="服务编号，用于筛选对应的数据库连接，必须提供")
    context: Optional[Dict[str, Any]] = Field(default=None, description="不需要大模型主动传递")
    
    @model_validator(mode='after')
    def validate_required_fields(self):
        """验证必需字段"""
        if not self.table_names or len(self.table_names) == 0:
            raise ValueError("table_names 必须提供至少一个表名")
        
        # 验证表名不能为空
        valid_table_names = [name for name in self.table_names if name and name.strip()]
        if len(valid_table_names) == 0:
            raise ValueError("table_names 中必须包含至少一个有效的表名")
        
        # 验证start_time不能为空
        if not self.start_time or not self.start_time.strip():
            raise ValueError("start_time 是必需的且不能为空")
        
        # 验证app_code不能为空
        if not self.app_code or not self.app_code.strip():
            raise ValueError("app_code 是必需的且不能为空")
        
        return self 