import json
from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool


from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class Issue:
    id: Optional[int] = None
    trace_id: Optional[str] = None
    issue_type: Optional[str] = None

    # root_cause.origin_point 字段
    rc_file_name: Optional[str] = None
    rc_class_name: Optional[str] = None
    rc_method_name: Optional[str] = None
    rc_line_number: Optional[str] = None
    rc_service_url: Optional[str] = None
    rc_sql: Optional[str] = None
    rc_table_names: Optional[str] = None
    rc_app_code: Optional[str] = None
    rc_middleware: Optional[str] = None

    # root_cause.origin_point.timestamp 字段
    rc_start_time: Optional[datetime] = None
    rc_end_time: Optional[datetime] = None
    rc_duration_ms: Optional[int] = None
    rc_occurrence_time: Optional[datetime] = None

    # root_cause.error_description
    rc_error_description: Optional[str] = None

    # final_manifestation.location 字段
    fm_file_name: Optional[str] = None
    fm_class_name: Optional[str] = None
    fm_method_name: Optional[str] = None
    fm_line_number: Optional[str] = None
    fm_service_url: Optional[str] = None
    fm_app_code: Optional[str] = None

    # final_manifestation.location.timestamp 字段
    fm_start_time: Optional[datetime] = None
    fm_end_time: Optional[datetime] = None
    fm_duration_ms: Optional[int] = None
    fm_occurrence_time: Optional[datetime] = None

    # final_manifestation 其他字段
    fm_error_type: Optional[str] = None
    fm_error_info: Optional[str] = None
    fm_duration: Optional[int] = None
    fm_impact_scope: Optional[str] = None
    raw_json: Optional[str] = None
    trace_hash: Optional[str] = None

    # 基础字段
    tenant_id: Optional[str] = None
    version: Optional[int] = None
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None

    @staticmethod
    def from_dict(data: dict, trace_id: str = None, trace_hash=trace_hash):
        root_cause = data.get("root_cause", {})
        origin_point = root_cause.get("origin_point", {})
        op_timestamp = origin_point.get("timestamp", {})

        final_manifestation = data.get("final_manifestation", {})
        fm_location = final_manifestation.get("location", {})
        fm_timestamp = fm_location.get("timestamp", {})

        return Issue(
            trace_hash=trace_hash,
            issue_type=data.get("issue_type"),
            trace_id= trace_id,

            # root_cause.origin_point
            rc_file_name=origin_point.get("file_name"),
            rc_class_name=origin_point.get("class_name"),
            rc_method_name=origin_point.get("method_name"),
            rc_line_number=origin_point.get("line_number"),
            rc_service_url=origin_point.get("service_url"),
            rc_sql=origin_point.get("sql"),
            rc_table_names=",".join(origin_point.get("table_name_list")) if origin_point.get(
                "table_name_list") else None,
            rc_app_code=origin_point.get("app_code"),
            rc_middleware=origin_point.get("middleware"),
            rc_start_time=datetime.strptime(op_timestamp.get("start_time"), "%Y-%m-%d %H:%M:%S.%f") if op_timestamp.get(
                "start_time") else None,
            rc_end_time=datetime.strptime(op_timestamp.get("end_time"), "%Y-%m-%d %H:%M:%S.%f") if op_timestamp.get(
                "end_time") else None,
            rc_duration_ms=op_timestamp.get("duration_ms"),
            rc_occurrence_time=datetime.strptime(op_timestamp.get("occurrence_time"),
                                                 "%Y-%m-%d %H:%M:%S.%f") if op_timestamp.get(
                "occurrence_time") else None,
            rc_error_description=root_cause.get("error_description"),

            # final_manifestation.location
            fm_file_name=fm_location.get("file_name"),
            fm_class_name=fm_location.get("class_name"),
            fm_method_name=fm_location.get("method_name"),
            fm_line_number=fm_location.get("line_number"),
            fm_service_url=fm_location.get("service_url"),
            fm_app_code=fm_location.get("app_code"),

            # final_manifestation.location.timestamp
            fm_start_time=datetime.strptime(fm_timestamp.get("start_time"), "%Y-%m-%d %H:%M:%S.%f") if fm_timestamp.get(
                "start_time") else None,
            fm_end_time=datetime.strptime(fm_timestamp.get("end_time"), "%Y-%m-%d %H:%M:%S.%f") if fm_timestamp.get(
                "end_time") else None,
            fm_duration_ms=fm_timestamp.get("duration_ms"),
            fm_occurrence_time=datetime.strptime(fm_timestamp.get("occurrence_time"),
                                                 "%Y-%m-%d %H:%M:%S.%f") if fm_timestamp.get(
                "occurrence_time") else None,

            # final_manifestation 其他字段
            fm_error_type=final_manifestation.get("error_type"),
            fm_error_info=final_manifestation.get("error_info"),
            fm_duration=final_manifestation.get("duration"),
            fm_impact_scope=final_manifestation.get("impact_scope"),
            raw_json=json.dumps(data, ensure_ascii=False),

            tenant_id="",
            version=0,
            create_user="-1",
            create_time=datetime.now(),
            last_modify_user="-1",
            last_modify_time=datetime.now()
        )



class IssueDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, issue: Issue) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO issue (
                    trace_id,
                    issue_type,
                    rc_file_name, 
                    rc_class_name, 
                    rc_method_name, 
                    rc_line_number,
                    rc_service_url, rc_sql, rc_table_names, rc_app_code, rc_middleware,
                    rc_start_time, rc_end_time, rc_duration_ms, rc_occurrence_time,
                    rc_error_description,
                    fm_file_name, fm_class_name, fm_method_name, fm_line_number,
                    fm_service_url, fm_app_code,
                    fm_start_time, fm_end_time, fm_duration_ms, fm_occurrence_time,
                    fm_error_type, fm_error_info, fm_duration, fm_impact_scope, raw_json,
                    tenant_id, version, create_user, create_time,
                    last_modify_user, last_modify_time, trace_hash
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                          %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                          %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                          %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    issue.trace_id,
                    issue.issue_type,
                    issue.rc_file_name,
                    issue.rc_class_name,
                    issue.rc_method_name,
                    issue.rc_line_number,
                    issue.rc_service_url,
                    issue.rc_sql,
                    issue.rc_table_names,
                    issue.rc_app_code,
                    issue.rc_middleware,
                    issue.rc_start_time,
                    issue.rc_end_time,
                    issue.rc_duration_ms,
                    issue.rc_occurrence_time,
                    issue.rc_error_description,
                    issue.fm_file_name,
                    issue.fm_class_name,
                    issue.fm_method_name,
                    issue.fm_line_number,
                    issue.fm_service_url,
                    issue.fm_app_code,
                    issue.fm_start_time,
                    issue.fm_end_time,
                    issue.fm_duration_ms,
                    issue.fm_occurrence_time,
                    issue.fm_error_type,
                    issue.fm_error_info,
                    issue.fm_duration,
                    issue.fm_impact_scope,
                    issue.raw_json,
                    issue.tenant_id,
                    issue.version,
                    issue.create_user,
                    issue.create_time,
                    issue.last_modify_user,
                    issue.last_modify_time,
                    issue.trace_hash
                ))

                cur.execute("""
                                SELECT id FROM issue
                                WHERE create_time = %s AND trace_id = %s
                                ORDER BY id DESC LIMIT 1
                            """, (
                issue.create_time,
                issue.trace_id))

                last_id = cur.fetchone()['id']

                conn.commit()
                print(f"Last log process ID: {last_id}")
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, issue: Issue, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([
                    issue.id            # id
                ])

                sql = f"""
                UPDATE issue
                SET {set_clause}, last_modify_time=NOW()
                WHERE id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, issue_id: int) -> Optional[Issue]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM issue WHERE id = %s", (issue_id,))
                result = cur.fetchone()
                if not result:
                    return None

                return Issue(
                    id=result['id'],
                    trace_id=result['trace_id'],
                    trace_hash=result['trace_hash'],
                    issue_type=result['issue_type'],

                    rc_file_name=result['rc_file_name'],
                    rc_class_name=result['rc_class_name'],
                    rc_method_name=result['rc_method_name'],
                    rc_line_number=result['rc_line_number'],
                    rc_service_url=result['rc_service_url'],
                    rc_sql=result['rc_sql'],
                    rc_table_names=result['rc_table_names'],
                    rc_app_code=result['rc_app_code'],
                    rc_middleware=result['rc_middleware'],

                    rc_start_time=result['rc_start_time'],
                    rc_end_time=result['rc_end_time'],
                    rc_duration_ms=result['rc_duration_ms'],
                    rc_occurrence_time=result['rc_occurrence_time'],
                    rc_error_description=result['rc_error_description'],

                    fm_file_name=result['fm_file_name'],
                    fm_class_name=result['fm_class_name'],
                    fm_method_name=result['fm_method_name'],
                    fm_line_number=result['fm_line_number'],
                    fm_service_url=result['fm_service_url'],
                    fm_app_code=result['fm_app_code'],

                    fm_start_time=result['fm_start_time'],
                    fm_end_time=result['fm_end_time'],
                    fm_duration_ms=result['fm_duration_ms'],
                    fm_occurrence_time=result['fm_occurrence_time'],
                    fm_error_type=result['fm_error_type'],
                    fm_error_info=result['fm_error_info'],
                    fm_duration=result['fm_duration'],
                    fm_impact_scope=result['fm_impact_scope'],
                    raw_json=result['raw_json'],

                    tenant_id=result['tenant_id'],
                    version=result['version'],
                    create_user=result['create_user'],
                    create_time=result['create_time'],
                    last_modify_user=result['last_modify_user'],
                    last_modify_time=result['last_modify_time']
                )
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[Issue]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM issue ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    Issue(
                        id=row['id'],
                        trace_id=row['trace_id'],
                        trace_hash=row['trace_hash'],
                        issue_type=row['issue_type'],

                        rc_file_name=row['rc_file_name'],
                        rc_class_name=row['rc_class_name'],
                        rc_method_name=row['rc_method_name'],
                        rc_line_number=row['rc_line_number'],
                        rc_service_url=row['rc_service_url'],
                        rc_sql=row['rc_sql'],
                        rc_table_names=row['rc_table_names'],
                        rc_app_code=row['rc_app_code'],
                        rc_middleware=row['rc_middleware'],

                        rc_start_time=row['rc_start_time'],
                        rc_end_time=row['rc_end_time'],
                        rc_duration_ms=row['rc_duration_ms'],
                        rc_occurrence_time=row['rc_occurrence_time'],
                        rc_error_description=row['rc_error_description'],

                        fm_file_name=row['fm_file_name'],
                        fm_class_name=row['fm_class_name'],
                        fm_method_name=row['fm_method_name'],
                        fm_line_number=row['fm_line_number'],
                        fm_service_url=row['fm_service_url'],
                        fm_app_code=row['fm_app_code'],

                        fm_start_time=row['fm_start_time'],
                        fm_end_time=row['fm_end_time'],
                        fm_duration_ms=row['fm_duration_ms'],
                        fm_occurrence_time=row['fm_occurrence_time'],
                        fm_error_type=row['fm_error_type'],
                        fm_error_info=row['fm_error_info'],
                        fm_duration=row['fm_duration'],
                        fm_impact_scope=row['fm_impact_scope'],
                        raw_json=row['raw_json'],

                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)

