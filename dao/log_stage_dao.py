
from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool


@dataclass
class LogStage:
    id: Optional[int] = None
    log_process_id: Optional[int] = None
    trace_id: Optional[str] = None
    trace_hash: Optional[str] = None
    solution_id: Optional[int] = None
    catalog_name: Optional[str] = None
    stage_name: Optional[str] = None
    stage_status: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_ms: Optional[int] = None
    comment: Optional[str] = None
    details: Optional[str] = None
    messages: Optional[str] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = 0
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None


class LogStageDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, stage: LogStage) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO log_stage (
                    log_process_id, trace_id, trace_hash,
                    solution_id, catalog_name, stage_name, stage_status,
                    start_time, end_time, duration_ms,
                    comment, details, messages, tenant_id,
                    version, create_user, create_time,
                    last_modify_user, last_modify_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    stage.log_process_id,
                    stage.trace_id,
                    stage.trace_hash,
                    stage.solution_id,
                    stage.catalog_name,
                    stage.stage_name,
                    stage.stage_status,
                    stage.start_time,
                    stage.end_time,
                    stage.duration_ms,
                    stage.comment,
                    stage.details,
                    stage.messages,
                    stage.tenant_id,
                    stage.version,
                    stage.create_user,
                    stage.create_time,
                    stage.last_modify_user,
                    stage.last_modify_time
                ))

                cur.execute("""
                    SELECT id FROM log_stage
                    WHERE create_time = %s AND trace_id = %s
                    ORDER BY id DESC LIMIT 1
                """, (stage.create_time, stage.trace_id))
                last_id = cur.fetchone()['id']

                conn.commit()
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, stage: LogStage, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([stage.id])

                sql = f"""
                UPDATE log_stage
                SET {set_clause}, last_modify_time=NOW(), version=version+1
                WHERE id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, stage_id: int) -> Optional[LogStage]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_stage WHERE id = %s", (stage_id,))
                result = cur.fetchone()
                if result:
                    return LogStage(
                        id=result['id'],
                        log_process_id=result['log_process_id'],
                        trace_id=result['trace_id'],
                        trace_hash=result['trace_hash'],
                        solution_id=result['solution_id'],
                        stage_name=result['stage_name'],
                        stage_status=result['stage_status'],
                        start_time=result['start_time'],
                        end_time=result['end_time'],
                        duration_ms=result['duration_ms'],
                        comment=result['comment'],
                        details=result['details'],
                        messages=result['messages'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        catalog_name=result['catalog_name']
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_log_stages_by_process_id_version(self, process_id: int, version: int) -> list[LogStage]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_stage WHERE log_process_id = %s and version = %s", (process_id, version))
                rows = cur.fetchall()
                return [
                    LogStage(
                        id=row['id'],
                        log_process_id=row['log_process_id'],
                        trace_id=row['trace_id'],
                        trace_hash=row['trace_hash'],
                        solution_id=row['solution_id'],
                        stage_name=row['stage_name'],
                        stage_status=row['stage_status'],
                        start_time=row['start_time'],
                        end_time=row['end_time'],
                        duration_ms=row['duration_ms'],
                        comment=row['comment'],
                        details=row['details'],
                        messages=row['messages'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        catalog_name=row['catalog_name']
                    )
                    for row in rows
                ]
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[LogStage]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_stage ORDER BY start_time DESC;")
                rows = cur.fetchall()
                return [
                    LogStage(
                        id=row['id'],
                        log_process_id=row['log_process_id'],
                        trace_id=row['trace_id'],
                        trace_hash=row['trace_hash'],
                        solution_id=row['solution_id'],
                        stage_name=row['stage_name'],
                        stage_status=row['stage_status'],
                        start_time=row['start_time'],
                        end_time=row['end_time'],
                        duration_ms=row['duration_ms'],
                        comment=row['comment'],
                        details=row['details'],
                        messages=row['messages'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        catalog_name=row['catalog_name']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)
