from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool

@dataclass
class JobTrigger:
    id: Optional[int] = None
    job_name: Optional[str] = None
    trigger_time: Optional[datetime] = None
    status: Optional[str] = None
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = None

class JobTriggerDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, job: JobTrigger) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO job_trigger (
                    job_name, trigger_time, status,
                    create_user, create_time, last_modify_user,
                    last_modify_time, tenant_id, version
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    job.job_name,
                    job.trigger_time,
                    job.status,
                    job.create_user,
                    job.create_time,
                    job.last_modify_user,
                    job.last_modify_time,
                    job.tenant_id,
                    job.version
                ))

                cur.execute("""
                    SELECT id FROM job_trigger
                    WHERE create_time = %s AND trigger_time = %s
                    ORDER BY id DESC LIMIT 1
                """, (job.create_time, job.trigger_time))
                last_id = cur.fetchone()['id']

                conn.commit()

                print(f"Last log job ID: {last_id}")
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, job: JobTrigger, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([job.version + 1, job.version, job.id])
                
                sql = f"""
                UPDATE job_trigger
                SET {set_clause}, last_modify_time=NOW(), version=%s
                WHERE version=%s AND id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, job_id: int) -> Optional[JobTrigger]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT * FROM job_trigger
                    WHERE id = %s
                """, (job_id,))
                result = cur.fetchone()
                if result:
                    return JobTrigger(
                        id=result['id'],
                        job_name=result['job_name'],
                        trigger_time=result['trigger_time'],
                        status=result['status'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        tenant_id=result['tenant_id'],
                        version=result['version']
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[JobTrigger]:
        """获取所有 job_trigger 记录。"""
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM job_trigger ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    JobTrigger(
                        id=row['id'],
                        job_name=row['job_name'],
                        trigger_time=row['trigger_time'],
                        status=row['status'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        tenant_id=row['tenant_id'],
                        version=row['version']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)