from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool


@dataclass
@dataclass
class Solution:
    id: Optional[int] = None
    trace_id: Optional[str] = None
    raw_json: Optional[str] = None
    trace_hash: Optional[str] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = None
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None
    confirm_status: Optional[str] = None
    issued_status: Optional[str] = None
    generate_time: Optional[datetime] = None
    user_name: Optional[str] = None


class SolutionDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, solution: Solution) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO solution (
                    trace_id, raw_json, trace_hash,
                    tenant_id, version, create_user,
                    create_time, last_modify_user, last_modify_time, confirm_status, issued_status, generate_time, user_name
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    solution.trace_id,
                    solution.raw_json,
                    solution.trace_hash,
                    solution.tenant_id,
                    solution.version,
                    solution.create_user,
                    solution.create_time,
                    solution.last_modify_user,
                    solution.last_modify_time,
                    solution.confirm_status,
                    solution.issued_status,
                    solution.generate_time,
                    solution.user_name
                ))

                cur.execute("""
                    SELECT id FROM solution
                    WHERE create_time = %s AND trace_id = %s
                    ORDER BY id DESC LIMIT 1
                """, (solution.create_time, solution.trace_id))
                last_id = cur.fetchone()['id']

                conn.commit()
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, solution: Solution, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([solution.id])

                sql = f"""
                UPDATE solution
                SET {set_clause}, last_modify_time=NOW()
                WHERE id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, solution_id: int) -> Optional[Solution]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution WHERE id = %s", (solution_id,))
                result = cur.fetchone()
                if result:
                    return Solution(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        raw_json=result['raw_json'],
                        trace_hash=result['trace_hash'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        confirm_status=result['confirm_status'],
                        issued_status=result['issued_status'],
                        generate_time=result['generate_time'],
                        user_name=result['user_name'],
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_trace_id(self, trace_id: int) -> Optional[Solution]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution WHERE trace_id = %s", (trace_id,))
                result = cur.fetchone()
                if result:
                    return Solution(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        raw_json=result['raw_json'],
                        trace_hash=result['trace_hash'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        confirm_status=result['confirm_status'],
                        issued_status=result['issued_status'],
                        generate_time=result['generate_time'],
                        user_name=result['user_name']
                    )
                return None
        finally:
            self.pool.put_conn(conn)


    def get_all(self) -> list[Solution]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    Solution(
                        id=row['id'],
                        trace_id=row['trace_id'],
                        raw_json=row['raw_json'],
                        trace_hash=row['trace_hash'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        confirm_status=row['confirm_status'],
                        issued_status=row['issued_status'],
                        generate_time=row['generate_time'],
                        user_name=result['user_name']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)

    def get_by_trace_id_and_version(self, trace_id, version) -> Optional[Solution]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution WHERE trace_id = %s and version = %s", (trace_id, version))
                result = cur.fetchone()
                if result:
                    return Solution(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        raw_json=result['raw_json'],
                        trace_hash=result['trace_hash'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        confirm_status=result['confirm_status'],
                        issued_status=result['issued_status'],
                        generate_time=result['generate_time'],
                        user_name=result['user_name']
                    )
                return None
        finally:
            self.pool.put_conn(conn)

