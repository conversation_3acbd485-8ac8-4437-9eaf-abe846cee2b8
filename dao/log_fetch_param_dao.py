from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool

@dataclass
class LogFetchParam:
    id: Optional[int] = None
    job_trigger_id: Optional[int] = None
    log_type: Optional[str] = None
    system_code: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    start_index: Optional[int] = 0
    page_num: Optional[int] = 10
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = None


class LogFetchParamDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, param: LogFetchParam) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO log_fetch_param (
                    job_trigger_id, log_type, system_code,
                    start_time, end_time, start_index, page_num,
                    create_user, create_time, last_modify_user,
                    last_modify_time, tenant_id, version
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    param.job_trigger_id,
                    param.log_type,
                    param.system_code,
                    param.start_time,
                    param.end_time,
                    param.start_index,
                    param.page_num,
                    param.create_user,
                    param.create_time,
                    param.last_modify_user,
                    param.last_modify_time,
                    param.tenant_id,
                    param.version
                ))
                cur.execute("""
                    SELECT id FROM log_fetch_param
                    WHERE create_time = %s AND job_trigger_id = %s AND log_type = %s
                    ORDER BY id DESC LIMIT 1
                """, (
                param.create_time, param.job_trigger_id, param.log_type))

                last_id = cur.fetchone()['id']
                conn.commit()
                print(f"Last log fetch param ID: {last_id}")
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, param: LogFetchParam, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([param.version + 1, param.version, param.id])

                sql = f"""
                UPDATE log_fetch_param
                SET {set_clause}, last_modify_time=NOW(), version=%s
                WHERE version=%s AND id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, param_id: int) -> Optional[LogFetchParam]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_fetch_param WHERE id = %s", (param_id,))
                result = cur.fetchone()
                if result:
                    return LogFetchParam(
                        id=result['id'],
                        job_trigger_id=result['job_trigger_id'],
                        log_type=result['log_type'],
                        system_code=result['system_code'],
                        start_time=result['start_time'],
                        end_time=result['end_time'],
                        start_index=result['start_index'],
                        page_num=result['page_num'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        tenant_id=result['tenant_id'],
                        version=result['version']
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[LogFetchParam]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_fetch_param ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    LogFetchParam(
                        id=row['id'],
                        job_trigger_id=row['job_trigger_id'],
                        log_type=row['log_type'],
                        system_code=row['system_code'],
                        start_time=row['start_time'],
                        end_time=row['end_time'],
                        start_index=row['start_index'],
                        page_num=row['page_num'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        tenant_id=row['tenant_id'],
                        version=row['version']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)
