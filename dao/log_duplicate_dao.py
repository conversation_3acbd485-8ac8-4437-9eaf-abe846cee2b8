from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool
from utils.logger import logger


@dataclass
class LogDuplicate:
    id: Optional[int] = None
    job_trigger_id: Optional[str] = None
    duplicate_log_id: Optional[int] = None
    trace_id: Optional[str] = None
    request: Optional[str] = None
    request_time: Optional[datetime] = None
    log_type: Optional[str] = None
    sys_code: Optional[str] = None
    count: Optional[int] = None
    comment: Optional[str] = None
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = None


class LogDuplicateDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, log_duplicate: LogDuplicate) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO log_duplicate (
                    job_trigger_id, duplicate_log_id, trace_id, request,
                    request_time, log_type,
                    sys_code, count, comment, create_user,
                    create_time, last_modify_user, last_modify_time,
                    tenant_id, version
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    log_duplicate.job_trigger_id,
                    log_duplicate.duplicate_log_id,
                    log_duplicate.trace_id,
                    log_duplicate.request,
                    log_duplicate.request_time,
                    log_duplicate.log_type,
                    log_duplicate.sys_code,
                    log_duplicate.count,
                    log_duplicate.comment,
                    log_duplicate.create_user,
                    log_duplicate.create_time,
                    log_duplicate.last_modify_user,
                    log_duplicate.last_modify_time,
                    log_duplicate.tenant_id,
                    log_duplicate.version
                ))
                cur.execute("""
                    SELECT id FROM log_duplicate
                    WHERE create_time = %s AND job_trigger_id = %s AND trace_id = %s AND request = %s
                    ORDER BY id DESC LIMIT 1
                """, (log_duplicate.create_time, log_duplicate.job_trigger_id, log_duplicate.trace_id, log_duplicate.request))

                last_id = cur.fetchone()['id']
                conn.commit()
                print(f"Last log duplicate ID: {last_id}")
                return last_id
        except Exception as e:
            conn.rollback()
            logger.exception(f"Create error: {str(e)}")
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, log_duplicate: LogDuplicate, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([log_duplicate.version + 1, log_duplicate.version, log_duplicate.id])

                sql = f"""
                UPDATE log_duplicate
                SET {set_clause}, last_modify_time=NOW(), version=%s
                WHERE version=%s AND id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, log_id: int) -> Optional[LogDuplicate]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_duplicate WHERE id = %s", (log_id,))
                result = cur.fetchone()
                if result:
                    return LogDuplicate(
                        id=result['id'],
                        job_trigger_id=result['job_trigger_id'],
                        duplicate_log_id=result['duplicate_log_id'],
                        trace_id=result['trace_id'],
                        request=result['request'],
                        request_time=result['request_time'],
                        log_type=result['log_type'],
                        sys_code=result['sys_code'],
                        count=result['count'],
                        comment=result['comment'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        tenant_id=result['tenant_id'],
                        version=result['version']
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[LogDuplicate]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_duplicate ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    LogDuplicate(
                        id=row['id'],
                        job_trigger_id=row['job_trigger_id'],
                        duplicate_log_id=row['duplicate_log_id'],
                        trace_id=row['trace_id'],
                        request=row['request'],
                        request_time=row['request_time'],
                        log_type=row['log_type'],
                        sys_code=row['sys_code'],
                        count=row['count'],
                        comment=row['comment'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        tenant_id=row['tenant_id'],
                        version=row['version']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)
