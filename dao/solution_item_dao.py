from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool


@dataclass
class SolutionItem:
    id: Optional[int] = None
    solution_id: Optional[int] = None
    trace_id: Optional[str] = None
    trace_hash: Optional[str] = None
    system_code: Optional[str] = None
    service_code: Optional[str] = None
    problem_desc: Optional[str] = None
    ai_suggestion: Optional[str] = None
    confirm_status: Optional[str] = None
    confirm_by: Optional[str] = None
    confirm_time: Optional[datetime] = None
    system_owner: Optional[str] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = 0
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None


class SolutionItemDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, item: SolutionItem) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO solution_item (
                    solution_id, trace_id, trace_hash,
                    system_code, service_code, problem_desc,
                    ai_suggestion, confirm_status, confirm_by,
                    confirm_time, system_owner, tenant_id,
                    version, create_user, create_time,
                    last_modify_user, last_modify_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    item.solution_id,
                    item.trace_id,
                    item.trace_hash,
                    item.system_code,
                    item.service_code,
                    item.problem_desc,
                    item.ai_suggestion,
                    item.confirm_status,
                    item.confirm_by,
                    item.confirm_time,
                    item.system_owner,
                    item.tenant_id,
                    item.version,
                    item.create_user,
                    item.create_time,
                    item.last_modify_user,
                    item.last_modify_time
                ))

                cur.execute("""
                    SELECT id FROM solution_item
                    WHERE create_time = %s AND trace_id = %s
                    ORDER BY id DESC LIMIT 1
                """, (item.create_time, item.trace_id))
                last_id = cur.fetchone()['id']

                conn.commit()
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, item: SolutionItem, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([item.id])

                sql = f"""
                UPDATE solution_item
                SET {set_clause}, last_modify_time=NOW(), version=version+1
                WHERE id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, item_id: int) -> Optional[SolutionItem]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution_item WHERE id = %s", (item_id,))
                result = cur.fetchone()
                if result:
                    return SolutionItem(
                        id=result['id'],
                        solution_id=result['solution_id'],
                        trace_id=result['trace_id'],
                        trace_hash=result['trace_hash'],
                        system_code=result['system_code'],
                        service_code=result['service_code'],
                        problem_desc=result['problem_desc'],
                        ai_suggestion=result['ai_suggestion'],
                        confirm_status=result['confirm_status'],
                        confirm_by=result['confirm_by'],
                        confirm_time=result['confirm_time'],
                        system_owner=result['system_owner'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time']
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_solution_id(self, solution_id: int) -> list[SolutionItem]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution_item WHERE solution_id = %s", (solution_id,))
                rows = cur.fetchall()
                return [
                    SolutionItem(
                        id=row['id'],
                        solution_id=row['solution_id'],
                        trace_id=row['trace_id'],
                        trace_hash=row['trace_hash'],
                        system_code=row['system_code'],
                        service_code=row['service_code'],
                        problem_desc=row['problem_desc'],
                        ai_suggestion=row['ai_suggestion'],
                        confirm_status=row['confirm_status'],
                        confirm_by=row['confirm_by'],
                        confirm_time=row['confirm_time'],
                        system_owner=row['system_owner'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time']
                    )
                    for row in rows
                ]
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[SolutionItem]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM solution_item ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    SolutionItem(
                        id=row['id'],
                        solution_id=row['solution_id'],
                        trace_id=row['trace_id'],
                        trace_hash=row['trace_hash'],
                        system_code=row['system_code'],
                        service_code=row['service_code'],
                        problem_desc=row['problem_desc'],
                        ai_suggestion=row['ai_suggestion'],
                        confirm_status=row['confirm_status'],
                        confirm_by=row['confirm_by'],
                        confirm_time=row['confirm_time'],
                        system_owner=row['system_owner'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time']
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)
