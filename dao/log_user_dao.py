from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool
from utils.logger import logger


@dataclass
class LogUser:
    id: Optional[int] = None
    trace_id: Optional[str] = None
    status: Optional[str] = None
    user_id: Optional[int] = None
    trace_data: Optional[str] = None
    log_process_id: Optional[int] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = None
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None
    comment: Optional[str] = None  # 新增字段
    solution_id: Optional[int] = None
    trace_hash: Optional[str] = None
    request_time: Optional[datetime] = None


class LogUserDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, process: LogUser) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO log_user (
                    trace_id, status, user_id, trace_data, log_process_id,
                    tenant_id, version, create_user, create_time,
                    last_modify_user, last_modify_time, comment, solution_id, trace_hash, request_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    process.trace_id,
                    process.status,
                    process.user_id,
                    process.trace_data,
                    process.log_process_id,
                    process.tenant_id,
                    process.version,
                    process.create_user,
                    process.create_time,
                    process.last_modify_user,
                    process.last_modify_time,
                    process.comment,
                    process.solution_id,
                    process.trace_hash,
                    process.request_time
                ))
                cur.execute("""
                    SELECT id FROM log_user
                    WHERE create_time = %s AND trace_id = %s AND user_id = %s
                    ORDER BY id DESC LIMIT 1
                """, (
                process.create_time, process.trace_id,
                process.user_id))

                last_id = cur.fetchone()['id']
                conn.commit()
                print(f"Last log process ID: {last_id}")
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, process: LogUser, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([process.id])

                sql = f"""
                UPDATE log_user
                SET {set_clause}, last_modify_time=NOW()
                WHERE id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            logger.exception(e)
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, process_id: int) -> Optional[LogUser]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_user WHERE id = %s", (process_id,))
                result = cur.fetchone()
                if result:
                    return LogUser(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_process_id=result['log_process_id'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        request_time=result.get('request_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_trace_id(self, trace_id: str) -> Optional[LogUser]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_user WHERE trace_id = %s", (trace_id,))
                result = cur.fetchone()
                if result:
                    return LogUser(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_process_id=result['log_process_id'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        request_time=result.get('request_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_trace_id_and_user_id(self, trace_id: str, user_id: int) -> Optional[LogUser]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_user WHERE trace_id = %s and user_id = %s", (trace_id, user_id))
                result = cur.fetchone()
                if result:
                    return LogUser(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_process_id=result['log_process_id'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        request_time=result.get('request_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[LogUser]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_user ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    LogUser(
                        id=row['id'],
                        trace_id=row['trace_id'],
                        status=row['status'],
                        user_id=row['user_id'],
                        trace_data=row['trace_data'],
                        log_process_id=row['log_process_id'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        comment=row.get('comment'),
                        solution_id=row.get('solution_id'),
                        trace_hash=row.get('trace_hash'),
                        request_time=row.get('request_time')
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)
