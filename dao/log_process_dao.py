from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from utils.connection_pool import ConnectionPool
from utils.logger import logger


@dataclass
class LogProcess:
    id: Optional[int] = None
    trace_id: Optional[str] = None
    request: Optional[str] = None
    request_time: Optional[datetime] = None
    sys_code: Optional[str] = None
    count: Optional[int] = None
    log_type: Optional[str] = None
    job_trigger_id: Optional[str] = None
    status: Optional[str] = None
    user_id: Optional[str] = None
    trace_data: Optional[str] = None
    log_data: Optional[str] = None
    tenant_id: Optional[str] = None
    version: Optional[int] = None
    create_user: Optional[str] = "-1"
    create_time: Optional[datetime] = None
    last_modify_user: Optional[str] = "-1"
    last_modify_time: Optional[datetime] = None
    comment: Optional[str] = None  # 新增字段
    solution_id: Optional[int] = None,
    trace_hash: Optional[str] = None,
    source_type: Optional[str] = None,
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    def to_dict(self) -> dict:
        return {
            "id": self.id,
            "trace_id": self.trace_id,
            "request": self.request,
            "request_time": self.request_time,
            "sys_code": self.sys_code,
            "count": self.count,
            "log_type": self.log_type,
            "job_trigger_id": self.job_trigger_id,
            "status": self.status,
            "user_id": self.user_id,
            "trace_data": self.trace_data,
            "log_data": self.log_data,
            "tenant_id": self.tenant_id,
            "version": self.version,
            "create_user": self.create_user,
            "create_time": self.create_time,
            "last_modify_time": self.last_modify_time,
            "comment": self.comment,
            "solution_id": self.solution_id,
            "trace_hash": self.trace_hash,
            "source_type": self.source_type,
            "start_time": self.start_time,
            "end_time": self.end_time
        }


class LogProcessDAO:
    def __init__(self):
        self.pool = ConnectionPool()

    def create(self, process: LogProcess) -> int:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                sql = """
                INSERT INTO log_process (
                    trace_id, request, sys_code, count, log_type,
                    job_trigger_id, status, user_id, trace_data, log_data,
                    tenant_id, version, create_user, create_time,
                    last_modify_user, last_modify_time, request_time, comment, solution_id, trace_hash, source_type, start_time, end_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cur.execute(sql, (
                    process.trace_id,
                    process.request,
                    process.sys_code,
                    process.count,
                    process.log_type,
                    process.job_trigger_id,
                    process.status,
                    process.user_id,
                    process.trace_data,
                    process.log_data,
                    process.tenant_id,
                    process.version,
                    process.create_user,
                    process.create_time,
                    process.last_modify_user,
                    process.last_modify_time,
                    process.request_time,
                    process.comment,
                    process.solution_id,
                    process.trace_hash,
                    process.source_type,
                    process.start_time,
                    process.end_time
                ))
                cur.execute("""
                    SELECT id FROM log_process
                    WHERE create_time = %s AND trace_id = %s
                    ORDER BY id DESC LIMIT 1
                """, (process.create_time, process.trace_id))

                last_id = cur.fetchone()['id']
                conn.commit()
                print(f"Last log process ID: {last_id}")
                return last_id
        except Exception as e:
            conn.rollback()
            raise RuntimeError(f"Create error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def update(self, process: LogProcess, fields: dict) -> bool:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                set_clause = ", ".join([f"{k}=%s" for k in fields])
                values = list(fields.values())
                values.extend([process.id])

                sql = f"""
                UPDATE log_process
                SET {set_clause}, last_modify_time=NOW()
                WHERE id=%s
                """
                affected = cur.execute(sql, values)
                conn.commit()
                return affected > 0
        except Exception as e:
            conn.rollback()
            logger.exception(e)
            raise RuntimeError(f"Update error: {str(e)}")
        finally:
            self.pool.put_conn(conn)

    def get_by_id(self, process_id: int) -> Optional[LogProcess]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_process WHERE id = %s", (process_id,))
                result = cur.fetchone()
                if result:
                    return LogProcess(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        request=result['request'],
                        sys_code=result['sys_code'],
                        count=result['count'],
                        log_type=result['log_type'],
                        job_trigger_id=result['job_trigger_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_data=result['log_data'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        request_time=result['request_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        source_type=result.get('source_type'),
                        start_time=result.get('start_time'),
                        end_time=result.get('end_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_trace_id(self, trace_id: str) -> Optional[LogProcess]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_process WHERE trace_id = %s", (trace_id,))
                result = cur.fetchone()
                if result:
                    return LogProcess(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        request=result['request'],
                        sys_code=result['sys_code'],
                        count=result['count'],
                        log_type=result['log_type'],
                        job_trigger_id=result['job_trigger_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_data=result['log_data'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        request_time=result['request_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        source_type=result.get('source_type'),
                        start_time=result.get('start_time'),
                        end_time=result.get('end_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_trace_hash(self, trace_hash: str) -> Optional[LogProcess]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_process WHERE trace_hash = %s ORDER BY create_time DESC;", (trace_hash,))
                result = cur.fetchone()
                if result:
                    return LogProcess(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        request=result['request'],
                        sys_code=result['sys_code'],
                        count=result['count'],
                        log_type=result['log_type'],
                        job_trigger_id=result['job_trigger_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_data=result['log_data'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        request_time=result['request_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        source_type=result.get('source_type'),
                        start_time=result.get('start_time'),
                        end_time=result.get('end_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_by_sys_code_and_request(self, sys_code: str, request: str) -> Optional[LogProcess]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_process WHERE sys_code = %s and request = %s", (sys_code, request))
                result = cur.fetchone()
                if result:
                    return LogProcess(
                        id=result['id'],
                        trace_id=result['trace_id'],
                        request=result['request'],
                        sys_code=result['sys_code'],
                        count=result['count'],
                        log_type=result['log_type'],
                        job_trigger_id=result['job_trigger_id'],
                        status=result['status'],
                        user_id=result['user_id'],
                        trace_data=result['trace_data'],
                        log_data=result['log_data'],
                        tenant_id=result['tenant_id'],
                        version=result['version'],
                        create_user=result['create_user'],
                        create_time=result['create_time'],
                        last_modify_user=result['last_modify_user'],
                        last_modify_time=result['last_modify_time'],
                        request_time=result['request_time'],
                        comment=result.get('comment'),
                        solution_id=result.get('solution_id'),
                        trace_hash=result.get('trace_hash'),
                        source_type=result.get('source_type'),
                        start_time=result.get('start_time'),
                        end_time=result.get('end_time')
                    )
                return None
        finally:
            self.pool.put_conn(conn)

    def get_all(self) -> list[LogProcess]:
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT * FROM log_process ORDER BY create_time DESC;")
                rows = cur.fetchall()
                return [
                    LogProcess(
                        id=row['id'],
                        trace_id=row['trace_id'],
                        request=row['request'],
                        sys_code=row['sys_code'],
                        count=row['count'],
                        log_type=row['log_type'],
                        job_trigger_id=row['job_trigger_id'],
                        status=row['status'],
                        user_id=row['user_id'],
                        trace_data=row['trace_data'],
                        log_data=row['log_data'],
                        tenant_id=row['tenant_id'],
                        version=row['version'],
                        create_user=row['create_user'],
                        create_time=row['create_time'],
                        last_modify_user=row['last_modify_user'],
                        last_modify_time=row['last_modify_time'],
                        request_time=row['request_time'],
                        comment=row.get('comment'),
                        solution_id=row.get('solution_id'),
                        trace_hash=row.get('trace_hash'),
                        source_type=row.get('source_type'),
                        start_time=row.get('start_time'),
                        end_time=row.get('end_time')
                    )
                    for row in rows
                ]
        except Exception as e:
            raise Exception(f"Database error: {e}")
        finally:
            self.pool.put_conn(conn)

    def delete(self, id: int) -> bool:
        """根据 ID 删除记录"""
        conn = self.pool.get_conn()
        try:
            with conn.cursor() as cur:
                cur.execute(
                    "DELETE FROM log_process WHERE id = %s;",
                    (id,)
                )
                conn.commit()
                return cur.rowcount > 0
        except Exception as e:
            raise Exception(f"Database error during delete: {e}")
        finally:
            self.pool.put_conn(conn)

    def query_log_process(self, page_num: int, page_size: int, custom_fields: dict = None):
        """根因分析看板页查询"""
        offset = (page_num - 1) * page_size
        conn = self.pool.get_conn()
        values = []
        custom_clause = " AND ".join([f"{k}" for k in custom_fields]) if custom_fields else None
        if custom_clause:
            values.extend(custom_fields.values())
        values.append(page_size)
        values.append(offset)
        try:
            with conn.cursor() as cur:
                sql = f"""
                    SELECT 
                        lp.*,
                        s.id AS solution_id,
                        s.raw_json AS solution_raw_json,
                        s.trace_id AS solution_trace_id,
                        s.trace_hash AS solution_trace_hash,
                        s.status AS solution_status,
                        s.confirm_status AS solution_confirm_status,
                        s.issued_status AS solution_issued_status,
                        s.generate_time AS generate_time,
                        s.user_name AS user_name,                   
                        ls.stage_name AS current_stage,
                        lp.request AS error_info
                    FROM 
                        log_process lp
                    LEFT JOIN 
                        solution s ON lp.trace_id = s.trace_id and s.version = lp.version
                    LEFT JOIN (
                        SELECT 
                            trace_id,
                            version,
                            stage_name,
                            ROW_NUMBER() OVER (PARTITION BY trace_id, version ORDER BY last_modify_time DESC) AS rk
                        FROM log_stage
                    ) ls ON lp.trace_id = ls.trace_id AND lp.version = ls.version AND ls.rk = 1
                    WHERE
                        1=1
                        {f'AND {custom_clause}' if custom_clause is not None else ''}
                    ORDER BY
                        lp.last_modify_time DESC
                    LIMIT %s OFFSET %s ;
                """
                logger.info(f"sql: {sql}")
                logger.info(f"values: {values}")
                cur.execute(sql, values)

                rows = cur.fetchall()
                return rows
                # columns = [desc[0] for desc in cur.description]
                # data = [dict(zip(columns, row)) for row in rows]
                # return data

        except Exception as e:
            raise Exception(f"Database error during query: {e}")
        finally:
            self.pool.put_conn(conn)


